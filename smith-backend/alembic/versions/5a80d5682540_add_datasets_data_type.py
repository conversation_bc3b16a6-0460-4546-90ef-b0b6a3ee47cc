"""Add datasets.data_type

Revision ID: 5a80d5682540
Revises: 48a68cbc5834
Create Date: 2023-07-06 10:07:14.597252

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "5a80d5682540"
down_revision = "48a68cbc5834"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "dataset",
        sa.Column("data_type", sa.String(), server_default="kv", nullable=False),
    )
    op.create_index(
        "idx_dataset_tenant_id_data_type_created_at",
        "dataset",
        ["tenant_id", "data_type", sa.text("created_at DESC")],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_dataset_tenant_id_data_type_created_at", table_name="dataset")
    op.drop_column("dataset", "data_type")
    # ### end Alembic commands ###
