"""20230815 Optimise indexes on runs table

Revision ID: 603fe644a19d
Revises: 01a670702169
Create Date: 2023-08-15 14:40:06.051008

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "603fe644a19d"
down_revision = "01a670702169"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        # Replace the existing FTS index with a smaller one
        op.execute(
            """
create index concurrently ix_runs_fts_root on runs using gin (
(to_tsvector('english', coalesce(error, ''))
|| jsonb_to_tsvector('english', coalesce(inputs, '{}'), '["string"]')
|| jsonb_to_tsvector('english', coalesce(outputs, '{}'), '["string"]'))
) where (execution_order = 1);
"""
        )
        op.execute("drop index concurrently idx_fts;")

        # Add index on tags
        op.execute(
            """create index concurrently ix_runs_tags on runs using gin (tags) where (execution_order = 1);"""
        )

        # Drop index on manifest_id
        op.execute(
            "drop index concurrently idx_runs_mainfest_id_execution_order_start_time;"
        )

        # Replace index on reference_example_id
        op.execute(
            """
create index concurrently runs_reference_example_id_start_time_id_root_idx on runs(reference_example_id, start_time desc, id) where execution_order = 1"""
        )
        op.execute(
            "drop index concurrently idx_runs_reference_example_id_execution_order_start_time;"
        )

        # Replace index on parent_run_id, hash index is smaller,
        # and we don't need the extra ops of btree
        op.execute("create index concurrently on runs using hash (parent_run_id);")
        op.execute("drop index concurrently ix_runs_parent_run_id;")

        # Add BRIN index on start_time, very cheap index that can accelerate
        # queries on start_time not covered by other indexes
        op.execute(
            "create index concurrently ix_runs_brin_start_time on runs using brin (start_time);"
        )

    # Drop the old FTS computed column
    op.execute("alter table runs drop column fts_searchable;")


def downgrade() -> None:
    raise NotImplementedError()
