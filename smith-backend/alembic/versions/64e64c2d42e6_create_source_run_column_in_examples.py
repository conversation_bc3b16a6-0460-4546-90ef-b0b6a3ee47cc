"""create source run column in examples

Revision ID: 64e64c2d42e6
Revises: 94540fc6ab5c
Create Date: 2023-10-19 11:32:51.337264

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "64e64c2d42e6"
down_revision = "94540fc6ab5c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE examples
        ADD COLUMN source_run_id uuid;
        """
    )
    with op.get_context().autocommit_block():
        op.execute(
            """
            CREATE INDEX CONCURRENTLY ix_examples_source_run_id ON examples(source_run_id);
            """
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """
            DROP INDEX CONCURRENTLY IF EXISTS ix_examples_source_run_id;
            """
        )
    op.execute(
        """
        ALTER TABLE examples
        DROP COLUMN if exists source_run_id;
        """
    )
