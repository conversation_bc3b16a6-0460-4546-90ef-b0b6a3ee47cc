"""hub comments

Revision ID: 5bed6a26f073
Revises: 182402d24909
Create Date: 2023-09-22 11:57:27.888262

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "5bed6a26f073"
down_revision = "182402d24909"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """CREATE TABLE hub_comments(
        id uuid not null primary key default gen_random_uuid(),
        parent_id uuid references hub_comments(id) on delete cascade,
        comment_by uuid not null references identities(id) on delete set null,
        comment_on uuid not null references hub_repos(id) on delete cascade,
        content text not null,
        created_at timestamp default current_timestamp,
        updated_at timestamp default current_timestamp
    )"""
    )
    op.execute(
        """
        CREATE TABLE hub_comment_likes(
            id uuid not null primary key default gen_random_uuid(),
            comment_id uuid not null references hub_comments(id) on delete cascade,
            liked_by uuid not null references identities(id) on delete cascade,
            created_at timestamp default current_timestamp,
            CONSTRAINT uq_hub_comment_likes_comment_id_liked_by UNIQUE (comment_id, liked_by)
        )
        """
    )


def downgrade() -> None:
    op.execute("""drop table if exists hub_comment_likes;""")
    op.execute("""drop table if exists hub_comments;""")
