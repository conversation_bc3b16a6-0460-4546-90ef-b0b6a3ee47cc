"""add tracer_session.default_dataset_id, add on delete to foreign key constraints

Revision ID: 66a242db79eb
Revises: b8f2dec24d22
Create Date: 2023-06-17 10:25:55.982685

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "66a242db79eb"
down_revision = "b8f2dec24d22"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("api_keys_tenant_id_fkey", "api_keys", type_="foreignkey")
    op.create_foreign_key(
        None, "api_keys", "tenants", ["tenant_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("dataset_tenant_id_fkey", "dataset", type_="foreignkey")
    op.create_foreign_key(
        None, "dataset", "tenants", ["tenant_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("examples_dataset_id_fkey", "examples", type_="foreignkey")
    op.create_foreign_key(
        None, "examples", "dataset", ["dataset_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("feedback_run_id_fkey", "feedback", type_="foreignkey")
    op.create_foreign_key(
        None, "feedback", "runs", ["run_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("identities_tenant_id_fkey", "identities", type_="foreignkey")
    op.create_foreign_key(
        None, "identities", "tenants", ["tenant_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("runs_session_id_fkey", "runs", type_="foreignkey")
    op.drop_constraint("runs_reference_example_id_fkey", "runs", type_="foreignkey")
    op.drop_constraint("runs_parent_run_id_fkey", "runs", type_="foreignkey")
    op.create_foreign_key(
        None, "runs", "runs", ["parent_run_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "runs", "tracer_session", ["session_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        None, "runs", "examples", ["reference_example_id"], ["id"], ondelete="SET NULL"
    )
    op.drop_constraint("share_keys_run_id_fkey", "share_keys", type_="foreignkey")
    op.create_foreign_key(
        None, "share_keys", "runs", ["run_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint("tasks_tenant_id_fkey", "tasks", type_="foreignkey")
    op.create_foreign_key(
        None, "tasks", "tenants", ["tenant_id"], ["id"], ondelete="CASCADE"
    )
    op.add_column(
        "tracer_session", sa.Column("default_dataset_id", sa.UUID(), nullable=True)
    )
    op.create_foreign_key(
        None,
        "tracer_session",
        "dataset",
        ["default_dataset_id"],
        ["id"],
        ondelete="SET NULL",
    )
    op.drop_constraint(
        "tracer_session_tenant_id_fkey", "tracer_session", type_="foreignkey"
    )
    op.create_foreign_key(
        None, "tracer_session", "tenants", ["tenant_id"], ["id"], ondelete="CASCADE"
    )
    op.drop_constraint(
        "tracer_session_stats_tracer_session_id_fkey",
        "tracer_session_stats",
        type_="foreignkey",
    )
    op.create_foreign_key(
        None,
        "tracer_session_stats",
        "tracer_session",
        ["tracer_session_id"],
        ["id"],
        ondelete="CASCADE",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "tracer_session_stats", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "tracer_session_stats_tracer_session_id_fkey",
        "tracer_session_stats",
        "tracer_session",
        ["tracer_session_id"],
        ["id"],
    )
    op.drop_constraint(None, "tracer_session", type_="foreignkey")  # type: ignore
    op.drop_constraint(None, "tracer_session", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "tracer_session_tenant_id_fkey",
        "tracer_session",
        "tenants",
        ["tenant_id"],
        ["id"],
    )
    op.drop_column("tracer_session", "default_dataset_id")
    op.drop_constraint(None, "tasks", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "tasks_tenant_id_fkey", "tasks", "tenants", ["tenant_id"], ["id"]
    )
    op.drop_constraint(None, "share_keys", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "share_keys_run_id_fkey", "share_keys", "runs", ["run_id"], ["id"]
    )
    op.drop_constraint(None, "runs", type_="foreignkey")  # type: ignore
    op.drop_constraint(None, "runs", type_="foreignkey")  # type: ignore
    op.drop_constraint(None, "runs", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "runs_parent_run_id_fkey", "runs", "runs", ["parent_run_id"], ["id"]
    )
    op.create_foreign_key(
        "runs_reference_example_id_fkey",
        "runs",
        "examples",
        ["reference_example_id"],
        ["id"],
    )
    op.create_foreign_key(
        "runs_session_id_fkey", "runs", "tracer_session", ["session_id"], ["id"]
    )
    op.drop_constraint(None, "identities", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "identities_tenant_id_fkey", "identities", "tenants", ["tenant_id"], ["id"]
    )
    op.drop_constraint(None, "feedback", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "feedback_run_id_fkey", "feedback", "runs", ["run_id"], ["id"]
    )
    op.drop_constraint(None, "examples", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "examples_dataset_id_fkey", "examples", "dataset", ["dataset_id"], ["id"]
    )
    op.drop_constraint(None, "dataset", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "dataset_tenant_id_fkey", "dataset", "tenants", ["tenant_id"], ["id"]
    )
    op.drop_constraint(None, "api_keys", type_="foreignkey")  # type: ignore
    op.create_foreign_key(
        "api_keys_tenant_id_fkey", "api_keys", "tenants", ["tenant_id"], ["id"]
    )
    # ### end Alembic commands ###
