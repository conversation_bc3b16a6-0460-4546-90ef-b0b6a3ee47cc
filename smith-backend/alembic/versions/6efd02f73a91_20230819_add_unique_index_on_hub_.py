"""20230819 Add unique index on hub_commits parent_id

Revision ID: 6efd02f73a91
Revises: 1210f53044b9
Create Date: 2023-08-19 13:57:25.555695

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6efd02f73a91"
down_revision = "1210f53044b9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # We cannot use a unique constraint here Postgres < 15 does not support
    # `unique nulls not distinct`
    with op.get_context().autocommit_block():
        op.execute(
            """create unique index concurrently uq_hub_commits_repo_parent on hub_commits (repo_id, parent_id) where parent_id is not null"""
        )
        op.execute(
            """create unique index concurrently uq_hub_commits_repo_parent_null on hub_commits (repo_id) where parent_id is null"""
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("""drop index concurrently uq_hub_commits_repo_parent""")
        op.execute("""drop index concurrently uq_hub_commits_repo_parent_null""")
