"""empty message

Revision ID: 6f725a99336b
Revises: fc7ce2f79d57
Create Date: 2024-01-10 17:40:17.277589

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6f725a99336b"
down_revision = "fc7ce2f79d57"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            "ALTER TABLE annotation_queue_runs_archive DROP CONSTRAINT IF EXISTS annotation_queue_runs_archive_run_id_fkey"
        )
        op.execute(
            "ALTER TABLE annotation_queue_runs DROP CONSTRAINT IF EXISTS annotation_queue_runs_run_id_fkey"
        )
        op.execute(
            "ALTER TABLE feedback DROP CONSTRAINT IF EXISTS feedback_run_id_fkey"
        )
        op.execute(
            "ALTER TABLE run_blobs DROP CONSTRAINT IF EXISTS run_blobs_run_id_fkey"
        )
        op.execute(
            "ALTER TABLE share_keys DROP CONSTRAINT IF EXISTS share_keys_run_id_fkey"
        )


def downgrade() -> None:
    pass
