"""20230723 Update tenants and identities

Revision ID: 7fc2abf70a73
Revises: 5384a4a334c1
Create Date: 2023-07-26 11:56:01.488974

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "7fc2abf70a73"
down_revision = "5384a4a334c1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "identities",
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False
        ),
    )
    op.add_column(
        "tenants",
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False
        ),
    )
    op.add_column(
        "tenants",
        sa.Column("config", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )
    op.execute(
        """UPDATE tenants
        SET config = '{"is_personal": true, "max_identities": 1}'
        WHERE config IS NULL"""
    )
    op.alter_column("tenants", "config", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tenants", "config")
    op.drop_column("tenants", "created_at")
    op.drop_column("identities", "created_at")
    # ### end Alembic commands ###
