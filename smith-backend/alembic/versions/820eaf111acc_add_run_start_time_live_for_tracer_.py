"""add run start time live for tracer session

Revision ID: 820eaf111acc
Revises: 82fd3bcee7aa
Create Date: 2023-12-04 12:22:31.866564

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "820eaf111acc"
down_revision = "82fd3bcee7aa"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
    ALTER TABLE tracer_session
    ADD COLUMN last_run_start_time_live timestamp;
    """
    )

    op.execute(
        """
    UPDATE tracer_session
    SET last_run_start_time_live = COALESCE(tss.last_run_start_time, ts.start_time)
    FROM tracer_session ts
    LEFT JOIN  tracer_session_stats tss ON ts.id = tss.tracer_session_id
    WHERE tracer_session.id = ts.id
    """
    )


def downgrade() -> None:
    # Remove the new column
    op.execute(
        """
    ALTER TABLE tracer_session
    DROP COLUMN last_run_start_time_live;
    """
    )
