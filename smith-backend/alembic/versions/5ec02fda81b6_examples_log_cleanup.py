"""examples log cleanup

Revision ID: 5ec02fda81b6
Revises: d2a99d8e8b46
Create Date: 2024-01-25 17:32:33.779568

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "5ec02fda81b6"
down_revision = "d2a99d8e8b46"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            "create index concurrently if not exists ix_examples_log_dataset_id on examples_log(dataset_id);"
        )
    op.execute("drop table examples cascade;")


def downgrade() -> None:
    pass
