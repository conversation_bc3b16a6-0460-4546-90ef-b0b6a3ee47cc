"""api-keys-default-create-date

Revision ID: 82fd3bcee7aa
Revises: 3e184ad2deaf
Create Date: 2023-12-04 16:13:15.905618

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "82fd3bcee7aa"
down_revision = "3e184ad2deaf"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        alter table api_keys 
        alter column created_at set default now();
        """
    )


def downgrade() -> None:
    op.execute(
        """
        alter table api_keys 
        alter column created_at drop default;
        """
    )
