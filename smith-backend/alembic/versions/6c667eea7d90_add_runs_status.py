"""Add runs.status

Revision ID: 6c667eea7d90
Revises: 201cb24f11ae
Create Date: 2023-07-14 13:37:57.672085

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "6c667eea7d90"
down_revision = "201cb24f11ae"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "runs",
        sa.Column(
            "status",
            sa.String(),
            sa.Computed(
                "case when error is not null then 'error' when end_time is not null then 'success' else 'pending' end",
            ),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("runs", "status")
    # ### end Alembic commands ###
