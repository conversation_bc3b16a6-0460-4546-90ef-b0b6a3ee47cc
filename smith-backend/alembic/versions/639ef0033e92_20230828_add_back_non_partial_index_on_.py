"""20230828 Add back non-partial index on runs.session_id

Revision ID: 639ef0033e92
Revises: 07742cabc7e8
Create Date: 2023-08-28 19:27:56.666777

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "639ef0033e92"
down_revision = "07742cabc7e8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """create index concurrently runs_session_id_start_time_idx on runs (session_id, start_time, id);"""
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("""drop index concurrently runs_session_id_start_time_idx;""")
