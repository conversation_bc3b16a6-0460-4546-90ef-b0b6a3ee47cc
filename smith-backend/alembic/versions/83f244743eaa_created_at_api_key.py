"""created at API Key

Revision ID: 83f244743eaa
Revises: 36b29128d4ef
Create Date: 2023-05-15 15:31:41.126002

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "83f244743eaa"
down_revision = "36b29128d4ef"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("api_keys", sa.Column("created_at", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("api_keys", "created_at")
    # ### end Alembic commands ###
