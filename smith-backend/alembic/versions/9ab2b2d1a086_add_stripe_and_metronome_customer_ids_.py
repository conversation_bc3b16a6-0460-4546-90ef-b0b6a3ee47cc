"""add stripe and metronome customer ids to orgs

Revision ID: 9ab2b2d1a086
Revises: bb2a0fcb58d5
Create Date: 2024-02-12 14:19:47.324927

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9ab2b2d1a086"
down_revision = "bb2a0fcb58d5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE organizations
        ADD COLUMN stripe_customer_id VARCHAR(255),
        ADD COLUMN metronome_customer_id VARCHAR(255);"""
    )
    op.execute(
        """
        ALTER TABLE organizations
        ADD CONSTRAINT orgs_stripe_customer_id_key UNIQUE (stripe_customer_id);
        """
    )
    op.execute(
        """
        ALTER TABLE organizations
        ADD CONSTRAINT orgs_metronome_customer_id_key UNIQUE (metronome_customer_id);
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE organizations
        DROP COLUMN stripe_customer_id,
        DROP COLUMN metronome_customer_id;
        """
    )
