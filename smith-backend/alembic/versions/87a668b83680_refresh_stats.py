"""refresh stats

Revision ID: 87a668b83680
Revises: 347a4625b46f
Create Date: 2023-06-19 18:20:02.310067

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "87a668b83680"
down_revision = "347a4625b46f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        sa.text(
            """
with

run_stats as (
    select
        session_id as tracer_session_id,
        count(*) as run_count,
        max(start_time) as last_run_start_time,
        sum(cast(extra->>'total_tokens' as int)) as total_tokens,
        sum(cast(extra->>'prompt_tokens' as int)) as prompt_tokens,
        sum(cast(extra->>'completion_tokens' as int)) as completion_tokens,
        percentile_cont(0.5) within group (order by end_time - start_time) as latency_p50,
        percentile_cont(0.99) within group (order by end_time - start_time) as latency_p99,
        array_agg(distinct dataset_id) filter (where dataset_id is not null)
            as reference_dataset_ids
    from runs
    left join examples on runs.reference_example_id = examples.id
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1
),

run_facets as ((
    select
        session_id as tracer_session_id,
        'name' as key,
        name as value,
        format('eq(name, "%s")', name) as query,
        count(*) as n
    from runs
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1, 2, 3
    order by n desc
    limit 10
    
    ) union all (
    
    select
        session_id as tracer_session_id,
        'run_type' as key,
        run_type as value,
        format('eq(run_type, "%s")', run_type) as query,
        count(*) as n
    from runs
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1, 2, 3
    order by n desc
    limit 10
    
    ) union all (
    
    select
        session_id as tracer_session_id,
        'feedback_key' as key,
        feedback.key as value,
        format('eq(feedback_key, "%s")', feedback.key) as query,
        count(distinct runs.id) as n
    from runs
    inner join feedback on runs.id = feedback.run_id
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1, 2, 3
    order by n desc
    limit 10
    
    ) union all (
    
    select
        session_id as tracer_session_id,
        'feedback_source' as key,
        feedback.feedback_source->>'type' as value,
        format('eq(feedback_source, "%s")', feedback.feedback_source->>'type') as query,
        count(distinct runs.id) as n
    from runs
    inner join feedback on runs.id = feedback.run_id
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1, 2, 3
    order by n desc
    limit 10
    
    ) union all (
    
    select
        session_id as tracer_session_id,
        'tags' as key,
        tag as value,
        format('has(tags, "%s")', tag) as query,
        count(*) as n
    from runs, unnest(tags) as tag
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1, 2, 3
    order by n desc
    limit 10
)),

run_facets_grouped as (
    select
        tracer_session_id,
        jsonb_agg(jsonb_build_object(
            'key', key, 
            'value', value,
            'query', query,
            'n', n
        )) as run_facets
    from run_facets
    group by 1
),

feedback_stats as (
    with stats as (
        select
            session_id as tracer_session_id,
            key,
            jsonb_build_object(
                'n', count(*),
                'mode', mode() within group (order by score),
                'avg', avg(score)
            ) as aggs
        from runs
        inner join feedback on runs.id = feedback.run_id
        where session_id = any(select distinct id from tracer_session)
        group by 1, 2
    )

    select
        tracer_session_id,
        jsonb_object_agg(key, aggs) as feedback_stats
    from stats
    group by 1
),

all_stats as (
    select run_stats.*, feedback_stats, run_facets
    from run_stats
    full outer join feedback_stats fs
        on run_stats.tracer_session_id = fs.tracer_session_id
    full outer join run_facets_grouped
        on run_stats.tracer_session_id = run_facets_grouped.tracer_session_id
)

insert into tracer_session_stats (
    tracer_session_id,
    run_count,
    last_run_start_time,
    total_tokens,
    prompt_tokens,
    completion_tokens,
    latency_p50,
    latency_p99,
    reference_dataset_ids,
    feedback_stats,
    run_facets
)
select * from all_stats
on conflict (tracer_session_id) do update
set run_count = excluded.run_count,
    last_run_start_time = excluded.last_run_start_time,
    total_tokens = excluded.total_tokens,
    prompt_tokens = excluded.prompt_tokens,
    completion_tokens = excluded.completion_tokens,
    latency_p50 = excluded.latency_p50,
    latency_p99 = excluded.latency_p99,
    reference_dataset_ids = excluded.reference_dataset_ids,
    feedback_stats = excluded.feedback_stats,
    run_facets = excluded.run_facets
where tracer_session_stats.tracer_session_id = any(select distinct id from tracer_session)
returning tracer_session_id
"""
        )
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
