"""20230823 Populate new token count columns

Revision ID: 5f4d861397a6
Revises: 06afc667143d
Create Date: 2023-08-23 16:46:20.425086

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "5f4d861397a6"
down_revision = "06afc667143d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        update runs
        set prompt_tokens = cast(extra->>'prompt_tokens' as int),
            completion_tokens = cast(extra->>'completion_tokens' as int),
            total_tokens = cast(extra->>'total_tokens' as int),
            extra = extra - 'prompt_tokens' - 'completion_tokens' - 'total_tokens'
        where status = 'success' and extra ? 'prompt_tokens' and extra ? 'completion_tokens' and extra ? 'total_tokens';
        """
    )


def downgrade() -> None:
    pass
