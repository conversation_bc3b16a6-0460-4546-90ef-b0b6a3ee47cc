"""drop source run fkey constraint on examples

Revision ID: 8ee1f940c25d
Revises: 6179b3bb5180
Create Date: 2024-01-16 16:48:38.547186

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "8ee1f940c25d"
down_revision = "6179b3bb5180"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            "ALTER TABLE examples DROP CONSTRAINT IF EXISTS examples_source_run_id_fkey"
        )


def downgrade() -> None:
    pass
