"""hub repo archived

Revision ID: 7e1059396af8
Revises: c31481055945
Create Date: 2023-09-13 17:04:07.920661

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "7e1059396af8"
down_revision = "c31481055945"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        "ALTER TABLE hub_repos ADD COLUMN is_archived BOOLEAN NOT NULL DEFAULT FALSE"
    )


def downgrade() -> None:
    op.execute("ALTER TABLE hub_repos DROP COLUMN IF EXISTS is_archived")
