"""create last updated trigger for annotation queues

Revision ID: 94540fc6ab5c
Revises: a899ad99af82
Create Date: 2023-10-12 15:16:59.886400

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "94540fc6ab5c"
down_revision = "a899ad99af82"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DELETE FROM annotation_queue_runs
        WHERE added_at IS NULL;
        """
    )
    op.execute(
        """
        DELETE FROM annotation_queues
        WHERE (name, tenant_id) IN (
            SELECT name, tenant_id
            FROM annotation_queues
            GROUP BY name, tenant_id
            HAVING COUNT(*) > 1
        );
        """
    )
    op.execute(
        """
        ALTER TABLE annotation_queue_runs
        ALTER COLUMN added_at SET NOT NULL;
        """
    )
    op.execute(
        """
        ALTER TABLE annotation_queues
        ADD COLUMN updated_at timestamp NOT NULL DEFAULT NOW();
        """
    )
    op.execute(
        """
        ALTER TABLE annotation_queues
        ADD CONSTRAINT annotation_queues_name_tenant_id_key UNIQUE (name, tenant_id);
        """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION update_updated_at() RETURNS TRIGGER AS $$
        BEGIN
            IF TG_OP = 'DELETE' THEN
                UPDATE annotation_queues
                SET updated_at = NOW()
                WHERE id = OLD.queue_id;
                RETURN OLD;
            ELSE
                UPDATE annotation_queues
                SET updated_at = NOW()
                WHERE id = NEW.queue_id;
                RETURN NEW;
            END IF;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
    op.execute(
        """
        CREATE TRIGGER update_updated_at_trigger
        AFTER INSERT OR DELETE OR UPDATE OF last_reviewed_time, added_at
        ON annotation_queue_runs
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at();
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP TRIGGER if exists update_updated_at_trigger ON annotation_queue_runs;
        """
    )
    op.execute(
        """
        DROP FUNCTION if exists update_updated_at();
        """
    )
    op.execute(
        """
        ALTER TABLE if exists annotation_queues
        DROP CONSTRAINT if exists annotation_queues_name_tenant_id_key;
        """
    )
    op.execute(
        """
        ALTER TABLE if exists annotation_queues
        DROP COLUMN if exists updated_at;
        """
    )
    op.execute(
        """
        ALTER TABLE if exists annotation_queue_runs
        ALTER COLUMN added_at DROP NOT NULL;
        """
    )
