"""add is_enabled to tenant table

Revision ID: 9b24e3214bc9
Revises: d2a99d8e8b46
Create Date: 2024-01-27 19:28:16.798592

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9b24e3214bc9"
down_revision = "d2a99d8e8b46"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE tenants
        ADD COLUMN is_enabled BOOLEAN NOT NULL DEFAULT TRUE;
        """
    )


def downgrade() -> None:
    pass
