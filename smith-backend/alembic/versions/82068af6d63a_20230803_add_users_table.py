"""20230803 Add users table

Revision ID: 82068af6d63a
Revises: ff90df680a7d
Create Date: 2023-08-03 09:11:49.657168

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "82068af6d63a"
down_revision = "ff90df680a7d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE TABLE users (
            id UUID PRIMARY KEY,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            email TEXT NOT NULL,
            full_name TEXT,
            avatar_url TEXT
        );
        """
    )


def downgrade() -> None:
    pass
