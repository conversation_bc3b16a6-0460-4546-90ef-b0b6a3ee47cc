"""tenant handle unique

Revision ID: 62e829b59d35
Revises: 285cae44c5a8
Create Date: 2023-08-09 18:25:27.534529

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "62e829b59d35"
down_revision = "285cae44c5a8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """create unique index concurrently uq_tenants_handle on tenants (tenant_handle);"""
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("""drop index concurrently uq_tenants_handle;""")
