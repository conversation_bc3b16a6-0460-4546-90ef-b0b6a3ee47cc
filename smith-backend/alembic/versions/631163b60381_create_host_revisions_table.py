"""create host_revisions table

Revision ID: 631163b60381
Revises: 48d808ec4445
Create Date: 2023-11-07 02:21:25.729366

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "631163b60381"
down_revision = "48d808ec4445"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE TABLE host_revisions (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            project_id uuid NOT NULL REFERENCES host_projects(id) on delete cascade,
            created_at timestamp WITH TIME ZONE NOT NULL DEFAULT now(),
            updated_at timestamp WITH TIME ZONE NOT NULL DEFAULT now(),
            repo_path varchar(255) NOT NULL default '.',
            repo_commit varchar(255) NOT NULL default 'master',
            -- to store knative identifiers once created. Null until a knative revision is created
           knative jsonb
        );"""
    )
    op.execute("ALTER TABLE host_projects DROP COLUMN IF EXISTS repo_path;")
    op.execute(
        "CREATE INDEX host_revisions_project_id_idx ON host_revisions(project_id, created_at DESC);"
    )


def downgrade() -> None:
    op.execute("DROP INDEX IF EXISTS host_revisions_project_id_idx;")
    op.execute("DROP TABLE IF EXISTS host_revisions;")
