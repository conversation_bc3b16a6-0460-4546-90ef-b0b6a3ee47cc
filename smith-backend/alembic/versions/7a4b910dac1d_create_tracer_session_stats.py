"""create tracer_session_stats

Revision ID: 7a4b910dac1d
Revises: 12b81975950a
Create Date: 2023-06-16 16:44:15.192997

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "7a4b910dac1d"
down_revision = "12b81975950a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tracer_session_stats",
        sa.<PERSON>umn("tracer_session_id", sa.UUID(), nullable=False),
        sa.Column("run_count", sa.Integer(), nullable=True),
        sa.Column("last_run_start_time", sa.DateTime(), nullable=True),
        sa.Column("total_tokens", sa.Integer(), nullable=True),
        sa.<PERSON>umn("prompt_tokens", sa.Integer(), nullable=True),
        sa.<PERSON>umn("completion_tokens", sa.Integer(), nullable=True),
        sa.<PERSON>umn("avg_latency", sa.Numeric(), nullable=True),
        sa.ForeignKeyConstraint(
            ["tracer_session_id"],
            ["tracer_session.id"],
        ),
        sa.PrimaryKeyConstraint("tracer_session_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("tracer_session_stats")
    # ### end Alembic commands ###
