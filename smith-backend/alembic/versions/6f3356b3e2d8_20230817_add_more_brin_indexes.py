"""20230817 Add more BRIN indexes

Revision ID: 6f3356b3e2d8
Revises: 603fe644a19d
Create Date: 2023-08-17 08:13:19.820565

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6f3356b3e2d8"
down_revision = "603fe644a19d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            "create index concurrently ix_tasks_brin_created_at on tasks using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_examples_brin_created_at on examples using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_dataset_brin_created_at on dataset using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_identities_brin_created_at on identities using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_pending_identities_brin_created_at on pending_identities using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_tenants_brin_created_at on tenants using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_users_brin_created_at on users using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_feedback_brin_created_at on feedback using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_api_keys_brin_created_at on api_keys using brin (created_at);"
        )
        op.execute(
            "create index concurrently ix_tracer_session_brin_start_time on tracer_session using brin (start_time);"
        )


def downgrade() -> None:
    pass
