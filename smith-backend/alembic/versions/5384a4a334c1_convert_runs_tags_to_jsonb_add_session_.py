"""Convert runs.tags to jsonb, add session stats time

Revision ID: 5384a4a334c1
Revises: f56e35fa80e0
Create Date: 2023-07-21 14:56:48.085553

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "5384a4a334c1"
down_revision = "f56e35fa80e0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tracer_session_stats", sa.Column("stats_time", sa.DateTime(), nullable=True)
    )
    op.drop_index("idx_fts", table_name="runs", postgresql_using="gin")
    op.drop_column("runs", "fts_searchable")
    op.alter_column(
        "runs",
        "tags",
        existing_type=sa.ARRAY(sa.String()),
        type_=postgresql.JSONB(),
        postgresql_using="to_jsonb(tags)",
    )
    op.add_column(
        "runs",
        sa.Column(
            "fts_searchable",
            postgresql.TSVECTOR(),
            sa.Computed(
                "\nto_tsvector('english', name)\n|| jsonb_to_tsvector('english', coalesce(tags, '[]'), '[\"string\", \"numeric\", \"key\"]')\n|| to_tsvector('english', coalesce(error, ''))\n|| to_tsvector('english', run_type)\n|| jsonb_to_tsvector('english', coalesce(inputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(outputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(events, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(extra, '{}'), '[\"string\", \"numeric\", \"key\"]')\n",
            ),
            nullable=True,
        ),
    )
    op.create_index(
        "idx_fts", "runs", ["fts_searchable"], unique=False, postgresql_using="gin"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tracer_session_stats", "stats_time")
    op.drop_index("idx_fts", table_name="runs", postgresql_using="gin")
    op.drop_column("runs", "fts_searchable")
    op.alter_column(
        "runs",
        "tags",
        existing_type=postgresql.JSONB(),
        type_=sa.ARRAY(sa.String()),
        # todo: this using statement doesn't work
        postgresql_using="tags::TEXT[]",
    )
    op.add_column(
        "runs",
        sa.Column(
            "fts_searchable",
            postgresql.TSVECTOR(),
            sa.Computed(
                "\nto_tsvector('english', name)\n|| array_to_tsvector(coalesce(tags, '{}'))\n|| to_tsvector('english', coalesce(error, ''))\n|| to_tsvector('english', run_type)\n|| jsonb_to_tsvector('english', coalesce(inputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(outputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(events, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(extra, '{}'), '[\"string\", \"numeric\", \"key\"]')\n",
            ),
            nullable=True,
        ),
    )
    op.create_index(
        "idx_fts", "runs", ["fts_searchable"], unique=False, postgresql_using="gin"
    )
    # ### end Alembic commands ###
