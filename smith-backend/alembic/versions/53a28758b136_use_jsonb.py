"""use jsonb

Revision ID: 53a28758b136
Revises: a1871eceb58a
Create Date: 2023-05-09 17:21:12.882694

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "53a28758b136"
down_revision = "a1871eceb58a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tracer_session",
        "extra",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.alter_column(
        "examples",
        "inputs",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.alter_column(
        "examples",
        "outputs",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "inputs",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "serialized",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "outputs",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
    )
    op.create_unique_constraint(
        "unique_example_per_dataset", "examples", ["inputs", "outputs", "dataset_id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tracer_session",
        "extra",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.alter_column(
        "examples",
        "inputs",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.alter_column(
        "examples",
        "outputs",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "inputs",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "serialized",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.alter_column(
        "runs",
        "outputs",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
    )
    op.drop_constraint("unique_example_per_dataset", "examples", type_="unique")
    # ### end Alembic commands ###
