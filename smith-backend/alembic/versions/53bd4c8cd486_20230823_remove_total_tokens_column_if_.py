"""20230823 Remove total_tokens column if exists

Revision ID: 53bd4c8cd486
Revises: 6efd02f73a91
Create Date: 2023-08-23 11:42:37.457699

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "53bd4c8cd486"
down_revision = "6efd02f73a91"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """
    create index concurrently if not exists runs_session_id_start_time_id_status_end_time_root_idx on runs(session_id, start_time desc, id) include (status, end_time) where execution_order = 1"""
        )
        op.execute(
            """
    create index concurrently if not exists runs_session_id_start_time_id_status_end_time_llm_idx on runs(session_id, start_time desc, id) include (status, end_time) where run_type = 'llm'"""
        )
        op.execute(
            """drop index concurrently if exists runs_session_id_start_time_id_status_end_time_total_tokens_root_idx;"""
        )
        op.execute(
            """drop index concurrently if exists runs_session_id_start_time_id_status_end_time_total_tokens_llm_idx;"""
        )
    op.execute("""alter table runs drop column if exists total_tokens;""")

    with op.get_context().autocommit_block():
        op.execute(
            """create unique index concurrently if not exists uq_tenants_handle on tenants (tenant_handle);"""
        )
    op.execute(
        """ALTER TABLE tenants DROP CONSTRAINT IF EXISTS uq_tenants_tenant_handle;"""
    )


def downgrade() -> None:
    pass
