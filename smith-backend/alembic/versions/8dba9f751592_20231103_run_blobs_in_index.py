"""20231103 run_blobs in_index

Revision ID: 8dba9f751592
Revises: 28fca39b95fb
Create Date: 2023-11-03 10:52:40.538394

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "8dba9f751592"
down_revision = "28fca39b95fb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        "alter table run_blobs add column if not exists in_index boolean not null default true;"
    )
    with op.get_context().autocommit_block():
        op.execute("drop index concurrently if exists ix_run_blobs_fts;")
        op.execute(
            """
            create index concurrently if not exists ix_run_blobs_fts on run_blobs using gin (
                jsonb_to_tsvector('english', inline, '["string"]')
            ) where in_index;
            """
        )


def downgrade() -> None:
    op.execute("alter table run_blobs drop column in_index;")
    with op.get_context().autocommit_block():
        op.execute("drop index concurrently if exists ix_run_blobs_fts;")
        op.execute(
            """
            create index concurrently if not exists ix_run_blobs_fts on run_blobs using gin (
                jsonb_to_tsvector('english', inline, '["string"]')
            );
            """
        )
