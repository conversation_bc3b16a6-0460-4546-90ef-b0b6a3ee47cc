"""20230723 Add pending_identities table

Revision ID: 96d4721eada4
Revises: 7fc2abf70a73
Create Date: 2023-07-26 12:03:02.531546

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "96d4721eada4"
down_revision = "7fc2abf70a73"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """CREATE TABLE pending_identities (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id UUID NOT NULL REFERENCES tenants(id),
            created_at TIMESTAMP NOT NULL DEFAULT now(),
            created_by UUID,
            email TEXT NOT NULL,
            UNIQUE (tenant_id, email)
        )"""
    )
    op.execute(
        """CREATE INDEX pending_identities_email_idx ON pending_identities (email)"""
    )


def downgrade() -> None:
    op.execute("DROP TABLE pending_identities")
