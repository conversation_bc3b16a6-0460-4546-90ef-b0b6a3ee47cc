import asyncio
import logging
import os
import random
import string
import sys
import tempfile
import time
from datetime import datetime, timezone
from functools import partial
from multiprocessing import Process
from pathlib import Path
from signal import Signals
from typing import (
    Any,
    AsyncGenerator,
    Awaitable,
    Callable,
    Dict,
    List,
    Optional,
)
from unittest.mock import patch
from uuid import UUID, uuid4

import asyncpg
import httpx
import jwt
import psutil
import pytest
import requests
import saq
import uvicorn
import xdist
from aiobotocore.session import get_session
from aiochclient import ChClient
from fastapi import APIRouter, Body, FastAPI
from filelock import FileLock
from httpx import ASGITransport, AsyncClient
from jwt import decode
from lc_config.service_communication_settings import service_communication_settings
from lc_config.tenant_config import TenantConfig
from lc_database import curl, elasticsearch
from lc_database.clickhouse import construct_clickhouse_url
from lc_database.database import asyncpg_conn
from lc_database.s3_client import close_global_s3_client, create_global_s3_client
from playground.api.api import api_router as playground_api_router
from playground.api.endpoints.internal import router as playground_internal_router
from sse_starlette.sse import AppStatus

from app import schemas
from app.api.auth import AuthInfo, verify
from app.config import settings
from app.main import app
from app.models.identities.users import get_user
from app.tests.utils import (
    ALGORITHM,
    RSA_PUBLIC_KEY,
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
    TEST_DISABLED_TENANT_UUID,
    TEST_DISABLED_USER_EMAIL,
    TEST_DISABLED_USER_UUID,
    TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT,
    TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID,
    TEST_SECOND_USER_LOW_TOTAL_REQUESTS,
    TEST_SECOND_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID,
    TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT,
    TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT_UUID,
    TEST_TENANT_CUSTOM_USAGE_LIMIT_UUID,
    TEST_TENANT_LOW_EVENTS_INGESTED_PER_MIN_UUID,
    TEST_TENANT_LOW_PAYLOAD_SIZE_LIMIT_UUID,
    TEST_TENANT_LOW_TOTAL_REQUESTS_LIMIT_UUID,
    TEST_TENANT_LOW_UNIQUE_TRACES_LIMIT_UUID,
    TEST_TENANT_ONE_USER_THREE_EMAIL,
    TEST_TENANT_ONE_USER_THREE_UUID,
    TEST_TENANT_ONE_USER_TWO_EMAIL,
    TEST_TENANT_ONE_USER_TWO_UUID,
    TEST_TENANT_ONE_UUID,
    TEST_TENANT_TWO_UUID,
    TEST_USER_CUSTOM_USAGE_LIMIT,
    TEST_USER_CUSTOM_USAGE_LIMIT_UUID,
    TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT,
    TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT_UUID,
    TEST_USER_LOW_PAY,
    TEST_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID,
    TEST_USER_LOW_TOTAL_REQUESTS,
    TEST_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID,
    TEST_USER_LOW_UNIQUE_TRACES_LIMIT,
    TEST_USER_LOW_UNIQUE_TRACES_LIMIT_UUID,
    TEST_USER_ONE_EMAIL,
    TEST_USER_ONE_UUID,
    TEST_USER_THREE_EMAIL,
    TEST_USER_THREE_UUID,
    TEST_USER_TWO_EMAIL,
    TEST_USER_TWO_UUID,
    aclient_for_headers,
    api_key_for_tenant,
    ensure_auth_info,
    ensure_basic_auth_user,
    fresh_tenant_client,
    include_user_info,
    jwt_for_tenant,
    queue_waiter,
    random_lower_string,
    setup_headers_for_auth,
    tracer_session_id_for_tenant,
)
from app.workers.queues import single_queue_worker

# Used to run tests locally in IDE
settings.LANGCHAIN_ENV = "local_test"

pytestmark = pytest.mark.anyio

# stdout is not captured in pytest-xdist, so we need to redirect it to stderr
# https://stackoverflow.com/questions/27006884/why-does-pytest-xdist-not-capture-output
sys.stdout = sys.stderr if "PYTEST_XDIST_WORKER" in os.environ else sys.stdout


# Add test name to log messages
class TestNameFilter(logging.Filter):
    def __init__(self):
        super().__init__()
        self.test_name = None

    def filter(self, record):
        record.test_name = self.test_name or "UNKNOWN_TEST"
        return True


@pytest.hookimpl(tryfirst=True)
def pytest_runtest_setup(item):
    test_name = item.name
    test_filter = TestNameFilter()
    test_filter.test_name = test_name
    for handler in logging.getLogger().handlers:
        handler.addFilter(test_filter)


@pytest.fixture()
def use_api_key(request):
    return request.config.getoption("--api-key")


@pytest.fixture(scope="session", autouse=True)
def anyio_backend():
    return ("asyncio", {"use_uvloop": True})


@pytest.fixture(scope="session")
async def db_asyncpg() -> AsyncGenerator[asyncpg.Connection, None]:
    async with asyncpg_conn() as conn:
        yield conn


@pytest.fixture()
async def aclient() -> AsyncGenerator:
    async with AsyncClient(transport=ASGITransport(app), base_url="http://test") as ac:
        yield ac


@pytest.fixture()
async def httpx_client_fixture() -> AsyncGenerator:
    async with httpx.AsyncClient() as client:
        yield client


@pytest.fixture()
async def ch_client(httpx_client_fixture) -> AsyncGenerator:
    client = ChClient(
        session=httpx_client_fixture,
        url=construct_clickhouse_url(
            settings.CLICKHOUSE_HOST, settings.CLICKHOUSE_PORT
        ),
        user=settings.CLICKHOUSE_USER,
        password=settings.CLICKHOUSE_PASSWORD,
        database=settings.CLICKHOUSE_DB,
    )
    yield client


@pytest.fixture()
async def s3_client_fixture() -> AsyncGenerator:
    session = get_session()
    async with session.create_client(
        "s3",
        region_name="us-east-1",
        endpoint_url=settings.S3_API_URL,
        aws_access_key_id=settings.S3_ACCESS_KEY,
        aws_secret_access_key=settings.S3_ACCESS_KEY_SECRET,
    ) as client:
        yield client


@pytest.fixture(scope="function", autouse=True)
async def reset_s3_client():
    await create_global_s3_client()
    yield
    await close_global_s3_client()


@pytest.fixture
def patch_session_aggregation(request):
    """Fixture to patch session aggregation settings"""
    with (
        patch(
            "app.models.tracer_sessions.sort_ch.settings.STATS_USE_SESSION_AGGREGATION",
            request.param,
        ),
        patch(
            "app.models.tracer_sessions.stream.settings.STATS_USE_SESSION_AGGREGATION",
            request.param,
        ),
    ):
        yield request.param


@pytest.fixture
def patch_topk_aggregated_merge_tree(request):
    with (
        patch(
            "app.models.runs.stats.settings.FF_USE_TOPK_AGGREGATED_MERGE_TREE",
            request.param,
        ),
        patch(
            "app.models.tracer_sessions.stream.settings.FF_USE_TOPK_AGGREGATED_MERGE_TREE",
            request.param,
        ),
    ):
        yield request.param


@pytest.fixture
def patch_min_max_time_filtered_query(request):
    with patch(
        "app.models.runs.stats.settings.FF_USE_MIN_MAX_TIME_FILTERED_QUERY",
        request.param,
    ):
        yield request.param


@pytest.fixture
async def fresh_tenant(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> AsyncGenerator[tuple[AsyncClient, AuthInfo], None]:
    """Fixture that provides a fresh tenant client and auth info."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        yield authed_client.client, authed_client.auth


async def _ensure_auth(
    db_asyncpg: asyncpg.Connection,
    tenant_id: UUID,
    tenant_config: TenantConfig,
    tenant_handle: str | None = None,
    user_id: UUID | None = None,
    user_email: str | None = None,
    user_full_name: str | None = None,
    identity_permissions: list[str] | None = None,
    read_only: bool = False,
) -> tuple[AuthInfo, schemas.Tenant]:
    """Ensure a tenant and auth info for the provided tenant and user details."""
    auth, tenant = await ensure_auth_info(
        db_asyncpg,
        tenant_id=tenant_id,
        tenant_handle=tenant_handle,
        user_id=user_id,
        user_email=user_email,
        user_full_name=user_full_name,
        tenant_config=tenant_config,
        identity_permissions=identity_permissions or ["projects:create"],
        read_only=read_only,
        should_ensure_user=True,
    )
    auth.organization_id = tenant.organization_id
    if settings.BASIC_AUTH_ENABLED and not auth.password:
        user = await ensure_basic_auth_user(auth)
        auth.password = user.password
    return auth, tenant


async def _auth_tenant_one(
    db_asyncpg: asyncpg.Connection,
    tenant_config: TenantConfig = SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
) -> tuple[AuthInfo, schemas.Tenant]:
    # For bypassing pytest's fixture caching to recreate the tenant if necessary
    return await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
        tenant_config=tenant_config,
        tenant_handle="test-tenant-one",
        user_id=UUID(TEST_USER_ONE_UUID),
        user_email=TEST_USER_ONE_EMAIL,
        user_full_name="Test User One",
    )


async def _auth_tenant_one_user_two(
    db_asyncpg: asyncpg.Connection,
    tenant_config: TenantConfig = SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
) -> tuple[AuthInfo, schemas.Tenant]:
    return await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
        tenant_config=tenant_config,
        tenant_handle="test-tenant-one",
        user_id=UUID(TEST_TENANT_ONE_USER_TWO_UUID),
        user_email=TEST_TENANT_ONE_USER_TWO_EMAIL,
        user_full_name="Test Tenant One User Two",
    )


async def _auth_tenant_one_user_three(
    db_asyncpg: asyncpg.Connection,
    tenant_config: TenantConfig = SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
) -> tuple[AuthInfo, schemas.Tenant]:
    return await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
        tenant_config=tenant_config,
        tenant_handle="test-tenant-one",
        user_id=UUID(TEST_TENANT_ONE_USER_THREE_UUID),
        user_email=TEST_TENANT_ONE_USER_THREE_EMAIL,
        user_full_name="Test Tenant One User Three",
    )


@pytest.fixture()
async def auth_tenant_one(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _auth_tenant_one(db_asyncpg)
    return auth


@pytest.fixture()
async def auth_tenant_one_user_two(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _auth_tenant_one_user_two(db_asyncpg)
    return auth


@pytest.fixture()
async def auth_tenant_one_user_three(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _auth_tenant_one_user_three(db_asyncpg)
    return auth


@pytest.fixture()
async def auth_tenant_one_read_only(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_ONE_UUID),
        tenant_handle="test-tenant-one",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        user_id=UUID(TEST_USER_THREE_UUID) if include_user_info() else None,
        user_email=TEST_USER_THREE_EMAIL if include_user_info() else None,
        user_full_name="Test User One" if include_user_info() else None,
        read_only=True,
    )
    return auth


@pytest.fixture()
async def auth_tenant_two(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_TWO_UUID),
        tenant_handle="test-tenant-two",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        user_id=UUID(TEST_USER_TWO_UUID) if include_user_info() else None,
        user_email=TEST_USER_TWO_EMAIL if include_user_info() else None,
        user_full_name=None,  # have one user with no full name, like email-only auth
        identity_permissions=["projects:create"],
    )
    return auth


@pytest.fixture()
async def auth_disabled_tenant(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_DISABLED_TENANT_UUID),
        tenant_handle="test-disabled",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        user_id=UUID(TEST_DISABLED_USER_UUID) if include_user_info() else None,
        user_email=TEST_DISABLED_USER_EMAIL if include_user_info() else None,
        identity_permissions=["projects:create"],
    )
    return auth


# Create a tenant with a maximum of 1500 bytes total payload per hour
@pytest.fixture()
async def auth_tenant_low_payload_size_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_PAYLOAD_SIZE_LIMIT_UUID),
        tenant_handle="test-low-payload-size",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_hourly_tracing_bytes": 1500},
        ),
        user_id=UUID(TEST_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_USER_LOW_PAY if include_user_info() else None,
        identity_permissions=["projects:create"],
    )
    return auth


# Create a tenant with a maximum number three total requests per hour
@pytest.fixture()
async def auth_tenant_low_total_requests_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_TOTAL_REQUESTS_LIMIT_UUID),
        tenant_handle="test-low-requests",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_hourly_tracing_requests": 4},
        ),
        user_id=UUID(TEST_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_USER_LOW_TOTAL_REQUESTS if include_user_info() else None,
    )
    return auth


@pytest.fixture()
async def auth_second_user_tenant_low_payload_size_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_PAYLOAD_SIZE_LIMIT_UUID),
        tenant_handle="test-payload-size-2",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_hourly_tracing_bytes": 1500},
        ),
        user_id=UUID(TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_SECOND_USER_LOW_PAYLOAD_SIZE_LIMIT
        if include_user_info()
        else None,
    )
    return auth


@pytest.fixture()
async def auth_second_user_tenant_low_total_requests_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_TOTAL_REQUESTS_LIMIT_UUID),
        tenant_handle="test-low-total",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_hourly_tracing_requests": 4},
        ),
        user_id=UUID(TEST_SECOND_USER_LOW_TOTAL_REQUESTS_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_SECOND_USER_LOW_TOTAL_REQUESTS if include_user_info() else None,
    )
    return auth


@pytest.fixture()
async def auth_tenant_low_unique_traces_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_UNIQUE_TRACES_LIMIT_UUID),
        tenant_handle="test-low-unique",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_monthly_total_unique_traces": 3},
        ),
        user_id=UUID(TEST_USER_LOW_UNIQUE_TRACES_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_USER_LOW_UNIQUE_TRACES_LIMIT if include_user_info() else None,
    )
    return auth


@pytest.fixture()
async def auth_second_user_tenant_low_unique_traces_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_UNIQUE_TRACES_LIMIT_UUID),
        tenant_handle="test-low-unique-2",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_monthly_total_unique_traces": 3},
        ),
        user_id=UUID(TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_SECOND_USER_LOW_UNIQUE_TRACES_LIMIT
        if include_user_info()
        else None,
    )
    return auth


@pytest.fixture()
async def auth_low_per_minute_event_ingestion_limit(
    db_asyncpg: asyncpg.Connection, anyio_backend: Any
) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_LOW_EVENTS_INGESTED_PER_MIN_UUID),
        tenant_handle="test-low-per-minute",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
            update={"max_events_ingested_per_minute": 11},
        ),
        user_id=UUID(TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_USER_LOW_EVENTS_INGESTED_PER_MINUTE_LIMIT
        if include_user_info()
        else None,
    )
    return auth


@pytest.fixture()
async def auth_custom_usage_limit(db_asyncpg: asyncpg.Connection) -> AuthInfo:
    auth, _ = await _ensure_auth(
        db_asyncpg,
        tenant_id=UUID(TEST_TENANT_CUSTOM_USAGE_LIMIT_UUID),
        tenant_handle="test-custom-usage",
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        user_id=UUID(TEST_USER_CUSTOM_USAGE_LIMIT_UUID)
        if include_user_info()
        else None,
        user_email=TEST_USER_CUSTOM_USAGE_LIMIT if include_user_info() else None,
    )
    return auth


@pytest.fixture()
async def tenant_one_headers(
    db_asyncpg: asyncpg.Connection, auth_tenant_one: AuthInfo, use_api_key: bool
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    return await setup_headers_for_auth(db_asyncpg, auth_tenant_one, use_api_key)


@pytest.fixture()
async def tenant_one_user_two_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one_user_two: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    return await setup_headers_for_auth(
        db_asyncpg, auth_tenant_one_user_two, use_api_key
    )


@pytest.fixture()
async def tenant_one_user_three_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one_user_three: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    return await setup_headers_for_auth(
        db_asyncpg, auth_tenant_one_user_three, use_api_key
    )


@pytest.fixture()
async def tenant_one_read_only_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one_read_only: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    if use_api_key:
        return {
            "x-api-key": await api_key_for_tenant(
                auth_tenant_one_read_only, read_only=True
            )
        }
    elif include_user_info():
        token = jwt_for_tenant(db_asyncpg, auth_tenant_one_read_only, read_only=True)
        return {
            "Authorization": f"Bearer {await token}",
            "X-Tenant-Id": str(auth_tenant_one_read_only.tenant_id),
            "X-Organization-Id": str(auth_tenant_one_read_only.organization_id),
        }
    elif settings.AUTH_TYPE == "none":
        # Note this doesn't actually implement read-only behaviour
        return {}
    else:
        raise NotImplementedError


@pytest.fixture()
async def tenant_two_headers(
    db_asyncpg: asyncpg.Connection, auth_tenant_two: AuthInfo, use_api_key: bool
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    return await setup_headers_for_auth(db_asyncpg, auth_tenant_two, use_api_key)


@pytest.fixture()
async def disabled_tenant_headers(
    db_asyncpg: asyncpg.Connection, auth_disabled_tenant: AuthInfo, use_api_key: bool
) -> dict[str, str]:
    # Reset the exit event because pytest uses a new event loop for each test
    # and an event from another loop will raise an exception
    AppStatus.should_exit_event = None
    return await setup_headers_for_auth(db_asyncpg, auth_disabled_tenant, use_api_key)


@pytest.fixture()
async def tenant_low_payload_size_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_payload_size_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_tenant_low_payload_size_limit, use_api_key
    )


@pytest.fixture()
async def tenant_low_total_requests_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_total_requests_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_tenant_low_total_requests_limit, use_api_key
    )


@pytest.fixture()
async def tenant_low_unique_traces_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_unique_traces_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_tenant_low_unique_traces_limit, use_api_key
    )


@pytest.fixture()
async def second_user_tenant_low_payload_size_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_payload_size_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_second_user_tenant_low_payload_size_limit, use_api_key
    )


@pytest.fixture()
async def second_user_tenant_low_total_requests_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_total_requests_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_second_user_tenant_low_total_requests_limit, use_api_key
    )


@pytest.fixture()
async def second_user_tenant_low_unique_traces_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_unique_traces_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_second_user_tenant_low_unique_traces_limit, use_api_key
    )


@pytest.fixture()
async def tenant_auth_low_per_minute_event_ingestion_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_low_per_minute_event_ingestion_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_low_per_minute_event_ingestion_limit, use_api_key
    )


@pytest.fixture()
async def tenant_auth_custom_usage_limit_headers(
    db_asyncpg: asyncpg.Connection,
    auth_custom_usage_limit: AuthInfo,
    use_api_key: bool,
) -> dict[str, str]:
    return await setup_headers_for_auth(
        db_asyncpg, auth_custom_usage_limit, use_api_key
    )


@pytest.fixture()
async def http_tenant_one(tenant_one_headers: dict[str, str]) -> AsyncGenerator:
    async with aclient_for_headers(tenant_one_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_one_user_two(
    tenant_one_user_two_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_one_user_two_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_one_user_three(
    tenant_one_user_three_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_one_user_three_headers) as ac:
        yield ac


@pytest.fixture()
async def http_no_auth() -> AsyncGenerator:
    async with aclient_for_headers() as ac:
        yield ac


@pytest.fixture()
async def http_tenant_one_read_only(
    tenant_one_read_only_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_one_read_only_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_two(tenant_two_headers: dict[str, str]) -> AsyncGenerator:
    async with aclient_for_headers(tenant_two_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_low_payload_size_limit(
    tenant_low_payload_size_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_low_payload_size_limit_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_low_total_requests_limit(
    tenant_low_total_requests_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_low_total_requests_limit_headers) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_low_unique_traces_limit(
    tenant_low_unique_traces_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_low_unique_traces_limit_headers) as ac:
        yield ac


@pytest.fixture()
async def http_second_user_tenant_low_payload_size_limit(
    second_user_tenant_low_payload_size_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(
        second_user_tenant_low_payload_size_limit_headers
    ) as ac:
        yield ac


@pytest.fixture()
async def http_second_user_tenant_low_total_requests_limit(
    second_user_tenant_low_total_requests_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(
        second_user_tenant_low_total_requests_limit_headers
    ) as ac:
        yield ac


@pytest.fixture()
async def http_second_user_tenant_low_unique_traces_limit(
    second_user_tenant_low_unique_traces_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(
        second_user_tenant_low_unique_traces_limit_headers
    ) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_auth_low_per_minute_event_ingestion_limit(
    tenant_auth_low_per_minute_event_ingestion_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(
        tenant_auth_low_per_minute_event_ingestion_limit_headers
    ) as ac:
        yield ac


@pytest.fixture()
async def http_tenant_custom_usage_limit(
    tenant_auth_custom_usage_limit_headers: dict[str, str],
) -> AsyncGenerator:
    async with aclient_for_headers(tenant_auth_custom_usage_limit_headers) as ac:
        yield ac


@pytest.fixture()
async def tenant_one_tracer_session_id(
    db_asyncpg: asyncpg.Connection, auth_tenant_one: AuthInfo
) -> UUID:
    return await tracer_session_id_for_tenant(db_asyncpg, auth_tenant_one)


@pytest.fixture()
async def tenant_one_tracer_session_id_2(
    db_asyncpg: asyncpg.Connection, auth_tenant_one: AuthInfo
) -> UUID:
    return await tracer_session_id_for_tenant(db_asyncpg, auth_tenant_one, "project_2")


@pytest.fixture()
async def tenant_two_tracer_session_id(
    db_asyncpg: asyncpg.Connection, auth_tenant_two: AuthInfo
) -> UUID:
    return await tracer_session_id_for_tenant(db_asyncpg, auth_tenant_two)


@pytest.fixture()
async def disabled_tenant_tracer_session_id(
    db_asyncpg: asyncpg.Connection, auth_disabled_tenant: AuthInfo
) -> UUID:
    return await tracer_session_id_for_tenant(db_asyncpg, auth_disabled_tenant)


@pytest.fixture()
async def tenant_low_payload_size_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_payload_size_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_tenant_low_payload_size_limit
    )


@pytest.fixture()
async def tenant_low_total_requests_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_total_requests_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_tenant_low_total_requests_limit
    )


@pytest.fixture()
async def tenant_low_unique_traces_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_low_unique_traces_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_tenant_low_unique_traces_limit
    )


@pytest.fixture()
async def second_user_tenant_low_payload_size_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_payload_size_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_second_user_tenant_low_payload_size_limit
    )


@pytest.fixture()
async def second_user_tenant_low_total_requests_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_total_requests_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_second_user_tenant_low_total_requests_limit
    )


@pytest.fixture()
async def second_user_tenant_low_unique_traces_limit_tracer_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_second_user_tenant_low_unique_traces_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_second_user_tenant_low_unique_traces_limit
    )


@pytest.fixture()
async def tenant_low_per_minute_event_ingestion_limit_session_id(
    db_asyncpg: asyncpg.Connection,
    auth_low_per_minute_event_ingestion_limit: AuthInfo,
) -> UUID:
    return await tracer_session_id_for_tenant(
        db_asyncpg, auth_low_per_minute_event_ingestion_limit
    )


@pytest.fixture()
async def tenant_one_dataset_id(
    http_tenant_one: AsyncClient,
) -> UUID:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
        },
    )
    assert response.status_code == 200
    return UUID(response.json()["id"])


@pytest.fixture()
async def tenant_one_chat_dataset_id(
    http_tenant_one: AsyncClient,
) -> UUID:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
            "data_type": "chat",
        },
    )
    assert response.status_code == 200
    return UUID(response.json()["id"])


@pytest.fixture()
async def tenant_one_llm_dataset_id(
    db_asyncpg: asyncpg.Connection, http_tenant_one: AsyncClient
) -> UUID:
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
            "data_type": "llm",
        },
    )
    assert response.status_code == 200
    return UUID(response.json()["id"])


@pytest.fixture()
async def tenant_two_dataset_id(
    db_asyncpg: asyncpg.Connection, http_tenant_two: AsyncClient
) -> UUID:
    response = await http_tenant_two.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "comparison view test",
        },
    )
    assert response.status_code == 200
    return UUID(response.json()["id"])


@pytest.fixture()
def wait_until_task_queue_empty(
    timeout: int = 60,
    n_empty_threshold: int = 3,
    debug: bool = False,
    sleep_length_sec: float = 0.1,
) -> Callable[[], Awaitable[Any]]:
    return partial(
        queue_waiter,
        timeout=timeout,
        n_empty_threshold=n_empty_threshold,
        debug=debug,
        sleep_length_sec=sleep_length_sec,
    )


@pytest.fixture(autouse=settings.AUTH_TYPE == "oauth")
def patch_get_user_info_from_access_token(monkeypatch):
    def mock_get_user_info_from_access_token(access_token: str):
        user_info = decode(
            access_token,
            RSA_PUBLIC_KEY,
            algorithms=[ALGORITHM],
            audience=settings.OAUTH_CLIENT_ID,
        )
        return user_info

    monkeypatch.setattr(
        verify, "get_user_info_from_access_token", mock_get_user_info_from_access_token
    )


@pytest.fixture()
async def private_repo_tenant_one(
    http_tenant_one: AsyncClient,
) -> AsyncGenerator[dict, None]:
    try:
        repo_handle = "".join(random.choices(string.ascii_lowercase, k=20))
        response = await http_tenant_one.post(
            "/repos/", json={"repo_handle": repo_handle, "is_public": False}
        )
        assert response.status_code == 200
        yield response.json()["repo"]
    finally:
        # Ideally we'd delete the repo here, but that functionality doesn't exist
        pass


@pytest.fixture()
async def public_repo_tenant_one(
    http_tenant_one: AsyncClient,
) -> AsyncGenerator[dict, None]:
    try:
        repo_handle = "".join(random.choices(string.ascii_lowercase, k=20))
        response = await http_tenant_one.post(
            "/repos/",
            json={"repo_handle": repo_handle, "is_public": True, "tags": ["a", "b"]},
        )
        assert response.status_code == 200
        yield response.json()["repo"]
    finally:
        # Ideally we'd delete the repo here, but that functionality doesn't exist
        pass


@pytest.fixture()
async def manifest_one() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "messages": [
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "SystemMessagePromptTemplate",
                    ],
                    "lc": 1,
                    "type": "constructor",
                    "kwargs": {
                        "prompt": {
                            "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "template": "You are an expert {profession} who loves answering questions cheerfully.",
                                "input_variables": ["profession"],
                                "template_format": "f-string",
                            },
                        }
                    },
                },
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "HumanMessagePromptTemplate",
                    ],
                    "lc": 1,
                    "type": "constructor",
                    "kwargs": {
                        "prompt": {
                            "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "template": "{question}",
                                "input_variables": ["question"],
                                "template_format": "f-string",
                            },
                        }
                    },
                },
            ],
            "input_variables": ["profession", "question"],
        },
    }


@pytest.fixture()
async def manifest_two() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "template": "hi {name}",
            "input_variables": ["name"],
            "template_format": "f-string",
            "partial_variables": {},
        },
    }


@pytest.fixture()
async def manifest_three() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "template": "hi {name} bye",
            "input_variables": ["name"],
            "template_format": "f-string",
            "partial_variables": {},
        },
    }


@pytest.fixture()
async def manifest_runnable() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "schema", "runnable", "RunnableSequence"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "last": {
                "id": ["langchain", "schema", "runnable", "RunnableBinding"],
                "lc": 1,
                "type": "constructor",
                "kwargs": {
                    "bound": {
                        "id": ["langchain", "chat_models", "openai", "ChatOpenAI"],
                        "lc": 1,
                        "type": "constructor",
                        "kwargs": {
                            "openai_api_key": {
                                "id": ["OPENAI_API_KEY"],
                                "lc": 1,
                                "type": "secret",
                            }
                        },
                    },
                    "kwargs": {},
                },
            },
            "first": {
                "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
                "lc": 1,
                "type": "constructor",
                "kwargs": {
                    "messages": [
                        {
                            "id": [
                                "langchain",
                                "prompts",
                                "chat",
                                "SystemMessagePromptTemplate",
                            ],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "prompt": {
                                    "id": [
                                        "langchain",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "lc": 1,
                                    "type": "constructor",
                                    "kwargs": {
                                        "template": "You are a chatbot who speaks like a pirate",
                                        "input_variables": [],
                                        "template_format": "f-string",
                                    },
                                }
                            },
                        },
                        {
                            "id": [
                                "langchain",
                                "prompts",
                                "chat",
                                "HumanMessagePromptTemplate",
                            ],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "prompt": {
                                    "id": [
                                        "langchain",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "lc": 1,
                                    "type": "constructor",
                                    "kwargs": {
                                        "template": "{question}",
                                        "input_variables": ["question"],
                                        "template_format": "f-string",
                                    },
                                }
                            },
                        },
                    ],
                    "input_variables": ["question"],
                },
            },
        },
    }


@pytest.fixture()
async def manifest_playground_prompt() -> AsyncGenerator[dict, None]:
    yield {
        "lc": 1,
        "type": "constructor",
        "id": ["langsmith", "playground", "PromptPlayground"],
        "kwargs": {
            "first": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
                "kwargs": {
                    "messages": [
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain_core",
                                "prompts",
                                "chat",
                                "SystemMessagePromptTemplate",
                            ],
                            "kwargs": {
                                "prompt": {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain_core",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "kwargs": {
                                        "input_variables": [],
                                        "template_format": "f-string",
                                        "template": "You are a helpful chatbot.",
                                    },
                                }
                            },
                        },
                        {
                            "lc": 1,
                            "type": "constructor",
                            "id": [
                                "langchain_core",
                                "prompts",
                                "chat",
                                "HumanMessagePromptTemplate",
                            ],
                            "kwargs": {
                                "prompt": {
                                    "lc": 1,
                                    "type": "constructor",
                                    "id": [
                                        "langchain_core",
                                        "prompts",
                                        "prompt",
                                        "PromptTemplate",
                                    ],
                                    "kwargs": {
                                        "input_variables": ["question"],
                                        "template_format": "f-string",
                                        "template": "{question}",
                                    },
                                }
                            },
                        },
                    ],
                    "input_variables": ["question"],
                },
            },
            "last": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "schema", "runnable", "RunnableBinding"],
                "kwargs": {
                    "bound": {
                        "lc": 1,
                        "type": "constructor",
                        "id": ["langchain", "chat_models", "openai", "ChatOpenAI"],
                        "kwargs": {
                            "openai_api_key": {
                                "id": ["OPENAI_API_KEY"],
                                "lc": 1,
                                "type": "secret",
                            },
                            "model": "gpt-3.5-turbo-0301",
                            "temperature": 0.5,
                            "max_tokens": None,
                            "top_p": 0.6,
                        },
                    },
                    "kwargs": {},
                },
            },
        },
    }


@pytest.fixture()
async def manifest_not_implemented() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "messages": [
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "SystemMessagePromptTemplate",
                    ],
                    "lc": 1,
                    "type": "constructor",
                    "kwargs": {
                        "prompt": {
                            "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "template": "You are an expert {profession} who loves answering questions cheerfully.",
                                "input_variables": ["profession"],
                                "template_format": "f-string",
                            },
                        }
                    },
                },
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "NotImplementedPromptTemplate",
                    ],
                    "lc": 1,
                    "type": "not_implemented",
                },
            ],
            "input_variables": ["profession", "question"],
        },
    }


@pytest.fixture()
async def manifest_bad_namespace() -> AsyncGenerator[dict, None]:
    yield {
        "id": ["langchain", "prompts", "chat", "ChatPromptTemplate"],
        "kwargs": {
            "input_variables": ["name"],
            "messages": [
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "SystemMessagePromptTemplate",
                    ],
                    "kwargs": {
                        "prompt": {
                            "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
                            "kwargs": {
                                "input_variables": [],
                                "partial_variables": {},
                                "template": "Hi",
                                "template_format": "f-string",
                            },
                            "lc": 1,
                            "type": "constructor",
                        }
                    },
                    "lc": 1,
                    "type": "constructor",
                },
                {
                    "id": [
                        "langchain",
                        "prompts",
                        "chat",
                        "HumanMessagePromptTemplate",
                    ],
                    "kwargs": {
                        "prompt": {
                            "id": ["langchain", "prompts", "prompt", "PromptTemplate"],
                            "kwargs": {
                                "input_variables": ["name"],
                                "partial_variables": {},
                                "template": "Whats up {name}",
                                "template_format": "f-string",
                            },
                            "lc": 1,
                            "type": "constructor",
                        }
                    },
                    "lc": 1,
                    "type": "constructor",
                },
                {
                    "id": ["__main__", "MyMessage"],
                    "kwargs": {"content": "hello there"},
                    "lc": 1,
                    "type": "constructor",
                },
            ],
        },
        "lc": 1,
        "type": "constructor",
    }


@pytest.fixture()
async def private_repo_tenant_one_commit(
    private_repo_tenant_one: dict,
    manifest_one: dict,
    http_tenant_one: AsyncClient,
) -> AsyncGenerator[dict, None]:
    resp = await http_tenant_one.post(
        f"/commits/{private_repo_tenant_one['full_name']}",
        json={
            "manifest": manifest_one,
            "parent_commit": None,
        },
    )
    assert resp.status_code == 200
    yield resp.json()["commit"]


@pytest.fixture()
async def tenant_one_run(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> AsyncGenerator[UUID, None]:
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "run_type": "chain",
                    "session_id": str(tenant_one_tracer_session_id),
                    "id": str(run_id),
                    "start_time": "2024-06-05T05:14:29.000000Z",
                    "dotted_order": f"20240605T051429000000Z{run_id}",
                    "trace_id": str(run_id),
                },
            ],
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()
    yield run_id


@pytest.fixture()
async def tenant_two_run(
    http_tenant_two: AsyncClient,
    tenant_two_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> AsyncGenerator[UUID, None]:
    run_id = uuid4()
    response = await http_tenant_two.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "run_type": "chain",
                    "session_id": str(tenant_two_tracer_session_id),
                    "id": str(run_id),
                    "start_time": "2024-06-05T05:14:30.000000Z",
                    "dotted_order": f"20240605T051430000000Z{run_id}",
                    "trace_id": str(run_id),
                },
            ],
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()
    yield run_id


@pytest.fixture(scope="session")
async def user_tenant_one(
    db_asyncpg: asyncpg.Connection,
) -> schemas.UserWithPassword:
    """Create a basic auth user for tenant one."""
    assert settings.BASIC_AUTH_ENABLED
    auth_tenant_one, _ = await _auth_tenant_one(db_asyncpg)
    async with AsyncClient(
        transport=ASGITransport(app),
        base_url="http://test",
    ) as ac:
        response = await ac.post(
            "/orgs/current/members",
            headers={
                "Authorization": f"Bearer {await jwt_for_tenant(db_asyncpg, auth_tenant_one)}",
                "X-Tenant-Id": str(auth_tenant_one.tenant_id),
            },
            json={
                "email": f"basicauthuser+{uuid4()}@langchain.dev",
                "access_scope": "organization",
            },
        )
        assert response.status_code == 200, f"User should be created: {response.text}"
        user_response = response.json()
        user = await get_user(user_response["email"])
        assert user is not None, "Created user should exist"
        return user


def get_minio_server_url():
    if os.getenv("MINIO_CH_URL"):
        return os.getenv("MINIO_CH_URL")
    else:
        # docker compose uses minio to talk from clickhouse to minio
        return "http://minio:9000"


# Mount playground routes on a fake api server
mock_port = 7233
playground_mock = FastAPI()
playground_mock.include_router(playground_api_router, prefix="/playground")
playground_mock.include_router(
    playground_internal_router, prefix="/internal/playground", include_in_schema=False
)


@playground_mock.get("/ok")
def ok():
    return {"status": "ok"}


def start_playground_server():
    uvicorn.run(playground_mock, host="0.0.0.0", port=mock_port)


# Mount ace routes on a fake api server
ace_mock = FastAPI()
ace_api_router = APIRouter()


@ace_api_router.post("/validate")
async def mock_evaluate():
    return {
        "status": "ok",
    }


@ace_api_router.post("/execute")
async def mock_execute(code=Body(...)):
    if (
        code["code"]
        == "def perform_eval(run):\n        return { \"broken\": run['sajkdfhkjsadf'] }"
    ):
        return [
            {
                "status": "failed",
                "stacktrace": "Traceback (most recent call last):\n  File \"<exec>\", line 16, in perform_eval\nKeyError: 'sajkdfhkjsadf'\n",
            }
        ] * len(code["executions"])
    return [
        {
            "status": "success",
            "result": {
                "score": 1.0,
            },
        }
    ]


@ace_api_router.get("/health")
async def health_check():
    return {"status": "ok"}


ace_mock.include_router(ace_api_router, prefix="")


def start_ace_server():
    uvicorn.run(ace_mock, host="0.0.0.0", port=8081)


class TestWorker(saq.Worker):
    # Override to allow tests to terminate workers
    SIGNALS: List[Signals] = []

    # Override to view queue name in logs
    def __repr__(self):
        return f"TestWorker({self.queue.name})"


async def _run_worker(saq_settings: dict) -> None:
    worker = TestWorker(**saq_settings)
    await worker.start()


def start_worker(name: str) -> None:
    saq_settings = single_queue_worker.settings_by_name[name]
    print(f"Starting single worker queue {name}")
    asyncio.run(_run_worker(saq_settings))


def start_sharded_worker(nodename: str) -> None:
    saq_settings = single_queue_worker.sharded_settings(node_name_override=nodename)
    print(f"Starting single worker queue for Redis shard {nodename}")
    asyncio.run(_run_worker(saq_settings))


mock_backend_port = 7234


def start_backend_server():
    uvicorn.run(app, host="0.0.0.0", port=mock_backend_port)


def is_current_worker(worker_id: str) -> bool:
    # default to "master" to catch the case where we're not running in parallel
    return os.getenv("PYTEST_XDIST_WORKER", "master") == worker_id


# In xdist mode, this looks like 'c86d0016d4f64e32a4e8a4f72a8a3327' and is necessary to select a single worker process
# to perfrom the setup and teardown tasks. Not present outside of a worker context (i.e. not present in xdist hooks)
# Without xdist, no locking is needed because the fixture is only run once.
def test_run_id() -> str:
    return os.getenv("PYTEST_XDIST_TESTRUNUID", "")


def xdist_worker() -> str:
    return os.environ.get("PYTEST_XDIST_WORKER", "")


def is_xdist_mode() -> bool:
    return xdist_worker() != ""


def _wait_for_servers():
    timeout = 60  # seconds
    start_time = time.time()
    playground_ready = False
    backend_ready = False
    platform_backend_ready = False
    while time.time() - start_time < timeout:
        try:
            if not playground_ready:
                response = requests.get(f"http://localhost:{mock_port}/ok")
                if response.status_code == 200:
                    playground_ready = True
                    print("Playground server ready")
            if not backend_ready:
                response = requests.get(f"http://localhost:{mock_backend_port}/ok")
                if response.status_code == 200:
                    backend_ready = True
                    print("backend server ready")
            if not platform_backend_ready:
                response = requests.get("http://localhost:8080/ok")
                if response.status_code == 200:
                    platform_backend_ready = True
                    print("platform backend server ready")
            if playground_ready and backend_ready and platform_backend_ready:
                break
        except requests.ConnectionError:
            pass
        print("Waiting for servers to start...")
        time.sleep(1)


async def setup_elasticsearch():
    # remove all test indexes from elasticsearch
    all_indexes = await elasticsearch.client.indices.get(index="*")

    # Filter indexes that start with "test_served_dataset"
    indexes_to_delete = [
        index for index in all_indexes if index.startswith("test_served_dataset")
    ]

    if indexes_to_delete:
        # Delete the filtered indexes
        print("Cleaning up indexes", indexes=indexes_to_delete)
        await elasticsearch.client.indices.delete(index=",".join(indexes_to_delete))


def _setup_servers():
    print("Setting up elasticsearch for testing...")
    setup_elasticsearch()
    print(f"Setting up servers in worker '{xdist_worker()}'")
    procs: List[Process] = []  # type: ignore[annotation-unchecked]
    temp_dir = Path(tempfile.gettempdir())
    pids_file = temp_dir / "_pytestpids.txt"

    proc_playground = Process(
        name="playground", target=start_playground_server, args=()
    )
    proc_playground.start()
    print(f"Started playground with PID {proc_playground.pid}")
    proc_backend = Process(name="backend", target=start_backend_server, args=())
    proc_backend.start()
    print(f"Started backend with PID {proc_backend.pid}")
    proc_ace = Process(name="ace", target=start_ace_server, args=())
    proc_ace.start()
    print(f"Started ace with PID {proc_ace.pid}")
    if settings.REDIS_SHARD_URIS and len(settings.REDIS_SHARD_URIS) > 1:
        print("Starting queue worker per shard")
        for i, nodename in enumerate(settings.REDIS_SHARD_URIS):
            os.environ["SAQ_REDIS_NODE_NAME"] = f"node-{i}"
            p = Process(
                name=f"sharded-worker-{nodename}",
                target=start_sharded_worker,
                args=(nodename,),
            )
            p.start()
            os.environ["SAQ_REDIS_NODE_NAME"] = ""
            print(f"Started sharded worker with pid {p.pid}")
            procs.append(p)
    else:
        proc_single_worker = Process(
            name="single-queue-worker",
            target=start_worker,
            args=(settings.SINGLE_WORKER_QUEUE,),
        )
        proc_single_worker.start()
        print(f"Started single queue worker with pid {proc_single_worker.pid}")
        procs.append(proc_single_worker)
    for proc in [proc_backend, proc_playground, proc_ace]:
        procs.append(proc)
    for name in single_queue_worker.separate_queue_settings_by_name():
        # TODO: support shards + separate queues
        p = Process(
            name=f"separate-queue-worker-{name}",
            target=start_worker,
            args=(name,),
        )
        p.start()
        procs.append(p)

    # Write process IDs to file for cleanup
    print(f"Writing process IDs to {pids_file}")
    procs_text = "\n".join([str(p.pid) for p in procs])
    pids_file.write_text(procs_text)

    # Check if servers are ready
    _wait_for_servers()


# https://pytest-xdist.readthedocs.io/en/latest/how-to.html#making-session-scoped-fixtures-execute-only-once
@pytest.fixture(scope="session", autouse=True)
def setup_servers(request: pytest.FixtureRequest) -> None:
    """Set up servers to prepare for tests."""

    if not xdist.is_xdist_worker(request) and not xdist.is_xdist_controller(request):
        _setup_servers()
        return

    worker = xdist_worker()
    temp_dir = Path(tempfile.gettempdir())
    main_worker_file = temp_dir / f"{test_run_id()}_pytest_main_worker.txt"

    with FileLock(str(main_worker_file) + ".lock"):
        if main_worker_file.is_file():
            # skip test setup, just wait for servers
            print(
                f"Skipping test setup for worker '{worker}' because file exists {main_worker_file}"
            )
            return
        else:
            main_worker_file.write_text(worker)
            print(f"Main worker: '{worker}' wrote file {main_worker_file}")
            _setup_servers()


# Run cleanup after all tests, ensuring that they aren't terminated early.
# https://stackoverflow.com/questions/72225847/pytest-run-teardown-once-for-all-workers-using-pytest-xdist
@pytest.hookimpl()
def pytest_sessionfinish(session: pytest.Session, exitstatus: int):
    """Insert teardown that you want to occur only once here"""

    temp_dir = Path(tempfile.gettempdir())
    pids_file = temp_dir / "_pytestpids.txt"

    if not hasattr(session.config, "workerinput"):
        # Read process IDs from file
        print(f"Reading process IDs from {pids_file}")
        if pids_file.exists():
            pids = [int(line) for line in pids_file.read_text().splitlines()]
            print(f"Terminating test processes {pids}")
            for pid in pids:
                try:
                    p = psutil.Process(pid)
                    print("Terminating", p)
                    p.terminate()
                except psutil.NoSuchProcess:
                    print(f"Process with PID {pid} no longer exists")
        else:
            print(f"Process IDs file does not exist: {pids_file}")
    else:
        print(f"Skipping session finish for worker {os.getenv('PYTEST_XDIST_WORKER')}")

    # print all child process IDs and current process ID
    print(f"Current process ID: {os.getpid()}")
    for child in psutil.Process(os.getpid()).children(recursive=True):
        print(f"Child process ID: {child.pid}")


@pytest.fixture()
def patch_x_service_jwt(monkeypatch):
    def get_x_service_jwt_token_patched(
        payload: Optional[Dict[str, Any]] = None,
    ) -> str:
        # Use the real current time
        exp = int(datetime(2030, 7, 3, 12, 0, 0, tzinfo=timezone.utc).timestamp())

        payload = payload or {}
        payload = {
            "sub": "unspecified",
            "exp": exp,
            **payload,
        }

        return jwt.encode(
            payload,
            service_communication_settings.X_SERVICE_AUTH_JWT_SECRET,
            algorithm="HS256",
        )

    monkeypatch.setattr(
        curl, "get_x_service_jwt_token", get_x_service_jwt_token_patched
    )


async def create_workspaces(
    client: AsyncClient,
    organization_id: UUID | None,
    num_workspaces: int,
) -> List[schemas.Tenant]:
    """Create multiple workspaces for an organization."""
    workspaces = []
    for i in range(num_workspaces):
        response = await client.post(
            "/workspaces" if organization_id else "/tenants",
            json={
                "display_name": random_lower_string(),
                **(
                    {"organization_id": str(organization_id)} if organization_id else {}
                ),
            },
        )
        assert response.status_code == 200, (
            f"Failed to create workspace for {i}: {response.text}"
        )
        workspaces.append(schemas.Tenant(**response.json()))
    return workspaces


# Configure logging for each worker. Necessary because pytest-xdist can't send logs to stdout.
# https://pytest-xdist.readthedocs.io/en/stable/how-to.html#creating-one-log-file-for-each-worker
def pytest_configure(config: pytest.Config):
    worker_id = xdist_worker()
    if worker_id != "":
        root_logger = logging.getLogger()
        file_handler = logging.FileHandler(f"logs/tests_{worker_id}.log", mode="w")
        file_handler.setLevel(settings.LOG_LEVEL)
        formatter = logging.Formatter(
            config.getini("log_file_format"),
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
