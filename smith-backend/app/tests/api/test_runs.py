"""Test correct behavior of the runs endpoints."""

import asyncio
import copy
import datetime
import json
import math
import os
import re
import sys
from collections import Counter, defaultdict
from contextlib import contextmanager
from decimal import Decimal
from functools import wraps
from pathlib import Path
from time import sleep
from typing import Any, Awaitable, Callable, Optional, Tuple, cast
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import aiochclient
import asyncpg
import orjson
import pytest
import requests
from fastapi import HTTPException
from httpx import AsyncClient, Response
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.curl import internal_platform_request
from syrupy import SnapshotAssertion

from app import config, crud, models, schemas
from app.api.auth import AuthInfo
from app.models.runs.fetch_ch import (
    _can_use_session_agg_merge_tree,
    _can_use_topk_agg_merge_tree,
    fetch_runs,
    process_query_params,
)
from app.models.runs.ingest import (
    ERROR_S3_KEY,
    EVENTS_ERROR_S3_KEY,
    EVENTS_S3_KEY,
    EXTRA_S3_KEY,
    ROOT_ERROR_S3_KEY,
    ROOT_S3_KEY,
    SERIALIZED_S3_KEY,
    TokenTracker,
    _get_cost_for_tokens,
)
from app.models.runs.preview import preview_inputs, preview_outputs
from app.models.runs.stats import cached_stats
from app.models.runs.upgrade import upgrade_trace_tier
from app.models.usage_limits import user_defined_limits
from app.tests.utils import (
    fresh_tenant_client,
    post_runs,
    random_lower_string,
    wait_for_runs_to_end,
)

CUR_DIR = Path(__file__).parent
pytestmark = [
    pytest.mark.anyio,
    pytest.mark.runs_only,
    pytest.mark.skipif(
        config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
    ),
]


# Mock time to prevent test flakiness due to crossing a minute/hour/month boundary
# The mock_current_time decorator below should be used for usage limits tests
DEFAULT_DATETIME = datetime.datetime(
    2023, 5, 5, hour=5, minute=12, second=0, tzinfo=datetime.timezone.utc
)


@contextmanager
def _mock_current_time(dt: datetime.datetime = DEFAULT_DATETIME):
    with (
        patch("app.models.runs.usage_limits._current_minute") as mock_current_minute,
        patch("app.models.runs.usage_limits._current_hour") as mock_current_hour,
        patch("app.models.runs.usage_limits._current_month") as mock_current_month,
    ):
        mock_current_minute.return_value = dt.strftime("%Y-%m-%d:%H:%M")
        mock_current_hour.return_value = dt.strftime("%Y-%m-%d:%H")
        mock_current_month.return_value = dt.strftime("%Y-%m")
        yield


def mock_current_time(delta: datetime.timedelta = datetime.timedelta(0)):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            with _mock_current_time(DEFAULT_DATETIME + delta):
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)

        return wrapper

    return decorator


PARAM_INGEST_ENDPOINT = pytest.mark.parametrize(
    "ingest_endpoint",
    [
        "/runs/batch|go",
        "/runs/multipart|go",
    ],
)

TOKEN_USAGE_TEST_DATA_DIR = Path(__file__).parents[1] / "test_data" / "token_usage"
LANGCHAIN_PY_RUNS = [
    TOKEN_USAGE_TEST_DATA_DIR / path
    for path in os.listdir(TOKEN_USAGE_TEST_DATA_DIR)
    if path.startswith("langchain_py")
]
LANGCHAIN_JS_RUNS = [
    TOKEN_USAGE_TEST_DATA_DIR / path
    for path in os.listdir(TOKEN_USAGE_TEST_DATA_DIR)
    if path.startswith("langchain_js")
]
LANGSMITH_PY_SDK_RUNS = [
    TOKEN_USAGE_TEST_DATA_DIR / path
    for path in os.listdir(TOKEN_USAGE_TEST_DATA_DIR)
    if path.startswith("langsmith_py")
]


def _prepare_requests(test_data_path: Path) -> Tuple[dict, Optional[dict], UUID]:
    """Utility routine to prepare requests for ingestion."""
    with open(test_data_path, "r") as f:
        ingest_requests = json.load(f)

    post_requests, patch_requests = (
        ingest_requests.get("post"),
        ingest_requests.get("patch"),
    )
    assert post_requests is not None and len(post_requests) == 1
    if patch_requests is not None:
        assert len(patch_requests) == 1

    post_request = ingest_requests["post"][0]
    patch_request = patch_requests[0] if patch_requests else None

    run_id = uuid4()
    old_dotted_order = post_request.get("dotted_order")
    assert old_dotted_order is not None
    start_time, uuid = old_dotted_order.split("Z")
    new_dotted_order = f"{start_time}Z{run_id}"
    for req in [post_request, patch_request]:
        if req:
            req["id"] = str(run_id)
            req["trace_id"] = str(run_id)
            req["dotted_order"] = new_dotted_order

    if post_request and patch_request:
        post_request.pop("outputs", None)
        post_request.pop("events", None)
        patch_request.pop("inputs", None)

    return post_request, patch_request, run_id


@pytest.mark.parametrize("test_data_path", LANGCHAIN_PY_RUNS)
@PARAM_INGEST_ENDPOINT
async def test_llm_runs_ingest_lc_py(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    auth_tenant_one: AuthInfo,
    ingest_endpoint: str,
    test_data_path: Path,
) -> None:
    """Test LLM run data correctly persisted for LC Py LLM runs."""
    post_request, patch_request, run_id = _prepare_requests(test_data_path)
    req_with_outputs = patch_request or post_request

    usage_metadata = req_with_outputs["outputs"]["generations"][0][0]["message"][
        "kwargs"
    ]["usage_metadata"]

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[post_request],
        patch_runs=[patch_request] if patch_request else [],
    )

    await wait_until_task_queue_empty()
    # fetch the run
    run_ = await crud.get_run(auth_tenant_one, run_id)
    assert run_ is not None
    assert run_.outputs is not None
    assert (
        run_.outputs["generations"][0][0]["message"]["kwargs"]["usage_metadata"]
        == usage_metadata
    )
    assert run_.prompt_tokens == usage_metadata["input_tokens"]
    assert run_.completion_tokens == usage_metadata["output_tokens"]
    assert run_.total_tokens == usage_metadata["total_tokens"]


@pytest.mark.parametrize("test_data_path", LANGCHAIN_JS_RUNS)
@PARAM_INGEST_ENDPOINT
async def test_llm_runs_ingest_lc_js(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    auth_tenant_one: AuthInfo,
    ingest_endpoint: str,
    test_data_path: Path,
) -> None:
    """Test LLM run data correctly persisted for LC JS LLM runs."""
    post_request, patch_request, run_id = _prepare_requests(test_data_path)
    req_with_outputs = patch_request or post_request

    usage_metadata = req_with_outputs["outputs"]["generations"][0][0]["message"][
        "kwargs"
    ].get("usage_metadata")
    legacy_usage_metadata = (
        req_with_outputs["outputs"].get("llmOutput", {}).get("tokenUsage", {})
    )
    assert usage_metadata or legacy_usage_metadata

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[post_request],
        patch_runs=[patch_request] if patch_request else [],
    )

    await wait_until_task_queue_empty()
    # fetch the run
    run_ = await crud.get_run(auth_tenant_one, run_id)
    assert run_ is not None
    assert run_.outputs is not None
    assert (
        run_.outputs["generations"][0][0]["message"]["kwargs"].get("usage_metadata")
        == usage_metadata
    )
    if usage_metadata:
        assert run_.prompt_tokens == usage_metadata["input_tokens"]
        assert run_.completion_tokens == usage_metadata["output_tokens"]
        assert run_.total_tokens == usage_metadata["total_tokens"]
    else:
        assert run_.prompt_tokens == legacy_usage_metadata["promptTokens"]
        assert run_.completion_tokens == legacy_usage_metadata["completionTokens"]
        assert run_.total_tokens == legacy_usage_metadata["totalTokens"]


@pytest.mark.parametrize("test_data_path", LANGSMITH_PY_SDK_RUNS)
@PARAM_INGEST_ENDPOINT
async def test_llm_runs_ingest_py_sdk(
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    auth_tenant_one: AuthInfo,
    ingest_endpoint: str,
    test_data_path: Path,
) -> None:
    """Test LLM run data correctly persisted for LS Python SDK runs."""
    post_request, patch_request, run_id = _prepare_requests(test_data_path)
    req_with_outputs = patch_request or post_request

    usage_metadata = req_with_outputs["outputs"].get("usage_metadata")
    legacy_oai_usage = req_with_outputs["outputs"].get("usage")
    legacy_oai_usage_nested = req_with_outputs["outputs"].get("output", {}).get("usage")
    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[post_request],
        patch_runs=[patch_request] if patch_request else [],
    )

    await wait_until_task_queue_empty()
    # fetch the run
    run_ = await crud.get_run(auth_tenant_one, run_id)
    assert run_ is not None
    assert run_.outputs is not None
    if usage_metadata:
        assert run_.outputs["usage_metadata"] == usage_metadata
        assert run_.prompt_tokens == usage_metadata["input_tokens"]
        assert run_.completion_tokens == usage_metadata["output_tokens"]
        assert run_.total_tokens == usage_metadata["total_tokens"]
    elif legacy_oai_usage:
        assert run_.outputs["usage"] == legacy_oai_usage
        assert run_.prompt_tokens == legacy_oai_usage["prompt_tokens"]
        assert run_.completion_tokens == legacy_oai_usage["completion_tokens"]
        assert run_.total_tokens == legacy_oai_usage["total_tokens"]
    elif legacy_oai_usage_nested:
        assert run_.outputs["output"]["usage"] == legacy_oai_usage_nested
        assert run_.prompt_tokens == legacy_oai_usage_nested["prompt_tokens"]
        assert run_.completion_tokens == legacy_oai_usage_nested["completion_tokens"]
        assert run_.total_tokens == legacy_oai_usage_nested["total_tokens"]


# TODO(agola11) add test for LLM run ingest with js sdk


@PARAM_INGEST_ENDPOINT
async def test_create_run(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that a run can be created."""
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ] * (1000 if settings.MIN_BLOB_STORAGE_SIZE_KB == 0 else 1)
    serialized = {"name": "AgentExecutor"}
    extra: dict[str, Any] = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": extra,
        "error": "an error",
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "trace_id": str(run_id),
    }

    if ingest_endpoint.startswith("/runs/multipart"):
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data)
        assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"
    # check for presence of optional fields
    assert run.inputs == inputs
    assert run.outputs == outputs
    assert run.events == events
    expected_extra = extra
    expected_extra["metadata"]["ls_run_depth"] = 0
    assert run.extra == expected_extra
    assert run.in_dataset is False
    if settings.FF_TRACE_TIERS_ENABLED:
        assert run.trace_tier == schemas.TraceTier.longlived
        assert run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
        assert run.trace_first_received_at is not None
        assert not run.trace_upgrade
    else:
        assert run.trace_tier is None
        assert run.ttl_seconds is None
        assert not run.trace_upgrade
        assert run.trace_first_received_at is None
        assert run.outputs_s3_urls is not None
        assert "ttl_l" not in run.outputs_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert run.inputs_s3_urls is not None
        assert "ttl_l" not in run.inputs_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert run.s3_urls is not None
        assert "ttl_l" not in run.s3_urls[ERROR_S3_KEY]["s3_url"]
        assert "ttl_l" not in run.s3_urls[EVENTS_S3_KEY]["s3_url"]
        assert "ttl_l" not in run.s3_urls[EXTRA_S3_KEY]["s3_url"]

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "LLM"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"
    assert row["input_size"] == len(orjson.dumps(inputs))
    assert row["output_size"] == len(orjson.dumps(outputs))
    assert row["thread_id"] == "112233"

    if config.settings.FF_CH_SEARCH_ENABLED:
        assert "canada" in row["input_tokens"]
        assert "people" in row["output_tokens"]
        assert "error" in row["error_tokens"]

    # Check that multipart split the data via extras
    if "multipart" in ingest_endpoint:
        pending_key = f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}:extra"
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis_operation=redis.RedisOperation.READ
        ) as aredis:
            extra_data = await aredis.hgetall(pending_key)
            extra_data_keys = {k.decode("utf-8") for k in extra_data.keys()}

            if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
                for key in (
                    "error_s3_url",
                    "events_s3_url",
                    "extra_s3_url",
                    "inputs_s3_url",
                    "outputs_s3_url",
                ):
                    assert key in extra_data_keys
            else:
                for key in ("error", "events", "extra", "inputs", "outputs"):
                    assert key in extra_data_keys
                for key in extra_data_keys:
                    assert "s3_url" not in key


@pytest.mark.parametrize(
    "feedback_order",
    ["before", "after", "with"],
)
async def test_create_run_feedback_multipart(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    feedback_order: str,
) -> None:
    """Test that a run can be created."""
    ingest_endpoint = "/runs/multipart|go"
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ] * 1000
    serialized = {"name": "AgentExecutor"}

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
        "error": "an error",
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }

    feedback_data = {
        "id": str(uuid4()),
        "run_id": str(run_id),
        "trace_id": str(run_id),
        "key": "correctness",
        "score": 1,
    }

    run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
    run_data["trace_id"] = str(run_id)

    if feedback_order == "before":
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
            feedback=[],
        )
        await wait_until_task_queue_empty()

    # post feedback and run if going together
    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[run_data] if feedback_order == "with" else [],
        feedback=[feedback_data],
    )

    await wait_until_task_queue_empty()

    if feedback_order == "after":
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
            feedback=[],
        )
        await wait_until_task_queue_empty()

    feedback = await models.feedback.fetch.fetch_feedback(
        UUID(cast(str, feedback_data["id"])), auth_tenant_one
    )
    assert feedback.key == "correctness"
    assert feedback.score == 1

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"
    # check for presence of optional fields
    if len(orjson.dumps(inputs)) > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024:
        assert run.inputs_s3_urls is not None
    else:
        assert run.inputs == inputs
    if len(orjson.dumps(outputs)) > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024:
        assert run.outputs_s3_urls is not None
    else:
        assert run.outputs == outputs
    if len(orjson.dumps(events)) > settings.MIN_BLOB_STORAGE_SIZE_KB * 1024:
        assert run.s3_urls is not None
    else:
        assert run.events == events
    assert run.in_dataset is False


@pytest.mark.parametrize(
    "invalid_format",
    [
        {
            "post.invalid-uuid": json.dumps(
                {"run_type": "llm", "id": str(uuid4()), "name": "yo"}
            )
        },
        {f"invalid.{uuid4()}": json.dumps({"name": "Invalid prefix"})},
        {f"post.{uuid4()}.invalid": json.dumps({"name": "Invalid field"})},
        {f"post.{uuid4()}": json.dumps({"name": "Missing required fields"})},
        # Test non-matching runs - inputs referring to invalid run
        {
            f"post.{uuid4()}": json.dumps({"name": "Valid Run"}),
            "inputs": json.dumps({"input": "This input doesn't match any run"}),
        },
        # Test fields defined twice, in the run and externally
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Duplicate Fields",
                    "inputs": {"internal": "input"},
                    "outputs": {"internal": "output"},
                    "serialized": {"internal": "serialized"},
                }
            ),
            "inputs": json.dumps({"external": "input"}),
            "outputs": json.dumps({"external": "output"}),
            "serialized": json.dumps({"external": "serialized"}),
        },
        # Test invalid JSON in multipart form
        {f"post.{uuid4()}": "Invalid JSON"},
        # Test invalid schema for run
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Invalid Schema",
                    "inputs": {"input": "Valid input"},
                    "outputs": {"output": "Valid output"},
                    "invalid_field": "This field shouldn't be here",
                }
            )
        },
        # Test missing required fields in JSON
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Missing Fields",
                    "inputs": {"input": "Valid input"},
                    # Missing 'outputs' field
                }
            )
        },
        # Test invalid data types
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": 12345,  # Should be a string
                    "inputs": {"input": "Valid input"},
                    "outputs": {"output": "Valid output"},
                }
            )
        },
        # Test invalid nested structures
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Invalid Nested Structure",
                    "inputs": ["This should be an object, not an array"],
                    "outputs": {"output": "Valid output"},
                }
            )
        },
        # Test duplicate run IDs
        (
            (
                f"post.{uuid4()}",
                json.dumps(
                    {
                        "name": "Run 1",
                        "inputs": {"input": "Input 1"},
                        "outputs": {"output": "Output 1"},
                    }
                ),
            ),
            (
                f"post.{uuid4()}",
                json.dumps(
                    {
                        "name": "Run 2",
                        "inputs": {"input": "Input 2"},
                        "outputs": {"output": "Output 2"},
                    }
                ),
            ),
        ),
        # Test duplicate fields within a single run
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Duplicate Fields",
                    "inputs": {"input": "First input"},
                    "outputs": {"output": "Output"},
                }
            )
        },
        # Test duplicate fields across run and external fields
        {
            f"post.{uuid4()}": json.dumps(
                {
                    "name": "Duplicate External Fields",
                    "inputs": {"input": "Internal input"},
                    "outputs": {"output": "Internal output"},
                }
            ),
            "inputs": json.dumps({"input": "External input"}),
            "outputs": json.dumps({"output": "External output"}),
        },
    ],
)
async def test_create_run_invalid_multipart(
    http_tenant_one: AsyncClient,
    invalid_format: dict,
) -> None:
    """Test invalid formats for multipart run creation."""
    response = await http_tenant_one.post("/runs/multipart", files=invalid_format)

    assert response.status_code == 422, response.text


async def test_ai_query(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that the generate-query endpoint works."""
    response = await http_tenant_one.post(
        "/workspaces/current/secrets/",
        json=[{"key": "OPENAI_API_KEY", "value": "a-fake-key"}],
    )

    assert response.status_code == 200, response.text

    mock_query_gen = AsyncMock()
    mock_query_gen.ainvoke.return_value = (
        'and(eq(status, "error"), lt(start_time, "2023-05-04T05:13:32.022361"))'
    )

    with patch(
        "app.models.query_lang.generate.load_query_gen", return_value=mock_query_gen
    ):
        response = await http_tenant_one.post(
            "/runs/generate-query/",
            json={
                "query": "Find all failed runs from yesterday",
            },
        )

        assert response.status_code == 200
        assert "filter" in response.json()
        assert (
            response.json()["filter"]
            == 'and(eq(status, "error"), lt(start_time, "2023-05-04T05:13:32.022361"))'
        )


async def test_create_run_numeric_dts(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be created."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": datetime.datetime.fromisoformat(
                "2023-05-05T05:13:24.571809+00:00"
            ).timestamp(),
            "end_time": datetime.datetime.fromisoformat(
                "2023-05-05T05:13:32.022361+00:00"
            ).timestamp(),
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"
    # check for presence of optional fields
    assert run.inputs == {"input": "How many people live in canada as of 2023?"}
    assert run.outputs == {"output": "39,566,248"}
    assert run.in_dataset is False

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "LLM"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"


async def test_run_depth_metadata(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that ls_run_depth is correctly set in run metadata for nested runs."""
    # Create parent run
    parent_id = uuid4()
    parent_response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Parent",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "Parent input"},
            "outputs": {"output": "Parent output"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(parent_id),
        },
    )
    assert parent_response.status_code == 202, parent_response.text

    # Create child run (depth 1)
    child_id = uuid4()
    child_response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Child",
            "start_time": "2023-05-05T05:13:25.571809",
            "end_time": "2023-05-05T05:13:30.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 2,
            "serialized": {"name": "LLM"},
            "inputs": {"input": "Child input"},
            "outputs": {"output": "Child output"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(parent_id),
            "run_type": "llm",
            "id": str(child_id),
        },
    )
    assert child_response.status_code == 202, child_response.text

    # Create grandchild run (depth 2)
    grandchild_id = uuid4()
    grandchild_response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "Grandchild",
            "start_time": "2023-05-05T05:13:26.571809",
            "end_time": "2023-05-05T05:13:28.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 3,
            "serialized": {"name": "Tool"},
            "inputs": {"input": "Grandchild input"},
            "outputs": {"output": "Grandchild output"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(child_id),
            "run_type": "tool",
            "id": str(grandchild_id),
        },
    )
    assert grandchild_response.status_code == 202, grandchild_response.text

    await wait_until_task_queue_empty()

    # Verify parent run has ls_run_depth = 0
    parent_run = await crud.get_run(auth_tenant_one, parent_id)
    assert parent_run is not None
    assert parent_run.extra is not None
    assert parent_run.extra.get("metadata", {}).get("ls_run_depth") == 0

    # Verify child run has ls_run_depth = 1
    child_run = await crud.get_run(auth_tenant_one, child_id)
    assert child_run is not None
    assert child_run.extra is not None
    assert child_run.extra.get("metadata", {}).get("ls_run_depth") == 1

    # Verify grandchild run has ls_run_depth = 2
    grandchild_run = await crud.get_run(auth_tenant_one, grandchild_id)
    assert grandchild_run is not None
    assert grandchild_run.extra is not None
    assert grandchild_run.extra.get("metadata", {}).get("ls_run_depth") == 2


async def _fetch_propagated_token_rows(ch_client, run_id):
    return await ch_client.fetchrow(
        f"SELECT id, sum(total_tokens) as total_tokens, sum(completion_tokens) as completion_tokens, sum(prompt_tokens) as prompt_tokens FROM runs_token_counts FINAL WHERE id = '{run_id}' group by id",
    )


async def test_create_run_name_size_limit(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a run can't be created with a large name."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM" * 128,
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    # run is accepted, name is truncated
    assert response.status_code == 202, response.text


async def test_create_run_llm_messages(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that both single and multi-batch messages work."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM Single Batch Messages",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "LLM"},
            "inputs": {
                "messages": [
                    {"data": {"content": "You are a helpful agent"}, "type": "ai"},
                    {
                        "data": {"content": "What's the temperature in Reno?"},
                        "type": "human",
                    },
                ],
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.inputs is not None
    expected_input_messages = [
        {"data": {"content": "You are a helpful agent"}, "type": "ai"},
        {"data": {"content": "What's the temperature in Reno?"}, "type": "human"},
    ]
    assert run.inputs["messages"] == expected_input_messages

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM Multi Batch Messages",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "LLM"},
            "inputs": {
                "messages": [
                    [
                        {"data": {"content": "You are a helpful agent"}, "type": "ai"},
                        {
                            "data": {"content": "What's the temperature in Reno?"},
                            "type": "human",
                        },
                    ],
                ],
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.inputs is not None
    assert run.inputs["messages"] == [
        [
            {"data": {"content": "You are a helpful agent"}, "type": "ai"},
            {"data": {"content": "What's the temperature in Reno?"}, "type": "human"},
        ]
    ]


async def test_single_generation(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that LLM runs work with a single list of generations."""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM Single Batch Messages",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "LLM"},
            "inputs": {
                "messages": [
                    {
                        "id": ["langchain", "schema", "messages", "SystemMessage"],
                        "lc": 1,
                        "type": "constructor",
                        "kwargs": {
                            "content": "You are a helpful assistant.",
                            "additional_kwargs": {},
                        },
                    },
                    {
                        "id": ["langchain", "schema", "messages", "HumanMessage"],
                        "lc": 1,
                        "type": "constructor",
                        "kwargs": {
                            "content": "What is the temperature in Reno?",
                            "additional_kwargs": {},
                        },
                    },
                ]
            },
            "outputs": {
                "generations": [
                    {
                        "text": "It's currently 95 degrees in Reno.",
                        "message": {
                            "id": ["langchain", "schema", "messages", "AIMessage"],
                            "lc": 1,
                            "type": "constructor",
                            "kwargs": {
                                "content": "It's currently 95 degrees in Reno.",
                                "additional_kwargs": {},
                            },
                        },
                        "generation_info": {"finish_reason": "stop"},
                    }
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.outputs is not None
    assert run.outputs["generations"] == [
        {
            "text": "It's currently 95 degrees in Reno.",
            "message": {
                "id": ["langchain", "schema", "messages", "AIMessage"],
                "lc": 1,
                "type": "constructor",
                "kwargs": {
                    "content": "It's currently 95 degrees in Reno.",
                    "additional_kwargs": {},
                },
            },
            "generation_info": {"finish_reason": "stop"},
        }
    ]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM Single Prompt",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "LLM"},
            "inputs": {
                "prompt": "What is the population of Canada?",
            },
            "outputs": {
                "generations": [{"text": "39,566,248"}],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.outputs is not None
    assert run.outputs["generations"] == [{"text": "39,566,248"}]


async def test_create_run_simultaneous_writes(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a run can be created."""
    run_id = uuid4()
    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                },
            )
            for _ in range(5)
        )
    )

    assert len(responses) == 5
    status_dict = Counter(response.status_code for response in responses)
    assert status_dict[202] > 0

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"


# Mock to default time so test timing doesn't introduce flakiness
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.flaky
async def test_usage_limits_with_multiple_tenant_users(
    tenant_low_payload_size_limit_tracer_session_id: UUID,
    auth_tenant_low_payload_size_limit: AuthInfo,
    http_tenant_low_payload_size_limit: AsyncClient,
    second_user_tenant_low_payload_size_limit_tracer_session_id: UUID,
    auth_second_user_tenant_low_payload_size_limit: AuthInfo,
    http_second_user_tenant_low_payload_size_limit: AsyncClient,
    tenant_low_total_requests_limit_tracer_session_id: UUID,
    http_tenant_low_total_requests_limit: AsyncClient,
    auth_tenant_low_total_requests_limit: AuthInfo,
    second_user_tenant_low_total_requests_limit_tracer_session_id: UUID,
    http_second_user_tenant_low_total_requests_limit: AsyncClient,
    auth_second_user_tenant_low_total_requests_limit: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that the simple counter tenant usage limits cannot be exceeded."""

    # First, test the payload limit.
    # Payload size: 603
    run_id = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_low_payload_size_limit, run_id)
    assert run.session_id == tenant_low_payload_size_limit_tracer_session_id
    assert run.trace_id == run_id

    # With the second user. Payload size: 603
    run_id_second_user = uuid4()
    response = await http_second_user_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(
                second_user_tenant_low_payload_size_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_second_user),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(
        auth_second_user_tenant_low_payload_size_limit, run_id_second_user
    )
    assert run.session_id == second_user_tenant_low_payload_size_limit_tracer_session_id
    assert run.trace_id == run_id_second_user

    response = await http_tenant_low_payload_size_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    response = await http_second_user_tenant_low_payload_size_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    # now put in a payload from the second user that will put us over the limit, but wouldn't put us over the limit (1500 bytes) on it's own. Payload size: 945
    run_id_2 = uuid4()
    response = await http_second_user_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "input2": "a" * 500,
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(
                second_user_tenant_low_payload_size_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Payload size: 634
    run_id_3 = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "input2": "a" * 500,
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 429
    await wait_until_task_queue_empty()

    # make sure the run wasn't created
    response = await http_tenant_low_payload_size_limit.get(f"/runs/{run_id_3}")
    assert response.status_code == 404

    run_id_4 = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "foo": "bar",
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_4),
        },
    )
    # Now the auth id should be in the reject set so we should get a 429
    assert response.status_code == 429

    response = await http_tenant_low_payload_size_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "payload_size"
    assert usage_limit_info["tenant_limit"] == 1500

    response = await http_second_user_tenant_low_payload_size_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "payload_size"
    assert usage_limit_info["tenant_limit"] == 1500

    run_id_5 = uuid4()
    response = await http_second_user_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "foo": "bar",
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(
                second_user_tenant_low_payload_size_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_5),
        },
    )
    assert response.status_code == 429

    # Now test the total requests limit:
    run_id = uuid4()
    response = await http_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_low_total_requests_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_low_total_requests_limit.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    run_id_2 = uuid4()
    response = await http_second_user_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(
                second_user_tenant_low_total_requests_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_low_total_requests_limit.get(f"/runs/{run_id}")
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id)
    assert response.json()["end_time"] == "2023-05-05T05:13:32.022361"

    response = await http_second_user_tenant_low_total_requests_limit.get(
        f"/runs/{run_id_2}"
    )
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id_2)

    response = await http_tenant_low_total_requests_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    response = await http_second_user_tenant_low_total_requests_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    run_id_3 = uuid4()
    response = await http_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_total_requests_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    # This is the request that sets us over the limit, so it is accepted,
    # but all subsequent requests will be rejected.
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # We can see we're above the limit now by looking at the usage limits endpoint
    response = await http_tenant_low_total_requests_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "events_ingested_per_hour"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_tenant_low_total_requests_limit.tenant_config.max_hourly_tracing_requests
    )

    run_id_4 = uuid4()
    response = await http_second_user_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(
                second_user_tenant_low_total_requests_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 429
    await wait_until_task_queue_empty()

    response = await http_tenant_low_total_requests_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "events_ingested_per_hour"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_tenant_low_total_requests_limit.tenant_config.max_hourly_tracing_requests
    )

    response = await http_second_user_tenant_low_total_requests_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "events_ingested_per_hour"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_second_user_tenant_low_total_requests_limit.tenant_config.max_hourly_tracing_requests
    )

    response = await http_tenant_low_total_requests_limit.get(f"/runs/{run_id_3}")
    assert response.status_code == 200

    response = await http_second_user_tenant_low_total_requests_limit.get(
        f"/runs/{run_id_4}"
    )
    assert response.status_code == 404

    run_id_5 = uuid4()
    response = await http_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": "error",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_total_requests_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_5),
        },
    )
    assert response.status_code == 429

    run_id_6 = uuid4()
    response = await http_second_user_tenant_low_total_requests_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": "error",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(
                second_user_tenant_low_total_requests_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_6),
        },
    )
    assert response.status_code == 429

    response = await http_second_user_tenant_low_total_requests_limit.patch(
        f"/runs/{run_id_2}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 429


# Mock to one hour past default so usage limits don't interfere with other tests
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@mock_current_time(delta=datetime.timedelta(hours=1))
async def test_payload_size_limit_resets_hourly(
    tenant_low_payload_size_limit_tracer_session_id: UUID,
    auth_tenant_low_payload_size_limit: AuthInfo,
    http_tenant_low_payload_size_limit: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that the payload size limit applies and then resets when the hour changes."""

    # First, test the payload limit.
    # Payload size: 945
    run_id = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "input2": "a" * 500,
            },
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # now put in another payload that will put us over the limit, but wouldn't put us over the limit (1500 bytes) on it's own.
    # Payload size: 945
    run_id_2 = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "input2": "a" * 500,
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202, response.text
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_low_payload_size_limit, run_id)
    assert run.trace_id == run_id
    run2 = await crud.get_run(auth_tenant_low_payload_size_limit, run_id_2)
    assert run2.trace_id == run_id_2

    response = await http_tenant_low_payload_size_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "payload_size"
    assert usage_limit_info["tenant_limit"] == 1500

    # This run should fail.
    run_id_3 = uuid4()
    response = await http_tenant_low_payload_size_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {
                "input": "How many people live in canada as of 2023?",
                "input2": "a" * 500,
            },
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 429
    await wait_until_task_queue_empty()

    # make sure the run wasn't created
    response = await http_tenant_low_payload_size_limit.get(f"/runs/{run_id_3}")
    assert response.status_code == 404

    # mock time to the next hour
    with _mock_current_time(DEFAULT_DATETIME + datetime.timedelta(hours=2)):
        # This run should now go through. Payload size: 945
        run_id_4 = uuid4()
        response = await http_tenant_low_payload_size_limit.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {
                    "input": "How many people live in canada as of 2023?",
                    "input2": "a" * 500,
                },
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_low_payload_size_limit_tracer_session_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id_4),
            },
        )

        assert response.status_code == 202
        await wait_until_task_queue_empty()

        run = await crud.get_run(auth_tenant_low_payload_size_limit, run_id_4)
        assert run.trace_id == run_id_4


# This is a monthly limit but we set the time just to be safe
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@mock_current_time(delta=datetime.timedelta(hours=0))
async def test_unique_trace_id_usage_limits(
    tenant_low_unique_traces_limit_tracer_session_id: UUID,
    http_tenant_low_unique_traces_limit: AsyncClient,
    second_user_tenant_low_unique_traces_limit_tracer_session_id: UUID,
    auth_tenant_low_unique_traces_limit: AuthInfo,
    auth_second_user_tenant_low_unique_traces_limit: AuthInfo,
    http_second_user_tenant_low_unique_traces_limit: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that the unique traces tenant usage limits cannot be exceeded."""

    # Create one unique trace. Limit for this tenant is 3.
    run_id = uuid4()
    child_id = uuid4()
    response = await http_tenant_low_unique_traces_limit.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "session_id": str(tenant_low_unique_traces_limit_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}",
                    "trace_id": str(run_id),
                },
                {
                    "name": "AgentExecutorChild",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_low_unique_traces_limit_tracer_session_id),
                    "parent_run_id": str(run_id),
                    "run_type": "chain",
                    "id": str(child_id),
                    "dotted_order": f"20230505T051324571809Z{run_id}.20230505T051324571809Z{child_id}",
                    "trace_id": str(run_id),
                },
            ]
        },
    )
    assert response.status_code == 202

    # Do a patch on the first run here as well. This should not count towards the unique traces limit.
    response = await http_tenant_low_unique_traces_limit.post(
        "/runs/batch",
        json={
            "patch": [
                {
                    "id": str(run_id),
                    "end_time": "2023-01-01T01:01:01.010101",
                    "outputs": {"output": "PATCH"},
                    "dotted_order": f"20230101T010101010101Z{run_id}",
                    "trace_id": str(run_id),
                }
            ]
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_low_unique_traces_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    response = await http_second_user_tenant_low_unique_traces_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is False
    assert usage_limit_info["usage_limit_type"] is None
    assert usage_limit_info["tenant_limit"] is None

    response = await http_tenant_low_unique_traces_limit.get(f"/runs/{run_id}")
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id)
    assert response.json()["name"] == "AgentExecutor"
    assert response.json()["end_time"] == "2023-01-01T01:01:01.010101"
    assert response.json()["outputs"] == {"output": "PATCH"}

    response = await http_tenant_low_unique_traces_limit.get(f"/runs/{child_id}")
    assert response.status_code == 200
    assert response.json()["id"] == str(child_id)
    assert response.json()["name"] == "AgentExecutorChild"

    # Create the second unique trace. this should go through, but bring us just under the limit.
    run_id_2 = uuid4()
    response = await http_second_user_tenant_low_unique_traces_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor2",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent2"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248", "foo": "bar"},
            "session_id": str(
                second_user_tenant_low_unique_traces_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_second_user_tenant_low_unique_traces_limit.get(
        f"/runs/{run_id_2}"
    )
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id_2)
    assert response.json()["name"] == "AgentExecutor2"

    # Now create a third unique trace. This should be the last run accepted
    run_id_3 = uuid4()
    response = await http_tenant_low_unique_traces_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor3",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent3"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248", "foo": "bar"},
            "session_id": str(tenant_low_unique_traces_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    run_id_4 = uuid4()
    response = await http_second_user_tenant_low_unique_traces_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor4",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent4"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248", "foo": "bar"},
            "session_id": str(
                second_user_tenant_low_unique_traces_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 429
    await wait_until_task_queue_empty()

    # Check that the third run was created, but the
    # fourth wasn't, as described above
    response = await http_tenant_low_unique_traces_limit.get(f"/runs/{run_id_3}")
    assert response.status_code == 200

    response = await http_second_user_tenant_low_unique_traces_limit.get(
        f"/runs/{run_id_4}"
    )
    assert response.status_code == 404

    response = await http_tenant_low_unique_traces_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "total_unique_traces"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_tenant_low_unique_traces_limit.tenant_config.max_monthly_total_unique_traces
    )

    response = await http_second_user_tenant_low_unique_traces_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "total_unique_traces"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_second_user_tenant_low_unique_traces_limit.tenant_config.max_monthly_total_unique_traces
    )

    # Now create a fourth unique trace. This should throw a 429 because auth is now in the monthly reject set.
    run_id_5 = uuid4()
    response = await http_tenant_low_unique_traces_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor5",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent5"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248", "foo": "bar"},
            "session_id": str(tenant_low_unique_traces_limit_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_5),
        },
    )
    assert response.status_code == 429

    run_id_6 = uuid4()
    response = await http_second_user_tenant_low_unique_traces_limit.post(
        "/runs",
        json={
            "name": "AgentExecutor6",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent6"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248", "foo": "bar"},
            "session_id": str(
                second_user_tenant_low_unique_traces_limit_tracer_session_id
            ),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_6),
        },
    )
    assert response.status_code == 429

    # Now try patching one of the original runs. Currently, we are blocking everything (post, patch, feedback) if the user is over their unique traces limit, so this should throw a 429.
    response = await http_tenant_low_unique_traces_limit.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-01-07T05:13:32.022361",
            "outputs": {"output": "39,566,248", "foo": "bar"},
        },
    )
    assert response.status_code == 429


# This run relies on real time in the queue, and the rate limit is per
# minute. This test will fail if the minute turns over, so we
# set the time to the beginning of the minute.
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@mock_current_time(delta=datetime.timedelta(hours=0))
async def test_too_many_events_ingested_per_minute(
    http_tenant_auth_low_per_minute_event_ingestion_limit: AsyncClient,
    auth_low_per_minute_event_ingestion_limit: AuthInfo,
    tenant_low_per_minute_event_ingestion_limit_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that the unique traces tenant usage limits cannot be exceeded."""
    # Create ten unique runs in one trace. Limit for events ingested is 11.
    new_runs = []
    trace_id = uuid4()
    parent_run = {
        "name": "AgentExecutor",
        "start_time": datetime.datetime.utcnow().isoformat(),
        "end_time": (
            datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
        ).isoformat(),
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "Agent"},
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "session_id": str(tenant_low_per_minute_event_ingestion_limit_session_id),
        "run_type": "chain",
        "id": str(trace_id),
        "parent_run_id": None,
        "trace_id": str(trace_id),
        "dotted_order": datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
        + str(trace_id),
    }
    new_runs.append(parent_run)
    for _ in range(9):
        run_id = uuid4()
        new_runs.append(
            {
                "name": "AgentExecutor",
                "start_time": datetime.datetime.utcnow().isoformat(),
                "end_time": (
                    datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
                ).isoformat(),
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "Agent"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_id": str(
                    tenant_low_per_minute_event_ingestion_limit_session_id
                ),
                "run_type": "chain",
                "id": str(run_id),
                "parent_run_id": str(trace_id),
                "trace_id": str(trace_id),
                "dotted_order": (
                    cast(str, parent_run["dotted_order"])
                    + "."
                    + datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
                    + str(run_id)
                ),
            },
        )

    # should succeed
    await post_runs(
        "/runs/batch",
        http_tenant_auth_low_per_minute_event_ingestion_limit,
        new_runs,
    )

    await wait_until_task_queue_empty()

    # This API request will succeed (but will be rejected downstream) because it
    # should set the reject key in redis
    next_run_id = uuid4()
    response = await http_tenant_auth_low_per_minute_event_ingestion_limit.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.datetime.utcnow().isoformat(),
                    "end_time": (
                        datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
                    ).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "session_id": str(
                        tenant_low_per_minute_event_ingestion_limit_session_id
                    ),
                    "run_type": "chain",
                    "id": str(next_run_id),
                    "parent_run_id": None,
                    "trace_id": str(next_run_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(next_run_id),
                },
            ],
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    next_run_id = uuid4()
    response = await http_tenant_auth_low_per_minute_event_ingestion_limit.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": datetime.datetime.utcnow().isoformat(),
                    "end_time": (
                        datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
                    ).isoformat(),
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "Agent"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "session_id": str(
                        tenant_low_per_minute_event_ingestion_limit_session_id
                    ),
                    "run_type": "chain",
                    "id": str(next_run_id),
                    "parent_run_id": None,
                    "trace_id": str(next_run_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(next_run_id),
                },
            ],
        },
    )
    assert response.status_code == 429

    response = await http_tenant_auth_low_per_minute_event_ingestion_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "events_ingested_per_minute"
    assert (
        usage_limit_info["tenant_limit"]
        == auth_low_per_minute_event_ingestion_limit.tenant_config.max_events_ingested_per_minute
    )


# Set time so it doesn't fail if run at midnight on the first of the month
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@mock_current_time(delta=datetime.timedelta(hours=0))
async def test_hit_user_defined_monthly_trace_limit(
    auth_custom_usage_limit: AuthInfo,
    http_tenant_custom_usage_limit: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that the unique traces tenant usage limits cannot be exceeded."""

    # No API key access, so call function directly to set usage limit
    payload = user_defined_limits.UpsertUsageLimit(
        limit_type=user_defined_limits.UsageLimitType.MONTHLY_TRACES,
        limit_value=2,
    )
    result = await user_defined_limits.upsert_user_defined_usage_limit(
        auth_custom_usage_limit, payload
    )
    assert result.limit_type == user_defined_limits.UsageLimitType.MONTHLY_TRACES
    assert result.limit_value == 2

    response = await http_tenant_custom_usage_limit.post(
        "/sessions",
        json={"name": "test_hit_user_defined_monthly_trace_limit_session"},
    )
    session_obj = response.json()
    assert response.status_code == 200
    tracer_session_id = session_obj["id"]

    def gen_base_run_fields() -> dict[str, Any]:
        return {
            "name": "AgentExecutor",
            "start_time": datetime.datetime.utcnow().isoformat(),
            "end_time": (
                datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
            ).isoformat(),
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "Agent"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": tracer_session_id,
            "run_type": "chain",
        }

    # Create ten unique runs in one trace. Limit for events ingested is 10.
    new_runs = []
    trace_id = uuid4()
    parent_run = {
        **gen_base_run_fields(),
        "id": str(trace_id),
        "parent_run_id": None,
        "trace_id": str(trace_id),
        "dotted_order": datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
        + str(trace_id),
    }
    new_runs.append(parent_run)

    # we should not limit child runs
    for _ in range(9):
        run_id = uuid4()
        new_runs.append(
            {
                **gen_base_run_fields(),
                "id": str(run_id),
                "parent_run_id": str(trace_id),
                "trace_id": str(trace_id),
                "dotted_order": (
                    cast(str, parent_run["dotted_order"])
                    + "."
                    + datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%S%fZ")
                    + str(run_id)
                ),
            },
        )

    response = await http_tenant_custom_usage_limit.post(
        "/runs/batch",
        json={
            "post": new_runs,
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # This API request will succeed, but hit the limit
    next_run_id = uuid4()
    response = await http_tenant_custom_usage_limit.post(
        "/runs/batch",
        json={
            "post": [
                {
                    **gen_base_run_fields(),
                    "id": str(next_run_id),
                    "parent_run_id": None,
                    "trace_id": str(next_run_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(next_run_id),
                },
            ],
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    next_run_id = uuid4()
    response = await http_tenant_custom_usage_limit.post(
        "/runs/batch",
        json={
            "post": [
                {
                    **gen_base_run_fields(),
                    "id": str(next_run_id),
                    "parent_run_id": None,
                    "trace_id": str(next_run_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(next_run_id),
                },
            ],
        },
    )
    assert response.status_code == 429

    # confirm the run is rejected
    next_run_resp = await http_tenant_custom_usage_limit.get(f"/runs/{next_run_id}")
    assert next_run_resp.status_code == 404

    response = await http_tenant_custom_usage_limit.get(
        "/workspaces/current/usage_limits"
    )
    assert response.status_code == 200
    usage_limit_info = response.json()
    assert usage_limit_info["in_reject_set"] is True
    assert usage_limit_info["usage_limit_type"] == "user_defined_monthly_traces"
    assert usage_limit_info["tenant_limit"] == 2


async def test_create_run_child_run_ignored(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    run_id_1, run_id_2 = uuid4(), uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
            "child_runs": [
                {
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_one_tracer_session_id),
                    "run_type": "chain",
                    "id": str(run_id_2),
                }
            ],
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()
    response = await http_tenant_one.get(f"/runs/{run_id_1}")

    assert response.status_code == 200
    run = response.json()
    assert run["child_run_ids"] is None
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    # Check trace values
    assert run["trace_id"] == str(run_id_1)
    assert run["dotted_order"] == f"20230505T051324571809Z{run_id_1}"

    response = await http_tenant_one.get(f"/runs/{run_id_2}")

    assert response.status_code == 404


async def test_run_already_exists(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 409, "Create run should reject duplicate requests"


@PARAM_INGEST_ENDPOINT
async def test_create_run_valid_manifest(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
    ingest_endpoint: str,
) -> None:
    """Test that a run can be created."""
    run_id = uuid4()
    serialized = {
        "id": ["langchain", "llms", "openai", "OpenAI"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "model": "gpt-4-vision-preview",
            "openai_api_key": {
                "id": ["OPENAI_API_KEY"],
                "lc": 1,
                "type": "secret",
            },
        },
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        serialized["data"] = "x" * 1024

    run_data = {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": serialized,
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "outputs": {"output": "39,566,248"},
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "prompt",
        "id": str(run_id),
    }

    if ingest_endpoint.startswith("/runs/multipart"):
        run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
        run_data["trace_id"] = str(run_id)
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data)
        assert response.status_code == 202

    # Test another run with a different manifest to make sure s3 is working
    run_id_2 = uuid4()
    serialized_2 = {
        "id": ["langchain", "llms", "openai", "OpenAI"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "model": "gpt-4",
            "openai_api_key": {
                "id": ["OPENAI_API_KEY"],
                "lc": 1,
                "type": "secret",
            },
        },
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        serialized_2["data"] = "y" * 1024

    run_data_2 = {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": serialized_2,
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "outputs": {"output": "39,566,248"},
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "llm",
        "id": str(run_id_2),
    }

    if ingest_endpoint.startswith("/runs/multipart"):
        run_data_2["dotted_order"] = f"20230505T051324571809Z{run_id_2}"
        run_data_2["trace_id"] = str(run_id_2)
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data_2],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data_2)
        assert response.status_code == 202

    # Also test another run with the same manifest
    run_id_3 = uuid4()
    run_data_3 = {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": serialized_2,
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "outputs": {"output": "39,566,248"},
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "prompt",
        "id": str(run_id_3),
    }

    if ingest_endpoint.startswith("/runs/multipart"):
        run_data_3["dotted_order"] = f"20230505T051324571809Z{run_id_3}"
        run_data_3["trace_id"] = str(run_id_3)
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data_3],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data_3)
        assert response.status_code == 202

    # Check that the run was created for the correct session
    await wait_until_task_queue_empty()
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        s3_urls = orjson.loads(row["s3_urls"])
        assert s3_urls.get(SERIALIZED_S3_KEY) is not None
        url = s3_urls[SERIALIZED_S3_KEY]
        if "#" in url:
            url, ranges = url.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": url,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
        )
        assert json.loads(result.body) == serialized
    else:
        assert row["manifest_id"] is None
        assert row["manifest_s3_id"] is None
    assert run.serialized == serialized

    run_2 = await crud.get_run(auth_tenant_one, run_id_2)
    assert run_2.session_id == tenant_one_tracer_session_id
    assert run_2.trace_id == run_id_2
    assert run_2.dotted_order == f"20230505T051324571809Z{run_id_2}"
    assert run_2.serialized == serialized_2

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id_2}'",
    )
    assert row is not None
    assert row["id"] == run_id_2
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id_2
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id_2}"
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        s3_urls = orjson.loads(row["s3_urls"])
        assert s3_urls.get(SERIALIZED_S3_KEY) is not None
        url = s3_urls[SERIALIZED_S3_KEY]
        if "#" in url:
            url, ranges = url.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": url,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
        )
        assert json.loads(result.body) == serialized_2
    else:
        assert row["manifest_id"] is None
        assert row["manifest_s3_id"] is None

    run_3 = await crud.get_run(auth_tenant_one, run_id_3)
    assert run_3.session_id == tenant_one_tracer_session_id
    assert run_3.trace_id == run_id_3
    assert run_3.dotted_order == f"20230505T051324571809Z{run_id_3}"
    assert run_3.serialized == serialized_2

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id_3}'",
    )
    assert row is not None
    assert row["id"] == run_id_3
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id_3
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id_3}"
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        s3_urls = orjson.loads(row["s3_urls"])
        assert s3_urls.get(SERIALIZED_S3_KEY) is not None
        url = s3_urls[SERIALIZED_S3_KEY]
        if "#" in url:
            url, ranges = url.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": url,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
        )
        assert json.loads(result.body) == serialized_2
    else:
        assert row["manifest_id"] is None
        assert row["manifest_s3_id"] is None


@pytest.mark.parametrize(
    "run_type,has_graph,should_include_manifest",
    [
        # test with or without graphs across both payload types
        ("llm", True, True),
        ("llm", False, True),
        ("llm", True, True),
        ("llm", False, True),
        # other included type
        ("prompt", False, True),
        ("prompt", True, True),
        # excluded type
        ("tool", False, False),
        ("chain", False, False),
    ],
)
async def test_create_run_manifest_with_included_types(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    run_type: str,
    has_graph: bool,
    should_include_manifest: bool,
) -> None:
    """Test that a run can be created."""
    run_id = uuid4()
    serialized = {
        "id": ["langchain", "llms", "openai", "OpenAI"],
        "lc": 1,
        "type": "constructor",
        "kwargs": {
            "model": "gpt-4-vision-preview",
            "openai_api_key": {
                "id": ["OPENAI_API_KEY"],
                "lc": 1,
                "type": "secret",
            },
        },
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        serialized["data"] = "x" * 1024
    if has_graph:
        serialized["graph"] = {"graph": "data"}

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": run_type,
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    # Check that the run was created for the correct session
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    if should_include_manifest:
        serialized.pop("graph", None)
        assert run.serialized == serialized
        if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
            assert run.s3_urls is not None
            assert run.s3_urls.get(SERIALIZED_S3_KEY) is not None
        else:
            assert run.s3_urls is None or run.s3_urls.get(SERIALIZED_S3_KEY) is None
    else:
        assert run.serialized is None


async def test_create_run_defaults(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a run can be created when serialized and extra not provided."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "run_type": "chain",
            # Below just so we can organize for the test
            "session_id": str(tenant_one_tracer_session_id),
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    # Check that the run was created for the correct session
    await wait_until_task_queue_empty()
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert not run.outputs
    # Check trace values
    assert run.trace_id == run_id
    assert (
        run.dotted_order
        == f"{run.start_time.isoformat(timespec='microseconds').replace(r'-', '').replace(':', '').replace('.', '')}Z{run_id}"
    )

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id
    assert (
        row["dotted_order"]
        == f"{run.start_time.isoformat(timespec='microseconds').replace(r'-', '').replace(':', '').replace('.', '')}Z{run_id}"
    )


async def test_create_run_tzinfo(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a run can be created with tzinfo (sent by LangchainJS)."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809+00:00",
            "end_time": "2023-05-05T05:13:32.022361+00:00",
            "execution_order": 1,
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    # Check that the run was created for the correct session
    await wait_until_task_queue_empty()
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_tenant_session(
    tenant_two_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Run cannot be created with a session that belongs to another tenant."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_two_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 404


async def test_create_run_with_invalid_session(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a run cannot be created with an invalid session."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(uuid4()),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 404

    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, run_id)

    # Test that the run wasn't written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is None


async def test_create_run_with_invalid_parent(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a run cannot be created with invalid parent_run_id."""
    run_id = uuid4()
    parent_run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(parent_run_id),
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, run_id)

    # Test that the run wasn't written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_401_on_auth_failure(
    tenant_one_tracer_session_id: UUID,
    auth_tenant_one: AuthInfo,
    aclient: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Run cannot be created with a parent that belongs to another tenant."""
    run_id = uuid4()

    invalid_headers: dict[str, str] = {}
    if config.settings.AUTH_TYPE == "api_key":
        invalid_headers = {"x-api-key": "invalid"}
    elif config.settings.AUTH_TYPE in ["supabase", "oauth"]:
        invalid_headers = {
            "Authorization": "Bearer invalid",
            "X-Tenant-Id": str(auth_tenant_one.tenant_id),
        }

    payload = {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "outputs": {"output": "39,566,248"},
        "session_id": str(tenant_one_tracer_session_id),
        "run_type": "chain",
        "id": str(run_id),
    }

    response = await aclient.post(
        "/runs",
        headers=invalid_headers,
        json=payload,
    )

    assert response.status_code == 401


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_tenant_parent(
    tenant_one_tracer_session_id: UUID,
    tenant_two_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Run cannot be created with a parent that belongs to another tenant."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.id == run_id

    child_id = uuid4()
    response = await http_tenant_two.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_two_tracer_session_id),
            "parent_run_id": str(run_id),
            "run_type": "chain",
            "id": str(child_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_two, child_id)

    # Test that the run wasn't written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{child_id}'",
    )
    assert row is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_tenant_auth(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Run cannot be created with a parent that belongs to another tenant."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.id == run_id

    child_id = uuid4()
    response = await http_tenant_two.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id),
            "run_type": "chain",
            "id": str(child_id),
        },
    )

    assert response.status_code == 404

    # child run is ignored and never persisted
    await wait_until_task_queue_empty()
    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_two, child_id)
    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, child_id)

    # Test that the run wasn't written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{child_id}'",
    )
    assert row is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_tenant_auth_parallel(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Run cannot be created with a parent that belongs to another tenant."""
    run_id = uuid4()
    child_id = uuid4()
    response_one, response_two = await asyncio.gather(
        http_tenant_one.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_one_tracer_session_id),
                "run_type": "chain",
                "id": str(run_id),
            },
        ),
        http_tenant_two.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_one_tracer_session_id),
                "parent_run_id": str(run_id),
                "run_type": "chain",
                "id": str(child_id),
            },
        ),
    )

    assert response_one.status_code == 202
    assert response_two.status_code == 404
    await wait_until_task_queue_empty()

    # parent run is persisted
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.id == run_id


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_auth_duplicates_ignored(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Run cannot be created with a parent that belongs to another tenant."""
    run_id = uuid4()
    bogus = {
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar", "bogus": True},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"input": "How many people live in canada as of 2023?"},
        "outputs": {"output": "39"},
        "session_id": str(tenant_one_tracer_session_id),
        "run_type": "chain",
        "id": str(run_id),
    }
    responses = await asyncio.gather(
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_one.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_one_tracer_session_id),
                "run_type": "chain",
                "id": str(run_id),
            },
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        http_tenant_two.post(
            "/runs",
            json=bogus,
        ),
        return_exceptions=True,
    )

    assert isinstance(responses[4], Response)
    assert responses[4].status_code == 202
    for i, r in enumerate(responses):
        if i != 4:
            assert isinstance(r, Response)
            assert r.status_code == 404

    await wait_until_task_queue_empty()

    # parent run is persisted, but not the bogus extra
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.id == run_id
    if settings.MIN_BLOB_STORAGE_SIZE_KB != 0:
        assert run.extra == {"foo": "bar", "metadata": {"ls_run_depth": 0}}
        assert run.outputs == {"output": "39,566,248"}

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["extra"] == '{"foo":"bar"}'
        assert row["outputs"] == '{"output":"39,566,248"}'


async def test_create_run_with_invalid_run_type(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a run cannot be created with an invalid run_type."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "invalid",
            "id": str(run_id),
        },
    )

    assert response.status_code == 422


async def test_create_run_with_value_error(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a run cannot be created a large number."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": sys.maxsize + 1,  # large number
            "end_time": sys.maxsize + 1,  # large number
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 422


async def test_create_run_with_example(
    tenant_one_dataset_id: UUID,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Test creating a run with an example."""

    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    output_val_1 = random_lower_string()
    output_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": {"output_1": output_val_1, "output_2": output_val_2},
            "dataset_id": str(tenant_one_dataset_id),
        },
    )

    assert response.status_code == 200
    example_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Fetch the run
    response = await http_tenant_one.get(f"/runs/{run_id}")

    assert response.status_code == 200
    run = response.json()
    assert run["reference_example_id"] == example_id
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    # Check trace values
    assert run["trace_id"] == str(run_id)
    assert (run["dotted_order"]) == f"20230505T051324571809Z{run_id}"
    if settings.FF_TRACE_TIERS_ENABLED:
        assert run["trace_tier"] == "longlived"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None
    assert row["id"] == run_id
    assert row["name"] == "AgentExecutor"
    assert row["trace_id"] == run_id
    assert row["dotted_order"] == f"20230505T051324571809Z{run_id}"
    assert row["reference_example_id"] == UUID(example_id)
    assert row["reference_dataset_id"] == tenant_one_dataset_id

    # test with cache hit
    with patch("app.models.runs.ingest._fetch_dataset_ids_from_pg") as mock_fetch:
        run_id = uuid4()
        response = await http_tenant_one.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_one_tracer_session_id),
                "reference_example_id": str(example_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 202
        await wait_until_task_queue_empty()
        assert mock_fetch.call_count == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.skipif(config.settings.BASIC_AUTH_ENABLED, reason="single org")
async def test_create_run_with_example_usage_limit_exceeded(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test creating a run with an example."""

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # setup usage limits
        payload = user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_LONGLIVED_TRACES,
            limit_value=1,
        )
        await user_defined_limits.upsert_user_defined_usage_limit(
            authed_client.auth, payload
        )

        response = await client.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "comparison view test",
            },
        )
        assert response.status_code == 200
        tenant_one_dataset_id = UUID(response.json()["id"])

        input_val_1 = random_lower_string()
        input_val_2 = random_lower_string()

        output_val_1 = random_lower_string()
        output_val_2 = random_lower_string()

        response = await client.post(
            "/examples",
            json={
                "created_at": "2021-01-01T00:00:00.000Z",
                "inputs": {"input_1": input_val_1, "input_2": input_val_2},
                "outputs": {"output_1": output_val_1, "output_2": output_val_2},
                "dataset_id": str(tenant_one_dataset_id),
            },
        )

        assert response.status_code == 200
        example_id = response.json()["id"]

        for i in range(2):
            run_id = uuid4()
            response = await client.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "reference_example_id": str(example_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                },
            )

            assert response.status_code == 202 if i == 0 else 429

            await wait_until_task_queue_empty()


async def test_query_runs(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test querying runs returns the correct runs."""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in czechia as of 2023?"},
            "outputs": {"output": "110,498,692"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    query_params = {"session": [str(tenant_one_tracer_session_id)], "id": [str(run_id)]}
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    assert post_response.status_code == 200
    post_runs = post_response.json()["runs"]
    run = post_runs[0]
    assert run["id"] == str(run_id)
    assert run["trace_id"] == str(run_id)
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["status"] == "success"


async def test_query_runs_stats(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test querying runs returns the correct feedback stats."""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in czechia as of 2023?"},
            "outputs": {"output": "110,498,692"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in czechia as of 2023?"},
            "outputs": {"output": "110,498,692"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "foo",
            "score": 0,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "foo",
            "score": 90,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id),
            "key": "bar",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_2),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id), str(run_id_2)],
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    assert post_response.status_code == 200
    post_runs = post_response.json()["runs"]
    assert str(run_id) in [run["id"] for run in post_runs]
    assert str(run_id_2) in [run["id"] for run in post_runs]
    run = post_runs[0] if post_runs[0]["id"] == str(run_id) else post_runs[1]
    run_2 = post_runs[0] if post_runs[0]["id"] == str(run_id_2) else post_runs[1]
    assert run["id"] == str(run_id)
    assert run["trace_id"] == str(run_id)
    assert run["session_id"] == str(tenant_one_tracer_session_id)
    assert run["status"] == "success"

    feedback_stats = run["feedback_stats"]
    assert feedback_stats["foo"]["values"] == {"blue": 2, "green": 1}
    assert feedback_stats["foo"]["n"] == 3
    assert feedback_stats["foo"]["avg"] == 30.0
    assert math.isclose(feedback_stats["foo"]["stdev"], 42.426406871193, rel_tol=1e-9)

    assert feedback_stats["bar"]["values"] == {"blue": 1}
    assert feedback_stats["bar"]["n"] == 1
    assert feedback_stats["bar"]["avg"] == 100.0
    assert feedback_stats["bar"]["stdev"] == 0

    feedback_stats_2 = run_2["feedback_stats"]
    assert feedback_stats_2["foo"]["values"] == {"blue": 1}
    assert feedback_stats_2["foo"]["n"] == 1
    assert feedback_stats_2["foo"]["avg"] == 100.0
    assert feedback_stats_2["foo"]["stdev"] == 0


async def test_query_runs_s3_run_io(
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test querying runs returns the correct runs."""
    # Create a tracer session
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )
    assert response.status_code == 200
    tenant_one_tracer_session_id = UUID(response.json()["id"])

    run_id = uuid4()
    inputs = {"input": "How many people live in czechia as of 2023?"}
    outputs = {"output": "110,498,692"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra: dict[str, Any] = {"foo": "bar"}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": extra,
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # should not send signed url if already returning inputs and outputs
    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id)],
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    assert post_response.status_code == 200
    run = post_response.json()["runs"][0]
    assert run["id"] == str(run_id)
    # inputs and outputs should be there
    assert run["inputs"] == inputs
    assert run["outputs"] == outputs
    assert run["error"] == error
    assert run["events"] == events
    extra.update({"metadata": {"ls_run_depth": 0}})
    assert run["extra"] == extra

    # should not return these urls since we are already getting inputs and outputs
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        assert run.get("inputs_s3_urls")
        assert run.get("outputs_s3_urls")
    else:
        assert run.get("inputs_s3_urls") is None
        assert run.get("outputs_s3_urls") is None

    # x_or_signed_url returns either signed urls or inputs whichever is available
    # as we can have data that is in clickhouse or in s3 and not known in advance
    # In this case, data is in s3, return signed url and no runs
    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id)],
        "select": [
            "id",
            "inputs_or_signed_url",
            "outputs_or_signed_url",
            "error_or_signed_url",
            "events_or_signed_url",
            "extra_or_signed_url",
            "serialized_or_signed_url",
        ],
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    run = post_response.json()["runs"][0]
    assert run["id"] == str(run_id)

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        # should return these urls since we are getting inputs and outputs from S3
        assert "presigned_url" in run["inputs_s3_urls"][ROOT_S3_KEY]
        assert "presigned_url" in run["outputs_s3_urls"][ROOT_S3_KEY]
        assert "presigned_url" in run["s3_urls"][ERROR_S3_KEY]
        assert "presigned_url" in run["s3_urls"][EVENTS_S3_KEY]
        assert "presigned_url" in run["s3_urls"][EXTRA_S3_KEY]

        # check contents
        response = requests.get(run["inputs_s3_urls"][ROOT_S3_KEY]["presigned_url"])
        assert orjson.loads(response.content) == inputs
        response = requests.get(run["outputs_s3_urls"][ROOT_S3_KEY]["presigned_url"])
        assert orjson.loads(response.content) == outputs
        response = requests.get(run["s3_urls"][ERROR_S3_KEY]["presigned_url"])
        assert orjson.loads(response.content) == error
        response = requests.get(run["s3_urls"][EVENTS_S3_KEY]["presigned_url"])
        assert orjson.loads(response.content) == events
        response = requests.get(run["s3_urls"][EXTRA_S3_KEY]["presigned_url"])
        assert orjson.loads(response.content) == extra

        # inputs and outputs should not be there
        assert run.get("inputs") is None
        assert run.get("outputs") is None
        assert run.get("error") is None
        assert run.get("events") == []
        assert run.get("extra") == {"metadata": {"ls_run_depth": 0}}
    else:
        # should not return these urls since we are getting inputs and outputs from Clickhouse
        assert not run.get("inputs_s3_urls")
        assert not run.get("outputs_s3_urls")
        assert not run.get("s3_urls")

        # inputs and outputs should be there
        assert run.get("inputs") == inputs
        assert run.get("outputs") == outputs
        assert run.get("error") == error
        assert run.get("events") == events
        extra.update({"metadata": {"ls_run_depth": 0}})
        assert run.get("extra") == extra

    # update to have inputs and outputs only in clickhouse without signed urls
    await ch_client.execute(
        f"""ALTER TABLE runs UPDATE s3_urls = '{{}}', outputs_s3_urls = '{{}}', inputs_s3_urls = '{{}}',
            inputs = '{orjson.dumps(inputs).decode("utf-8")}',
            outputs = '{orjson.dumps(outputs).decode("utf-8")}',
            error = '{error}',
            events = '{orjson.dumps(events).decode("utf-8")}',
            extra = '{orjson.dumps(extra).decode("utf-8")}'
            where id = '{run_id}'
        """
    )

    sleep(1)  # wait for the data to be available in clickhouse

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id)],
        "select": [
            "id",
            "inputs_or_signed_url",
            "outputs_or_signed_url",
            "error_or_signed_url",
            "events_or_signed_url",
            "extra_or_signed_url",
            "serialized_or_signed_url",
        ],
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)
    run = post_response.json()["runs"][0]
    assert run["id"] == str(run_id)
    # inputs and outputs should be there
    assert run.get("inputs") == inputs
    assert run.get("outputs") == outputs
    assert run.get("error") == error
    assert run.get("events") == events
    assert run.get("extra") == extra

    # update to invalid URL. Should not fail the query
    await ch_client.execute(
        f"""ALTER TABLE runs UPDATE outputs_s3_urls = '{{"ROOT": "invalid_url"}}', inputs_s3_urls = '{{}}',
                            inputs = '{{}}', outputs='{{}}' where id = '{run_id}'"""
    )
    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id)],
        "select": ["id", "inputs", "outputs", "error", "events", "extra"],
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    assert post_response.status_code == 200
    run = post_response.json()["runs"][0]


async def test_get_runs_for_dataset_examples(
    auth_tenant_one: AuthInfo,
    tenant_one_dataset_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """
    Test fetching runs by database id and session ids (datasets/{dataset_id}/runs POST endpoint).
    Create 3 sessions with two runs each, but only fetch two of the session ids and make sure we
    don't return the third session's runs.
    """

    tenant_one_first_tracer_session_id = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_first_tracer_session_id = tenant_one_first_tracer_session_id.id

    tenant_one_second_tracer_session_id = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_second_tracer_session_id = tenant_one_second_tracer_session_id.id

    tenant_one_third_tracer_session_id = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_third_tracer_session_id = tenant_one_third_tracer_session_id.id

    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()
    input_val_3 = random_lower_string()
    input_val_4 = random_lower_string()

    output_val_1 = random_lower_string()
    output_val_2 = random_lower_string()
    output_val_3 = random_lower_string()
    output_val_4 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": {"output_1": output_val_1, "output_2": output_val_2},
            "dataset_id": str(tenant_one_dataset_id),
        },
    )

    assert response.status_code == 200
    example_id_1 = response.json()["id"]

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_3, "input_2": input_val_4},
            "outputs": {"output_1": output_val_3, "output_2": output_val_4},
            "dataset_id": str(tenant_one_dataset_id),
        },
    )

    assert response.status_code == 200
    example_id_2 = response.json()["id"]
    await wait_until_task_queue_empty()

    run_id = "32aabc33-aa1c-4ecd-90ce-92af3ec06694"
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the first run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_first_tracer_session_id),
            "reference_example_id": str(example_id_1),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    run_id_2 = "c242f3ee-47bf-4f6f-be4e-a001bd515545"
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-06T05:13:24.571809",
            "end_time": "2023-05-06T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the second run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_first_tracer_session_id),
            "reference_example_id": str(example_id_2),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    run_id_3 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-07T05:13:24.571809",
            "end_time": "2023-05-07T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the third run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_second_tracer_session_id),
            "reference_example_id": str(example_id_1),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    run_id_4 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-08T05:13:24.571809",
            "end_time": "2023-05-08T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the fourth run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_second_tracer_session_id),
            "reference_example_id": str(example_id_2),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 202

    run_id_5 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-09T05:13:24.571809",
            "end_time": "2023-05-09T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the fifth run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_third_tracer_session_id),
            "reference_example_id": str(example_id_1),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_5),
        },
    )
    assert response.status_code == 202

    run_id_6 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-10T05:13:24.571809",
            "end_time": "2023-05-10T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "This is the sixth run on the page?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_third_tracer_session_id),
            "reference_example_id": str(example_id_2),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_6),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        f"/datasets/{tenant_one_dataset_id}/runs",
        json={
            "session_ids": [
                str(tenant_one_first_tracer_session_id),
                str(tenant_one_second_tracer_session_id),
            ],
            "offset": 0,
            "limit": 100,
        },
    )
    assert response.status_code == 200

    e_with_runs = [e for e in response.json() if len(e["runs"]) > 0]
    assert len(e_with_runs) == 2
    assert len(e_with_runs[0]["runs"]) == 2
    assert len(e_with_runs[1]["runs"]) == 2

    response = await http_tenant_one.post(
        "/runs/query",
        json={
            "session": [str(tenant_one_first_tracer_session_id)],
            "limit": 1,
        },
    )
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 1
    assert response.json()["runs"][0]["id"] == str(run_id_2)
    next_cursor = response.json()["cursors"]["next"]
    assert next_cursor is not None
    prev_cursor = response.json()["cursors"]["prev"]
    assert prev_cursor is None

    response = await http_tenant_one.post(
        "/runs/query",
        json={
            "session": [str(tenant_one_first_tracer_session_id)],
            "limit": 1,
            "cursor": next_cursor,
        },
    )
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 1
    assert response.json()["runs"][0]["id"] == str(run_id)
    next_cursor = response.json()["cursors"]["next"]
    assert next_cursor is None
    prev_cursor = response.json()["cursors"]["prev"]
    assert prev_cursor is not None

    if e_with_runs[0]["id"] == example_id_1:
        first_example_from_endpoint = e_with_runs[0]
        second_example_from_endpoint = e_with_runs[1]
        assert e_with_runs[1]["id"] == example_id_2
    else:
        first_example_from_endpoint = e_with_runs[1]
        second_example_from_endpoint = e_with_runs[0]
        assert e_with_runs[0]["id"] == example_id_2

    assert first_example_from_endpoint["runs"][0]["id"] == str(
        run_id
    ) or first_example_from_endpoint["runs"][0]["id"] == str(run_id_3)
    assert first_example_from_endpoint["runs"][1]["id"] == str(
        run_id
    ) or first_example_from_endpoint["runs"][1]["id"] == str(run_id_3)
    assert first_example_from_endpoint["runs"][0]["session_id"] == str(
        tenant_one_first_tracer_session_id
    ) or first_example_from_endpoint["runs"][0]["session_id"] == str(
        tenant_one_second_tracer_session_id
    )
    assert second_example_from_endpoint["runs"][0]["id"] == str(
        run_id_2
    ) or second_example_from_endpoint["runs"][0]["id"] == str(run_id_4)
    assert second_example_from_endpoint["runs"][1]["id"] == str(
        run_id_2
    ) or second_example_from_endpoint["runs"][1]["id"] == str(run_id_4)
    assert second_example_from_endpoint["runs"][0]["session_id"] == str(
        tenant_one_first_tracer_session_id
    ) or second_example_from_endpoint["runs"][0]["session_id"] == str(
        tenant_one_second_tracer_session_id
    )


async def test_create_run_invalid_example(
    tenant_one_tracer_session_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run cannot be created with an invalid example."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "reference_example_id": str(uuid4()),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, run_id)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_run_with_other_tenant_example_id(
    tenant_one_dataset_id: UUID,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
    tenant_two_tracer_session_id: UUID,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run cannot be created with an example from another tenant."""

    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    output_val_1 = random_lower_string()
    output_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": {"output_1": output_val_1, "output_2": output_val_2},
            "dataset_id": str(tenant_one_dataset_id),
        },
    )

    assert response.status_code == 200
    example_id = response.json()["id"]

    run_id = uuid4()
    response = await http_tenant_two.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_two_tracer_session_id),
            "reference_example_id": str(example_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    with pytest.raises(HTTPException):
        await crud.get_run(auth_tenant_one, run_id)


async def test_create_run_token_counting_multiple_posts(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 401,
                        "total_tokens": 1000,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 202

    # Finish the root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()
    # Check that the LLM run that provided explicit token info was respected
    llm_run_id3 = await crud.get_run(auth_tenant_one, run_id_3)
    assert llm_run_id3.total_tokens == 1000
    assert llm_run_id3.prompt_tokens == 599
    assert llm_run_id3.completion_tokens == (
        llm_run_id3.total_tokens - llm_run_id3.prompt_tokens
    )

    # Fetch token counts from clickhouse
    root_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_1}'",
    )
    # Check trace values
    assert root_row["trace_id"] == run_id_1
    assert root_row["dotted_order"] == f"20230505T051324571809Z{run_id_1}"

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_2}'",
    )
    assert row is not None
    assert row["total_tokens"] == root_row["total_tokens"]
    assert row["prompt_tokens"] == root_row["prompt_tokens"]
    assert row["completion_tokens"] == root_row["completion_tokens"]
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_3}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_4}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}"
    )

    # Fetch propagated row counts from clickhouse
    root_row_counts = await _fetch_propagated_token_rows(ch_client, run_id_1)
    assert root_row_counts is not None
    assert root_row_counts["total_tokens"] > 1000
    assert root_row_counts["prompt_tokens"] > 0
    assert root_row_counts["completion_tokens"] == (
        root_row_counts["total_tokens"] - root_row_counts["prompt_tokens"]
    )


@PARAM_INGEST_ENDPOINT
async def test_create_run_token_counting_batch_endpoint(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()
    post = [
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
        },
        {
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 401,
                        "total_tokens": 1000,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}",
        },
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
        },
    ]
    patch = [
        {
            "id": str(run_id_1),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        }
    ]

    await post_runs(ingest_endpoint, http_tenant_one, post, patch)

    await wait_until_task_queue_empty()
    # Check that the LLM run that provided explicit token info was respected
    llm_run_id3 = await crud.get_run(auth_tenant_one, run_id_3)
    assert llm_run_id3.total_tokens == 1000
    assert llm_run_id3.prompt_tokens == 599
    assert llm_run_id3.completion_tokens == (
        llm_run_id3.total_tokens - llm_run_id3.prompt_tokens
    )

    # fetch propagated row counts
    root_row_counts = await _fetch_propagated_token_rows(ch_client, run_id_1)
    assert root_row_counts is not None
    assert root_row_counts["total_tokens"] > 1000
    assert root_row_counts["prompt_tokens"] > 0
    assert root_row_counts["completion_tokens"] == (
        root_row_counts["total_tokens"] - root_row_counts["prompt_tokens"]
    )
    # Fetch token counts from clickhouse
    root_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_1}'",
    )
    assert root_row is not None
    # Check trace values
    assert root_row["trace_id"] == run_id_1
    assert root_row["dotted_order"] == f"20230505T051324571809Z{run_id_1}"

    # Fetch token counts from clickhouse
    row_counts = await _fetch_propagated_token_rows(ch_client, run_id_1)
    assert row_counts is not None
    assert row_counts["total_tokens"] == root_row_counts["total_tokens"]
    assert row_counts["prompt_tokens"] == root_row_counts["prompt_tokens"]
    assert row_counts["completion_tokens"] == root_row_counts["completion_tokens"]
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_2}'",
    )
    assert row is not None
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_3}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_4}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}"
    )


async def test_create_run_token_counting_late_arriving_child_runs_post(
    fresh_tenant: tuple[AsyncClient, AuthInfo],
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    client, auth = fresh_tenant
    test_session = await crud.create_tracer_session(
        auth,
        schemas.TracerSessionCreate(name=random_lower_string(), description="test"),
    )
    tracer_session_id = test_session.id
    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()

    response = await client.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "session_id": str(tracer_session_id),
                    "run_type": "chain",
                    "id": str(run_id_1),
                    "parent_run_id": None,
                    "trace_id": str(run_id_1),
                    "dotted_order": "20230505T051324571809Z" + str(run_id_1),
                },
                {
                    "name": "LLMChain",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2023?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tracer_session_id),
                    "parent_run_id": str(run_id_1),
                    "run_type": "chain",
                    "id": str(run_id_2),
                    "trace_id": str(run_id_1),
                    "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}",
                },
                {
                    "name": "LLM",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {
                        "prompts": ["How many people live in canada as of 2023?"]
                    },
                    "outputs": {
                        "generations": [[{"text": "39,566,248"}]],
                    },
                    "session_id": str(tracer_session_id),
                    "parent_run_id": str(run_id_2),
                    "run_type": "llm",
                    "id": str(run_id_3),
                    "trace_id": str(run_id_1),
                    "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324571809Z{run_id_3}",
                },
            ],
            "patch": [
                {
                    "id": str(run_id_1),
                    "end_time": "2023-05-05T05:13:32.022361",
                    "outputs": {"output": "39,566,248"},
                    "trace_id": str(run_id_1),
                    "dotted_order": "20230505T051324571809Z" + str(run_id_1),
                },
            ],
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    response = await client.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "LLM",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {"foo": "bar", "batch_size": 1},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {
                        "prompts": ["How many people live in canada as of 2023?"]
                    },
                    "outputs": {
                        "generations": [[{"text": "39,566,248"}]],
                        "llm_output": {
                            "token_usage": {
                                "prompt_tokens": 599,
                                "completion_tokens": 401,
                                "total_tokens": 1000,
                            },
                        },
                    },
                    "session_id": str(tracer_session_id),
                    "parent_run_id": str(run_id_2),
                    "run_type": "llm",
                    "id": str(run_id_4),
                    "trace_id": str(run_id_1),
                    "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
                },
            ],
        },
    )
    assert response.status_code == 202

    await wait_for_runs_to_end(auth, run_id_1, run_id_2, run_id_3, run_id_4, debug=True)

    # Check that the LLM run has token counts
    llm_run_id3 = await crud.get_run(auth, run_id_3)
    assert llm_run_id3.total_tokens > 0
    assert llm_run_id3.prompt_tokens > 0
    assert llm_run_id3.completion_tokens == (
        llm_run_id3.total_tokens - llm_run_id3.prompt_tokens
    )

    # Fetch propagated row counts from clickhouse
    root_row_counts = await _fetch_propagated_token_rows(ch_client, run_id_1)
    assert root_row_counts is not None
    assert root_row_counts["total_tokens"] > 0
    assert root_row_counts["prompt_tokens"] > 0
    assert root_row_counts["completion_tokens"] == (
        root_row_counts["total_tokens"] - root_row_counts["prompt_tokens"]
    )
    # Fetch token counts from clickhouse
    root_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_1}'",
    )
    assert root_row is not None
    # Check trace values
    assert root_row["trace_id"] == run_id_1
    assert root_row["dotted_order"] == f"20230505T051324571809Z{run_id_1}"

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_2}'",
    )
    assert row is not None
    assert row["total_tokens"] == root_row["total_tokens"]
    assert row["prompt_tokens"] == root_row["prompt_tokens"]
    assert row["completion_tokens"] == root_row["completion_tokens"]
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_3}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324571809Z{run_id_3}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_4}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324601809Z{run_id_4}"
    )


async def test_create_run_token_counting_late_arriving_child_runs_patch(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()
    run_id_5 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLMRunId4",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 202

    # Test that post/patch LLM calls with explicit token counts are respected
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 409

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "SomeLLM"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_5),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.patch(
        f"/runs/{run_id_5}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "error": None,
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 317,
                        "completion_tokens": 313,
                        "total_tokens": 630,
                    },
                },
            },
        },
    )
    assert response.status_code == 202

    # Finish the root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    response = await http_tenant_one.patch(
        f"/runs/{run_id_4}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "error": None,
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "parent_run_id": str(run_id_2),
            "id": str(run_id_4),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the LLM run has token counts
    llm_run_id3 = await crud.get_run(auth_tenant_one, run_id_3)
    assert llm_run_id3.total_tokens > 0
    assert llm_run_id3.prompt_tokens > 0
    assert llm_run_id3.completion_tokens == (
        llm_run_id3.total_tokens - llm_run_id3.prompt_tokens
    )

    # fetch propagated row counts
    root_row_counts = await _fetch_propagated_token_rows(ch_client, run_id_1)
    assert root_row_counts is not None
    assert root_row_counts["total_tokens"] > 0
    assert root_row_counts["prompt_tokens"] > 0
    assert root_row_counts["completion_tokens"] == (
        root_row_counts["total_tokens"] - root_row_counts["prompt_tokens"]
    )
    # Fetch token counts from clickhouse
    root_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_1}'",
    )
    assert root_row is not None
    # Check trace values
    assert root_row["trace_id"] == run_id_1
    assert root_row["dotted_order"] == f"20230505T051324571809Z{run_id_1}"

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_2}'",
    )
    assert row is not None
    assert row["total_tokens"] == root_row["total_tokens"]
    assert row["prompt_tokens"] == root_row["prompt_tokens"]
    assert row["completion_tokens"] == root_row["completion_tokens"]
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_3}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324571809Z{run_id_3}"
    )

    # Fetch token counts from clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id_4}'",
    )
    assert row is not None
    assert row["total_tokens"] > 0
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == (row["total_tokens"] - row["prompt_tokens"])
    # Check trace values
    assert row["trace_id"] == run_id_1
    assert (
        row["dotted_order"]
        == f"20230505T051324571809Z{run_id_1}.20230505T051324571809Z{run_id_2}.20230505T051324571809Z{run_id_4}"
    )


async def test_create_run_token_counting_function_calls(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that run with functions can be created with tokens updating properly."""
    run_id = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "id": str(run_id),
            "name": "LLM",
            "start_time": "2023-06-05T05:13:24.571809",
            "end_time": "2023-06-05T05:13:32.022361",
            "extra": {
                "invocation_params": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0,
                    "top_p": 1,
                    "frequency_penalty": 0,
                    "presence_penalty": 0,
                    "n": 1,
                    "stream": False,
                    "functions": [
                        {
                            "name": "information_extraction",
                            "description": "Extracts the relevant information from the passage.",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "sentiment": {"type": "string"},
                                    "tone": {"type": "string"},
                                    "language": {"type": "string"},
                                },
                                "required": ["tone"],
                            },
                        }
                    ],
                    "function_call": "auto",
                },
            },
            "error": None,
            "execution_order": 1,
            "child_execution_order": 1,
            "serialized": {"name": "LLM"},
            "inputs": {
                "prompts": [
                    "Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!"
                ]
            },
            "outputs": {
                "generations": [
                    [
                        {
                            "text": "",
                            "message": {
                                "type": "ai",
                                "data": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "function_call": {
                                            "name": "information_extraction",
                                            "arguments": '{\n  "sentiment": "positive",\n  "tone": "friendly",\n  "language": "Spanish"\n}',
                                        }
                                    },
                                },
                            },
                        }
                    ]
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the runs were created with the correct tokens
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens > 0
    assert run.completion_tokens > 0
    assert run.total_tokens == run.prompt_tokens + run.completion_tokens


async def test_create_interrupt_run(
    tenant_one_tracer_session_id: UUID,
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run cannot be created with an invalid example."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": "GraphInterrupt((Interrupt(value={'query': 'How many people live in canada as of 2023?'}, resumable=True, ns=['tools:619e0246-2e91-307b-0344-0594a7b7e271'], when='during'),))",
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.status == "interrupted"


async def test_run_update(
    tenant_one_dataset_id: UUID,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be updated successfully."""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    # Create another session belonging to the same tenant
    session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )

    # Create another run
    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(session.id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Create an example belonging to the same tenant
    input_val_1 = random_lower_string()
    input_val_2 = random_lower_string()

    output_val_1 = random_lower_string()
    output_val_2 = random_lower_string()

    response = await http_tenant_one.post(
        "/examples",
        json={
            "created_at": "2021-01-01T00:00:00.000Z",
            "inputs": {"input_1": input_val_1, "input_2": input_val_2},
            "outputs": {"output_1": output_val_1, "output_2": output_val_2},
            "dataset_id": str(tenant_one_dataset_id),
        },
    )
    assert response.status_code == 200

    # Update the run
    response = await http_tenant_one.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "error": "Something went wrong",
            "events": [{"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"}],
            "tags": ["tag1", "tag2"],
            "extra": {"metadata": {"baz": "foo"}},
        },
    )
    assert response.status_code == 202

    # Update the second run with interrupted status
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "error": "GraphInterrupt((Interrupt(value={'query': 'How many people live in canada as of 2023?'}, resumable=True, ns=['tools:619e0246-2e91-307b-0344-0594a7b7e271'], when='during'),))",
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was updated
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.end_time == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert run.extra == {"metadata": {"baz": "foo", "ls_run_depth": 0}}
    assert run.tags == ["tag1", "tag2"]
    assert run.error == "Something went wrong"
    assert run.events == [{"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"}]
    assert run.status == "error"
    assert run.session_id == tenant_one_tracer_session_id
    assert run.parent_run_id is None
    if settings.FF_TRACE_TIERS_ENABLED:
        assert run.trace_tier == settings.DEFAULT_TRACE_TIER
    else:
        assert run.trace_tier is None

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.status == "interrupted"


async def test_run_update_inputs(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be updated successfully."""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "session_id": str(tenant_one_tracer_session_id),
            "inputs": {"input": "How many people live in canada as of 2025?"},
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202

    # Update the run
    response = await http_tenant_one.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-06-05T05:13:32.022361",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was updated
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.end_time == datetime.datetime(2023, 6, 5, 5, 13, 32, 22361)
    assert run.extra == {"foo": "bar", "metadata": {"ls_run_depth": 0}}
    assert run.inputs == {"input": "How many people live in canada as of 2023?"}
    assert run.outputs == {"output": "39,566,248"}

    assert run.status == "success"
    assert run.session_id == tenant_one_tracer_session_id
    assert run.parent_run_id is None


@PARAM_INGEST_ENDPOINT
@pytest.mark.parametrize(
    ["post_inputs_multiple", "patch_inputs_multiple"],
    [
        (1, 1),
        (1, 10000),
        (10000, 10000),
    ],
)
async def test_run_update_inputs_batch(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    post_inputs_multiple: int,
    patch_inputs_multiple: int,
    ingest_endpoint: str,
) -> None:
    """Test that a run can be updated successfully,
    even when switching storage backends between post and patch"""

    run_id = uuid4()

    post_inputs = {
        "input": "How many people live in canada as of 2025?" * post_inputs_multiple
    }
    post = {
        "trace_id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "name": "AgentExecutor",
        "start_time": "2023-05-05T05:13:24.571809",
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": str(tenant_one_tracer_session_id),
        "inputs": post_inputs,
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }
    await post_runs(ingest_endpoint, http_tenant_one, [post])

    # Update the run
    patch_inputs = {
        "input": "How many people live in canada as of 2023?" * patch_inputs_multiple
    }
    patch_inputs_len = len(json.dumps(patch_inputs))
    patch = {
        "id": str(run_id),
        "trace_id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "end_time": "2023-06-05T05:13:32.022361",
        "inputs": patch_inputs,
        "outputs": {"output": "39,566,248"},
    }
    await post_runs(ingest_endpoint, http_tenant_one, patch_runs=[patch])

    await wait_until_task_queue_empty()

    # Check that the run was updated
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.end_time == datetime.datetime(2023, 6, 5, 5, 13, 32, 22361)
    assert run.extra == {"foo": "bar", "metadata": {"ls_run_depth": 0}}
    assert len(json.dumps(run.inputs)) == patch_inputs_len
    assert run.inputs == {
        "input": "How many people live in canada as of 2023?" * patch_inputs_multiple
    }
    assert run.outputs == {"output": "39,566,248"}

    assert run.status == "success"
    assert run.session_id == tenant_one_tracer_session_id
    assert run.parent_run_id is None


async def test_run_update_before_create(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be updated before being created."""
    run_id = uuid4()

    # Update the run
    response = await http_tenant_one.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-06-05T05:13:32.022361",
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2025?"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was updated
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.end_time == datetime.datetime(2023, 6, 5, 5, 13, 32, 22361)
    assert run.extra == {"foo": "bar", "metadata": {"ls_run_depth": 0}}
    assert run.inputs == {"input": "How many people live in canada as of 2023?"}
    assert run.outputs == {"output": "39,566,248"}
    assert run.status == "success"
    assert run.session_id == tenant_one_tracer_session_id
    assert run.parent_run_id is None


async def test_token_counting_update(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that token counting happens properly when updating a run."""

    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Finish leaf run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022361",
        },
    )
    assert response.status_code == 202, response.text

    # Finish root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    # Check that the runs were created with the correct tokens
    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.total_tokens > 0
    assert root_run.prompt_tokens > 0
    assert root_run.completion_tokens == (
        root_run.total_tokens - root_run.prompt_tokens
    )

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.total_tokens == root_run.total_tokens
    assert run.prompt_tokens == root_run.prompt_tokens
    assert run.completion_tokens == root_run.completion_tokens


async def test_token_counting_dotted_order_microsecond_mismatch(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that token counting happens properly when receiving a run with a start_time and dotted_order difference."""

    run_id = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591000",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"invocation_params": {"model": "gpt-4o"}, "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["Tohle je čeština v plné kráse!"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
            "trace_id": str(run_id),
            # In JS, the microsecond part also includes the execution order
            # which acts as a tiebreaker for runs with same start_time
            "dotted_order": f"20230505T051324591001Z{run_id}",
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    root_run = await crud.get_run(auth_tenant_one, run_id)
    assert root_run.total_tokens > 0
    assert root_run.prompt_tokens > 0
    assert root_run.completion_tokens == (
        root_run.total_tokens - root_run.prompt_tokens
    )


async def test_token_counting_update_with_parent_run_id(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that token counting happens properly when updating a run."""

    run_id_1 = uuid4()
    run_id_2 = uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Finish leaf run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022361",
            "parent_run_id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    # Finish root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the runs were created with the correct tokens
    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.total_tokens > 0
    assert root_run.prompt_tokens > 0
    assert root_run.completion_tokens == (
        root_run.total_tokens - root_run.prompt_tokens
    )

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.total_tokens == root_run.total_tokens
    assert run.prompt_tokens == root_run.prompt_tokens
    assert run.completion_tokens == root_run.completion_tokens


async def test_child_before_parent(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that runs are created when receiving child runs before parent."""

    run_id_1 = uuid4()
    run_id_2 = uuid4()

    # Finish leaf run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022361",
        },
    )
    assert response.status_code == 202

    # Insert child run
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Finish root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    # Insert root run
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the runs were created with the correct tokens
    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.end_time is not None
    assert root_run.total_tokens > 0
    assert root_run.prompt_tokens > 0
    assert root_run.completion_tokens == (
        root_run.total_tokens - root_run.prompt_tokens
    )

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.end_time is not None
    assert run.total_tokens == root_run.total_tokens
    assert run.prompt_tokens == root_run.prompt_tokens
    assert run.completion_tokens == root_run.completion_tokens


async def test_child_before_parent_patch_inc_parent_id(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that runs are created when receiving child runs before parent."""

    run_id_1 = uuid4()
    run_id_2 = uuid4()

    # Finish leaf run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022361",
            "parent_run_id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    # Insert child run
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Finish root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202

    # Insert root run
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the runs were created with the correct tokens
    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.end_time is not None
    assert root_run.total_tokens > 0
    assert root_run.prompt_tokens > 0
    assert root_run.completion_tokens == (
        root_run.total_tokens - root_run.prompt_tokens
    )

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.end_time is not None
    assert run.total_tokens == root_run.total_tokens
    assert run.prompt_tokens == root_run.prompt_tokens
    assert run.completion_tokens == root_run.completion_tokens


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_share_public_run(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    tenant_two_headers: dict[str, str],
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that sharing a run works."""
    # Create a run tied to a new session
    run_id_1, run_id_2 = uuid4(), uuid4()
    session_name = random_lower_string()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": session_name,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": session_name,
            "parent_run_id": str(run_id_1),
            "id": str(run_id_2),
            "run_type": "chain",
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that issuing a GET for both runs with tenant_two_headers fails with 404
    response = await http_tenant_one.get(
        f"/runs/{str(run_id_1)}", headers=tenant_two_headers
    )
    assert response.status_code == 404
    response = await http_tenant_one.get(
        f"/runs/{str(run_id_2)}", headers=tenant_two_headers
    )
    assert response.status_code == 404

    # Share the run
    response = await http_tenant_one.put(
        f"/runs/{str(run_id_1)}/share",
        headers=tenant_two_headers,
    )
    assert response.status_code == 404

    response = await http_tenant_one.put(
        f"/runs/{str(run_id_1)}/share",
    )
    assert response.status_code == 200
    assert response.json()["run_id"] == str(run_id_1)
    share_token = response.json()["share_token"]

    response = await http_tenant_one.get(f"/runs/{str(run_id_1)}")
    assert response.status_code == 200
    assert response.json()["id"] == str(run_id_1)
    assert response.json()["share_token"] == share_token

    response = await http_tenant_one.get(f"/runs/{str(run_id_1)}/share")
    assert response.status_code == 200
    assert response.json()["share_token"] == share_token

    response = await http_tenant_one.post(f"/public/{share_token}/runs/query", json={})
    assert response.status_code == 200, response.text
    runs = response.json()["runs"]
    assert len(runs) == 1
    assert runs[0]["id"] == str(run_id_1)
    assert runs[0]["parent_run_id"] is None

    response = await http_tenant_one.post(
        f"/public/{share_token}/runs/query",
        json={"id": [str(run_id_2)]},
    )

    assert response.status_code == 200
    runs = response.json()["runs"]
    assert len(runs) == 1
    assert runs[0]["id"] == str(run_id_2)
    assert runs[0]["parent_run_id"] == str(run_id_1)

    # check if the shared run is in the list of all shared entities
    response = await http_tenant_one.get("/workspaces/current/shared")
    assert response.status_code == 200, response.text
    entities = response.json()["entities"]

    assert len(entities) > 0
    assert any(
        x.get("type") == "run"
        and x.get("share_token") == str(share_token)
        and x.get("run_id") == str(run_id_1)
        for x in entities
    )

    # Unshare the run
    response = await http_tenant_one.delete(
        f"/runs/{str(run_id_1)}/share",
        headers=tenant_two_headers,
    )
    assert response.status_code == 404

    response = await http_tenant_one.delete(
        f"/runs/{str(run_id_1)}/share",
    )
    assert response.status_code == 200

    response = await http_tenant_one.post(f"/public/{share_token}/runs/query", json={})
    assert response.status_code == 404

    response = await http_tenant_one.get("/workspaces/current/shared")
    assert response.status_code == 200, response.text
    entities = response.json()["entities"]

    assert not any(
        x.get("type") == "run"
        and x.get("share_token") == str(share_token)
        and x.get("run_id") == str(run_id_1)
        for x in entities
    )

    # Share the child run
    response = await http_tenant_one.put(
        f"/runs/{str(run_id_2)}/share",
    )

    assert response.status_code == 200
    assert response.json()["run_id"] == str(run_id_2)
    share_token = response.json()["share_token"]

    response = await http_tenant_one.post(f"/public/{share_token}/runs/query", json={})
    assert response.status_code == 200
    runs = response.json()["runs"]
    assert len(runs) == 1
    assert runs[0]["id"] == str(run_id_2)
    # Check that parent run id is obfuscated
    assert runs[0]["parent_run_id"] is None


async def test_run_ordering(
    http_tenant_one: AsyncClient,
    tenant_one_tracer_session_id: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that runs are ordered correctly."""
    # Create some runs with different start times
    run_id_1, run_id_2, run_id_3 = uuid4(), uuid4(), uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-07-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-06-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_3),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs/query",
        json={"id": [str(run_id_3), str(run_id_2), str(run_id_1)]},
    )
    assert response.status_code == 200

    runs = response.json()["runs"]
    assert len(runs) == 3


async def test_create_run_openai_schema(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that an LLM run made with openai schema is correctly formatted."""
    run_id = uuid4()
    function_schema = {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city and state, e.g. San Francisco, CA",
                },
                "unit": {
                    "type": "string",
                    "enum": ["celsius", "fahrenheit"],
                },
            },
            "required": ["location"],
        },
    }
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "MyLLMRun",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": "You are a helpful agent"},
                    {"role": "user", "content": "What's the temperature in Reno?"},
                ],
                "temperature": 0,
                "functions": [function_schema],
            },
            "outputs": {
                "id": "chatcmpl-7lsqKDGhb6UDHiXS2IBZ81q0gAMVL",
                "object": "chat.completion",
                "created": 1691646588,
                "model": "gpt-3.5-turbo-0613",
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": None,
                            "function_call": {
                                "name": "get_current_weather",
                                "arguments": '{\n  "location": "Reno, NV"\n}',
                            },
                        },
                        "finish_reason": "function_call",
                    }
                ],
                "usage": {
                    "prompt_tokens": 87,
                    "completion_tokens": 19,
                    "total_tokens": 106,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id


async def test_create_run_openai_schema_wrapped_messages(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a chat model doesn't error out if the messages are nested."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "MyLLMRun",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "model": "gpt-3.5-turbo",
                "messages": [
                    [
                        {"role": "system", "content": "You are a helpful agent"},
                        {"role": "user", "content": "What's the temperature in Reno?"},
                    ]
                ],
                "temperature": 0,
            },
            "outputs": {
                "id": "chatcmpl-7lsqKDGhb6UDHiXS2IBZ81q0gAMVL",
                "object": "chat.completion",
                "created": 1691646588,
                "model": "gpt-3.5-turbo-0613",
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": "Foo Bar",
                        },
                        "finish_reason": "function_call",
                    }
                ],
                "usage": {
                    "prompt_tokens": 87,
                    "completion_tokens": 19,
                    "total_tokens": 106,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.inputs is not None
    assert "messages" in run.inputs
    assert len(run.inputs["messages"]) == 1
    assert len(run.inputs["messages"][0]) == 2


async def test_create_run_openai_completion_schema(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that an LLM run made with openai schema is correctly formatted."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "MyOAILLMRun",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "model": "my-fav-model",
                "prompt": "foo to the bar to the baz",
                "temperature": 0.75,
            },
            "outputs": {
                "choices": [
                    {
                        "text": "DEFG\n\nABCDFEG",
                        "index": 0,
                        "logprobs": None,
                        "finish_reason": "stop",
                    },
                ],
                "usage": {
                    "prompt_tokens": 3,
                    "completion_tokens": 1,
                    "total_tokens": 4,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.prompt_tokens == 3
    assert run.completion_tokens == 1
    assert run.total_tokens == 4


async def test_create_run_openai_schema_multi_call(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that an LLM run made with openai schema is correctly formatted
    when posted then patched."""
    run_id = uuid4()
    function_schema = {
        "name": "get_current_weather",
        "description": "Get the current weather in a given location",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city and state, e.g. San Francisco, CA",
                },
                "unit": {
                    "type": "string",
                    "enum": ["celsius", "fahrenheit"],
                },
            },
            "required": ["location"],
        },
    }
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "MyLLMRun",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": "You are a helpful agent"},
                    {"role": "user", "content": "What's the temperature in Reno?"},
                ],
                "temperature": 0,
                "functions": [function_schema],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    outputs = {
        "id": "chatcmpl-7lsqKDGhb6UDHiXS2IBZ81q0gAMVL",
        "object": "chat.completion",
        "created": 1691646588,
        "model": "gpt-3.5-turbo-0613",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": None,
                    "function_call": {
                        "name": "get_current_weather",
                        "arguments": '{\n  "location": "Reno, NV"\n}',
                    },
                },
                "finish_reason": "function_call",
            }
        ],
        "usage": {
            "prompt_tokens": 87,
            "completion_tokens": 19,
            "total_tokens": 106,
        },
    }

    response = await http_tenant_one.patch(
        f"/runs/{str(run_id)}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": outputs,
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id


async def test_create_run_openai_schema_unrecognized_output(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
) -> None:
    """Test that an LLM run made with openai schema is correctly formatted
    when posted then patched."""
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "MyLLMRun",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {},
            "inputs": {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": "You are a helpful agent"},
                    {"role": "user", "content": "What's the temperature in Reno?"},
                ],
                "temperature": 0,
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    outputs = {
        "YOU DONT KNOW ME": "foo",
        "OR ME": "bar",
    }

    response = await http_tenant_one.patch(
        f"/runs/{str(run_id)}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": outputs,
        },
    )
    assert response.status_code == 202
    await wait_for_runs_to_end(auth_tenant_one, run_id)

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.extra is not None
    # We should't touch this
    assert run.outputs == outputs


async def test_create_run_finished_tracer_session(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run cannot be created once a tracer session is 'ended'."""
    session_name = random_lower_string()
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": session_name},
    )
    session_obj = response.json()
    assert response.status_code == 200
    tracer_session_id = session_obj["id"]

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert str(run.session_id) == tracer_session_id
    # Check trace values
    assert run.trace_id == run_id
    assert run.dotted_order == f"20230505T051324571809Z{run_id}"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id}'",
    )
    assert row is not None

    # End the session
    response = await http_tenant_one.patch(
        f"/sessions/{tracer_session_id}",
        json={"end_time": "2023-05-05T05:13:32.022361+00:00"},
    )
    assert response.status_code == 200

    await asyncio.sleep(60)  # enough time for cache to clear

    run_id_2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:42.571809+00:00",
            "end_time": "2023-05-05T05:13:44.022361+00:00",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_2),
        },
    )

    assert response.status_code == 400


async def test_create_run_clickhouse_cycle_pre_queue(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run with a circular dependency is not created."""

    # Test vanilla post
    run_id1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "RootChain",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id1),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id1)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id1
    assert run.dotted_order == f"20230505T051324571809Z{run_id1}"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id1}'",
    )
    assert row is not None
    assert row["id"] == run_id1
    assert row["name"] == "RootChain"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 24, 571809)
    assert row["end_time"] == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert row["status"] == "success"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"input": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == orjson.dumps({"output": "39,566,248"}).decode("utf-8")
    assert row["total_tokens"] == 0

    run_id2, run_id3 = uuid4(), uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:25.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id3),
            "run_type": "llm",
            "id": str(run_id2),
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022350",
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id2}'",
    )
    assert row is None

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:25.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id2),
            "run_type": "llm",
            "id": str(run_id3),
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022350",
        },
    )
    assert response.status_code == 400
    await wait_until_task_queue_empty()

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id3}'",
    )
    assert row is None
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id2}'",
    )
    assert row is None


async def test_create_run_clickhouse(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be created and persisted in clickhouse."""

    # Test vanilla post
    run_id1 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "RootChain",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id1),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id1)
    assert run.session_id == tenant_one_tracer_session_id
    # Check trace values
    assert run.trace_id == run_id1
    assert run.dotted_order == f"20230505T051324571809Z{run_id1}"

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id1}'",
    )
    assert row is not None
    assert row["id"] == run_id1
    assert row["name"] == "RootChain"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 24, 571809)
    assert row["end_time"] == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert row["status"] == "success"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"input": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == orjson.dumps({"output": "39,566,248"}).decode("utf-8")
    assert row["total_tokens"] == 0

    run_id2 = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:25.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id1),
            "run_type": "llm",
            "id": str(run_id2),
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022350",
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id2}'",
    )
    assert row is not None
    assert row["id"] == run_id2
    assert row["name"] == "LLM"
    assert row["status"] == "success"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"prompt": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == orjson.dumps(
            {
                "generations": [{"text": "39,566,248"}],
            }
        ).decode("utf-8")
    assert row["total_tokens"] > 0
    assert row["total_tokens"] == row["prompt_tokens"] + row["completion_tokens"]
    leaf_run_total_tokens = row["total_tokens"]

    # Check that the root run's tokens are the sum of the leaf run's tokens
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id1}'",
    )
    assert row is not None
    assert row["id"] == run_id1
    assert row["name"] == "RootChain"
    assert row["status"] == "success"

    counts_row = await _fetch_propagated_token_rows(ch_client, run_id1)
    assert (
        counts_row["total_tokens"]
        == counts_row["prompt_tokens"] + counts_row["completion_tokens"]
    )
    assert counts_row["total_tokens"] == leaf_run_total_tokens

    # Test post and patch
    run_id3, run_id4 = uuid4(), uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id3),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id3}'",
    )
    assert row is not None
    assert row["id"] == run_id3
    assert row["name"] == "AgentExecutor"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 24, 571809)
    assert row["end_time"] is None
    assert row["status"] == "pending"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"input": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == "{}"

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:26.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "OpenAI"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": str(run_id3),
            "run_type": "llm",
            "id": str(run_id4),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    # Test that the run was written to Clickhouse
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id4}'",
    )
    assert row is not None
    assert row["id"] == run_id4
    assert row["name"] == "LLM"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 26, 571809)
    assert row["end_time"] is None
    assert row["status"] == "pending"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"prompt": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == "{}"
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] == 0
    assert row["total_tokens"] == row["prompt_tokens"]

    # Finish leaf run
    response = await http_tenant_one.patch(
        f"/runs/{run_id4}",
        json={
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
            },
            "end_time": "2023-05-05T05:13:32.022350",
        },
    )
    assert response.status_code == 202
    await wait_for_runs_to_end(auth_tenant_one, run_id4, timeout=60)

    # Test that the run was written to Clickhouse after update
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id4}'",
    )
    assert row is not None
    assert row["id"] == run_id4
    assert row["name"] == "LLM"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 26, 571809)
    assert row["end_time"] == datetime.datetime(2023, 5, 5, 5, 13, 32, 22350)
    assert row["status"] == "success"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"prompt": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == orjson.dumps(
            {
                "generations": [{"text": "39,566,248"}],
            }
        ).decode("utf-8")
    assert row["prompt_tokens"] > 0
    assert row["completion_tokens"] > 0
    assert row["total_tokens"] == row["prompt_tokens"] + row["completion_tokens"]

    # Test that the root run's tokens are not yet updated (it's not finished)
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id3}'",
    )
    assert row is not None
    assert row["id"] == run_id3
    assert row["prompt_tokens"] == 0
    assert row["completion_tokens"] == 0
    assert row["total_tokens"] == row["prompt_tokens"] + row["completion_tokens"]

    counts_row = await _fetch_propagated_token_rows(ch_client, run_id3)
    assert counts_row["prompt_tokens"] > 0
    assert counts_row["completion_tokens"] > 0
    assert (
        counts_row["total_tokens"]
        == counts_row["prompt_tokens"] + counts_row["completion_tokens"]
    )

    # Finish root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id3}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty(n_empty_threshold=6)  # type: ignore[call-arg]

    # Test that the run was written to Clickhouse after update
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id3}'",
    )
    row_leaf = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id4}'",
    )
    assert row is not None
    assert row["id"] == run_id3
    assert row["name"] == "AgentExecutor"
    assert row["start_time"] == datetime.datetime(2023, 5, 5, 5, 13, 24, 571809)
    assert row["end_time"] == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert row["status"] == "success"
    if not settings.FF_BLOB_STORAGE_ENABLED:
        assert row["inputs"] == orjson.dumps(
            {"input": "How many people live in canada as of 2023?"}
        ).decode("utf-8")
        assert row["outputs"] == orjson.dumps({"output": "39,566,248"}).decode("utf-8")

    assert row_leaf is not None
    assert row_leaf["id"] == run_id4
    assert (
        row_leaf["total_tokens"] > 0
        and row_leaf["total_tokens"]
        == row_leaf["prompt_tokens"] + row_leaf["completion_tokens"]
    )

    counts_row = await _fetch_propagated_token_rows(ch_client, run_id4)
    assert (
        counts_row["total_tokens"]
        == counts_row["prompt_tokens"] + counts_row["completion_tokens"]
    )
    assert counts_row["total_tokens"] == row_leaf["total_tokens"]


@PARAM_INGEST_ENDPOINT
@pytest.mark.parametrize(
    "exceeds_s3_threshold",
    [
        pytest.param(True, id="s3_exceeds_threshold"),
        pytest.param(False, id="s3_below_threshold"),
    ],
)
async def test_query_runs_clickhouse(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    exceeds_s3_threshold: bool,
) -> None:
    """Test that runs can be queried from clickhouse."""

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_tracer_session_id = new_session.id

    run_ids = [uuid4() for _ in range(10)]
    names = ["FooChain" for _ in range(5)] + ["BarChain" for _ in range(5)]
    metadata: list[dict[str, Any]] = [{"foo": "bar"} for _ in range(5)] + [
        {"bar": "foo"} for _ in range(5)
    ]

    inputs = {"input": "How many people live in czechia as of 2023?"}
    outputs = {"output": "110,498,692"}
    error = None
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        exceeds_s3_threshold = True
    if not settings.FF_BLOB_STORAGE_ENABLED:
        exceeds_s3_threshold = False

    if exceeds_s3_threshold and settings.FF_BLOB_STORAGE_ENABLED:
        large_data = "x" * 1024 * max(1, settings.MIN_BLOB_STORAGE_SIZE_KB)
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024 * max(1, settings.MIN_BLOB_STORAGE_SIZE_KB)
        for e in metadata:
            e["data"] = large_data

    run_data = [
        {
            "name": name,
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "metadata": meta,
                "runtime": {"library": "langsmith"},
                "excluded": {"foo": "bar"},
            },
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }
        for name, run_id, meta in zip(names, run_ids, metadata)
    ]

    if ingest_endpoint.startswith("/runs/multipart"):
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=run_data,
        )
    else:
        run_tasks = [http_tenant_one.post("/runs", json=data) for data in run_data]
        responses = await asyncio.gather(*run_tasks)
        for response in responses:
            assert response.status_code == 202, response.text

    await wait_until_task_queue_empty(timeout=120)  # type: ignore[call-arg]

    feedback_scores = [float(Decimal("0.1") * Decimal(i)) for i in range(10)]
    feedback_tasks = [
        http_tenant_one.post(
            "/feedback",
            json={
                "run_id": str(run_id),
                "key": "test_query_runs_clickhouse",
                "score": score,
                "feedback_source": {"type": "api"},
            },
        )
        for run_id, score in zip(run_ids, feedback_scores)
    ]
    responses = await asyncio.gather(*feedback_tasks)
    for response in responses:
        assert response.status_code in (200, 202), response.text
    await wait_until_task_queue_empty(timeout=120)  # type: ignore[call-arg]

    query_params: dict[str, Any]
    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id) for run_id in run_ids],
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 10

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id) for run_id in run_ids[:5]],
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "id": [str(run_id) for run_id in run_ids[5:]],
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "filter": 'and(eq(feedback_key, "test_query_runs_clickhouse"), gte(feedback_score, "0.45"))',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "filter": 'and(eq(feedback_key, "test_query_runs_clickhouse"), gte(feedback_score, "foo"))',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 400

    # filter on metadata
    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "ids": [str(run_id) for run_id in run_ids],
        "filter": 'has(metadata, \'{"foo":"bar"}\')',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "ids": [str(run_id) for run_id in run_ids],
        "filter": 'has(metadata, \'{"bar":"foo"}\')',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "ids": [str(run_id) for run_id in run_ids],
        "start_time": "2023-05-05T05:13:20",
        "end_time": "2023-05-05T05:13:30",
        "is_root": True,
        "filter": 'and(has(metadata, \'{"bar":"foo"}\'), and(eq(feedback_key, "test_query_runs_clickhouse"), gte(feedback_score, "0.45")))',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 5

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "ids": [str(run_id) for run_id in run_ids],
        "filter": 'and(has(metadata, \'{"foo":"bar"}\'), and(eq(feedback_key, "test_query_runs_clickhouse"), gte(feedback_score, "0.45")))',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 0

    # check that items for search in extra are also in clickhouse even if going to s3
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_ids[0]}'",
    )
    assert row is not None
    extra = orjson.loads(row["extra"])

    assert extra.get("runtime") == {"library": "langsmith"}


@PARAM_INGEST_ENDPOINT
@pytest.mark.parametrize(
    "exceeds_s3_threshold",
    [
        pytest.param(True, id="s3_exceeds_threshold"),
        pytest.param(False, id="s3_below_threshold"),
    ],
)
async def test_extra_clickhouse_ingest(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    exceeds_s3_threshold: bool,
) -> None:
    """Test that runs can be queried from clickhouse."""

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_tracer_session_id = new_session.id

    run_ids = [uuid4() for _ in range(10)]
    names = ["FooChain" for _ in range(5)] + ["BarChain" for _ in range(5)]
    metadata: list[dict[str, Any]] = [{"foo": "bar"} for _ in range(5)] + [
        {"bar": "foo"} for _ in range(5)
    ]

    inputs = {"input": "How many people live in czechia as of 2023?"}
    outputs = {"output": "110,498,692"}
    error = None
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        exceeds_s3_threshold = True
    if not settings.FF_BLOB_STORAGE_ENABLED:
        exceeds_s3_threshold = False

    if exceeds_s3_threshold:
        large_data = "x" * 1024 * max(1, settings.MIN_BLOB_STORAGE_SIZE_KB)
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024 * max(1, settings.MIN_BLOB_STORAGE_SIZE_KB)

        for i, m in enumerate(metadata):
            m["data"] = large_data
            m["nested"] = {"nested": "metadata"}
            m["array"] = [{"array": "metadata"}]
            if i == 1:
                for j in range(settings.MAX_EXTRA_KEYS):
                    m[f"key_{j}"] = "a"

    run_data = [
        {
            "name": name,
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "metadata": meta,
                "runtime": {"library": "langsmith"},
                "excluded": {"foo": "bar"},
            },
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }
        for name, run_id, meta in zip(names, run_ids, metadata)
    ]

    if ingest_endpoint.startswith("/runs/multipart"):
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=run_data,
        )
    else:
        run_tasks = [http_tenant_one.post("/runs", json=data) for data in run_data]
        responses = await asyncio.gather(*run_tasks)
        for response in responses:
            assert response.status_code == 202, response.text

    await wait_until_task_queue_empty(timeout=120)  # type: ignore[call-arg]

    # check that items for search in extra are also in clickhouse even if going to s3
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id ='{run_ids[0]}'",
    )
    assert row is not None
    extra = orjson.loads(row["extra"])

    assert extra.get("runtime") == {"library": "langsmith"}

    if exceeds_s3_threshold:
        expected_metadata = {
            k: v
            for k, v in metadata[0].items()
            if k != "data" and k != "nested" and k != "array"
        }
        expected_metadata["ls_run_depth"] = 0
        assert extra.get("metadata") == dict(expected_metadata)
    else:
        expected_metadata = copy.deepcopy(metadata[0])
        expected_metadata["ls_run_depth"] = 0
        assert extra.get("metadata") == expected_metadata

    if exceeds_s3_threshold:
        assert extra.get("excluded") is None
    else:
        assert extra.get("excluded") is not None

    # check that items for search in extra are also in clickhouse even if going to s3
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_ids[1]}'",
    )
    assert row is not None
    extra = orjson.loads(row["extra"])
    assert extra is not None
    if exceeds_s3_threshold:
        assert len(extra.get("metadata")) == settings.MAX_EXTRA_KEYS


async def test_create_run_token_counts(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test edge cases for token counts"""
    # no tokens provided
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    # cl100k_base encoding of input and output
    assert run.prompt_tokens == 12
    assert run.completion_tokens == 5
    assert run.total_tokens == 17

    # no completion tokens provided
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 599
    assert run.completion_tokens == 0
    assert run.total_tokens == 599

    # camelCase keys (sent by JS libraries)
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llmOutput": {
                    "tokenUsage": {
                        "promptTokens": 599,
                        "completionTokens": 401,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 599
    assert run.completion_tokens == 401
    assert run.total_tokens == 1000


async def test_create_run_token_counts_unified(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test cases for token counts in unified schema"""
    # token counts in root
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 10
    assert run.completion_tokens == 20
    assert run.total_tokens == 30

    # in langchain generation > message
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [
                    [
                        {
                            "text": "39,566,248",
                            "type": "ChatGeneration",
                            "message": {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "usage_metadata": {
                                        "input_tokens": 10,
                                        "output_tokens": 20,
                                        "total_tokens": 30,
                                    }
                                },
                            },
                        }
                    ]
                ],
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 10
    assert run.completion_tokens == 20
    assert run.total_tokens == 30

    # in langchain message as kwarg
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "output": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "AIMessage"],
                    "kwargs": {
                        "usage_metadata": {
                            "input_tokens": 10,
                            "output_tokens": 20,
                            "total_tokens": 30,
                        }
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 10
    assert run.completion_tokens == 20
    assert run.total_tokens == 30

    # exposed kwargs
    # don't think it's actually sent, but just in case
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain", "schema", "messages", "AIMessage"],
                "kwargs": {
                    "usage_metadata": {
                        "input_tokens": 10,
                        "output_tokens": 20,
                        "total_tokens": 30,
                    }
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 10
    assert run.completion_tokens == 20
    assert run.total_tokens == 30

    # no batch size provided
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.prompt_tokens == 10
    assert run.completion_tokens == 20
    assert run.total_tokens == 30


async def test_create_run_token_costs(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test cases for token costs"""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "invocation_params": {"model": "gpt-3.5-turbo"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    model_price_map_id = await db_asyncpg.fetchval(
        """
        SELECT id FROM model_price_map
        WHERE name = 'gpt-3.5-turbo' AND start_time is NULL
        LIMIT 1"""
    )

    assert model_price_map_id is not None
    assert run.price_model_id == model_price_map_id

    assert run.prompt_tokens == 599
    assert run.prompt_cost == Decimal("0.0000015") * run.prompt_tokens

    assert run.completion_tokens == 101
    assert run.completion_cost == Decimal("0.000002") * run.completion_tokens

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost

    # unknown model provided
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "invocation_params": {"model": "gpt-10"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    assert run.price_model_id is None

    assert run.prompt_tokens == 599
    assert run.prompt_cost is None

    assert run.completion_tokens == 101
    assert run.completion_cost is None

    assert run.total_tokens == 700
    assert run.total_cost is None

    # newer model overridden by model name
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "invocation_params": {"model": "gpt-3.5-turbo-0125"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    model_price_map_id = await db_asyncpg.fetchval(
        """
        SELECT id FROM model_price_map
        WHERE name = 'gpt-3.5-turbo-0125'
        LIMIT 1"""
    )

    assert model_price_map_id is not None
    assert run.price_model_id == model_price_map_id

    assert run.prompt_tokens == 599
    assert run.prompt_cost == Decimal("0.0000005") * run.prompt_tokens

    assert run.completion_tokens == 101
    assert run.completion_cost == Decimal("0.0000015") * run.completion_tokens

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost

    # newer model overridden by date
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2024-02-16T05:13:24.591809",
            "end_time": "2024-02-16T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "invocation_params": {"model": "gpt-3.5-turbo"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    model_price_map_id = await db_asyncpg.fetchval(
        """
        SELECT id FROM model_price_map
        WHERE name = 'gpt-3.5-turbo' AND start_time = '2024-02-16'
        LIMIT 1"""
    )

    assert model_price_map_id is not None
    assert run.price_model_id == model_price_map_id

    assert run.prompt_tokens == 599
    assert run.prompt_cost == Decimal("0.0000005") * run.prompt_tokens

    assert run.completion_tokens == 101
    assert run.completion_cost == Decimal("0.0000015") * run.completion_tokens

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost

    # no model provided
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    assert run.price_model_id is None

    assert run.prompt_tokens == 599
    assert run.prompt_cost is None

    assert run.completion_tokens == 101
    assert run.completion_cost is None

    assert run.total_tokens == 700
    assert run.total_cost is None

    # model provided in invocation params
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"invocation_params": {"model": "gpt-4o"}, "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["Tohle je čeština v plné kráse!"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    # o200k_base encoding of input and output
    assert run.prompt_tokens == 13
    assert run.completion_tokens == 5
    assert run.total_tokens == 18

    # model provided in metadata
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"metadata": {"ls_model_name": "gpt-4o"}, "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["Tohle je čeština v plné kráse!"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    # o200k_base encoding of input and output
    assert run.prompt_tokens == 13
    assert run.completion_tokens == 5
    assert run.total_tokens == 18


@pytest.mark.parametrize(
    "model",
    [
        "gemini-2.5-pro-preview-03-25",
        "models/gemini-2.5-pro-preview-03-25",
    ],
)
async def test_create_run_token_costs_gemini(
    model: str,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test cases for token costs"""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "invocation_params": {"model": model},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    model_price_map_id = await db_asyncpg.fetchval(
        """
        SELECT id FROM model_price_map
        WHERE name = 'gemini-2.5-pro-preview-03-25' AND start_time is NULL
        LIMIT 1"""
    )

    assert model_price_map_id is not None
    assert run.price_model_id == model_price_map_id

    assert run.prompt_tokens == 599
    assert run.prompt_cost == Decimal("0.00000125") * run.prompt_tokens

    assert run.completion_tokens == 101
    assert run.completion_cost == Decimal("0.00001") * run.completion_tokens

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost


@pytest.mark.flaky
async def test_create_run_token_costs_from_metadata(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test cases for token costs using model name and provider inferred from metadata"""

    # metadata now being sent, but using the old model name
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "metadata": {"ls_model_name": "gpt-4o", "ls_provider": "openai"},
                "invocation_params": {"model": "gpt-4o"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    model_price_map = await db_asyncpg.fetchrow(
        """
        SELECT * FROM model_price_map
        WHERE name = 'gpt-4o' AND start_time is NULL
        LIMIT 1"""
    )

    assert model_price_map is not None
    assert run.price_model_id == model_price_map["id"]
    assert run.prompt_tokens == 599
    assert run.prompt_cost == model_price_map["prompt_cost"] * run.prompt_tokens

    assert run.completion_tokens == 101
    assert (
        run.completion_cost
        == model_price_map["completion_cost"] * run.completion_tokens
    )

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost

    # create new dummy model price map with provider known
    new_model_price_map = await db_asyncpg.fetchrow(
        """
        INSERT INTO model_price_map(priority_order, name, provider, match_pattern, prompt_cost, completion_cost)
        VALUES (999, 'azure-test', 'azure', '^gpt-4o?$', 0.000003, 0.000004)
        RETURNING *
        """
    )

    # blow out the redis cache
    async with redis.aredis_pool() as aredis:
        keys = await aredis.keys("*_model_price_map*")
        for key in keys:
            await aredis.delete(key)

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "metadata": {"ls_model_name": "gpt-4o", "ls_provider": "azure"},
                "invocation_params": {"model": "gpt-4o"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)

    assert new_model_price_map is not None
    assert run.price_model_id == new_model_price_map["id"]

    assert run.prompt_tokens == 599
    assert run.prompt_cost == new_model_price_map["prompt_cost"] * run.prompt_tokens

    assert run.completion_tokens == 101
    assert (
        run.completion_cost
        == new_model_price_map["completion_cost"] * run.completion_tokens
    )

    assert run.total_tokens == 700
    assert run.total_cost == run.prompt_cost + run.completion_cost

    # test with malformed model name
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "metadata": {"ls_model_name": 1234, "ls_provider": "azure"},
                "invocation_params": {"model": "gpt-4o"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.price_model_id is None
    assert run.prompt_tokens == 599
    assert run.prompt_cost is None
    assert run.completion_tokens == 101
    assert run.completion_cost is None
    assert run.total_tokens == 700
    assert run.total_cost is None


async def test_create_run_with_cost_details_integration(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test integration between model_price_map cost_details and run cost calculations."""

    # Create a model price map with detailed cost breakdowns
    model_price_map = await db_asyncpg.fetchrow(
        """
        INSERT INTO model_price_map(
            priority_order, 
            name, 
            provider, 
            match_pattern, 
            prompt_cost, 
            completion_cost,
            prompt_cost_details,
            completion_cost_details
        )
        VALUES (
            9999, 
            'test-detailed-cost-model', 
            'test-provider', 
            '^test-detailed-model$', 
            0.003,
            0.006,
            '{"input_tokens": "0.001", "cached_tokens": "0.0005"}',
            '{"output_tokens": "0.002", "reasoning_tokens": "0.004"}'
        )
        RETURNING *
        """
    )

    # Clear Redis cache to ensure fresh model price map lookup
    async with redis.aredis_pool() as aredis:
        keys = await aredis.keys("*_model_price_map*")
        for key in keys:
            await aredis.delete(key)

    # Create a run with detailed token usage that matches our price map structure
    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM_Detailed_Cost_Test",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "metadata": {
                    "ls_model_name": "test-detailed-model",
                    "ls_provider": "test-provider",
                },
                "invocation_params": {"model": "test-detailed-model"},
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "TestLLM"},
            "inputs": {"prompts": ["Test prompt for detailed cost calculation"]},
            "outputs": {
                "generations": [
                    [{"text": "Test response with detailed cost breakdown"}]
                ],
                "usage_metadata": {
                    "input_tokens": 150,  # 100 input + 50 cached
                    "output_tokens": 100,  # 75 output + 25 reasoning
                    "input_token_details": {
                        "input_tokens": 100,
                        "cached_tokens": 50,
                    },
                    "output_token_details": {
                        "output_tokens": 75,
                        "reasoning_tokens": 25,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # Fetch the run and verify cost calculations
    run = await crud.get_run(auth_tenant_one, run_id)

    # Verify the model price map was matched correctly
    assert run.price_model_id == model_price_map["id"]

    # Verify token counts
    assert run.prompt_tokens == 150
    assert run.completion_tokens == 100
    assert run.total_tokens == 250

    # Calculate expected costs based on our price map:
    # Prompt cost: (100 * 0.001) + (50 * 0.0005) = 0.1 + 0.025 = 0.125
    expected_prompt_cost = Decimal("0.125")
    # Completion cost: (75 * 0.002) + (25 * 0.004) = 0.15 + 0.1 = 0.25
    expected_completion_cost = Decimal("0.25")
    # Total cost: 0.125 + 0.25 = 0.375
    expected_total_cost = Decimal("0.375")

    # Verify calculated costs
    assert run.prompt_cost == expected_prompt_cost
    assert run.completion_cost == expected_completion_cost
    assert run.total_cost == expected_total_cost

    # Verify cost details were calculated and stored correctly
    assert run.prompt_cost_details is not None
    assert run.completion_cost_details is not None

    # Parse cost details from JSON
    prompt_cost_breakdown = run.prompt_cost_details
    completion_cost_breakdown = run.completion_cost_details

    # Verify prompt cost breakdown
    expected_prompt_breakdown = {
        "input_tokens": Decimal("0.1"),  # 100 * 0.001
        "cached_tokens": Decimal("0.025"),  # 50 * 0.0005
    }
    assert len(prompt_cost_breakdown) == 2
    assert (
        Decimal(prompt_cost_breakdown["input_tokens"])
        == expected_prompt_breakdown["input_tokens"]
    )
    assert (
        Decimal(prompt_cost_breakdown["cached_tokens"])
        == expected_prompt_breakdown["cached_tokens"]
    )

    # Verify completion cost breakdown
    expected_completion_breakdown = {
        "output_tokens": Decimal("0.15"),  # 75 * 0.002
        "reasoning_tokens": Decimal("0.1"),  # 25 * 0.004
    }
    assert len(completion_cost_breakdown) == 2
    assert (
        Decimal(completion_cost_breakdown["output_tokens"])
        == expected_completion_breakdown["output_tokens"]
    )
    assert (
        Decimal(completion_cost_breakdown["reasoning_tokens"])
        == expected_completion_breakdown["reasoning_tokens"]
    )

    # Verify the cost breakdown sums match the total costs
    prompt_breakdown_sum = sum(Decimal(v) for v in prompt_cost_breakdown.values())
    completion_breakdown_sum = sum(
        Decimal(v) for v in completion_cost_breakdown.values()
    )

    assert prompt_breakdown_sum == expected_prompt_cost
    assert completion_breakdown_sum == expected_completion_cost


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@PARAM_INGEST_ENDPOINT
async def test_run_stats(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    tenant_two_tracer_session_id: UUID,
    http_tenant_two: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    snapshot: SnapshotAssertion,
) -> None:
    """Test edge cases for token counts"""
    # no tokens provided

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_custom_tracer_session_id = new_session.id

    # Make sure runs stats succeeds on empty session
    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
        },
    )
    assert response.status_code == 200
    assert response.json()["completion_tokens"] == 0
    assert response.json()["prompt_tokens"] == 0
    assert response.json()["total_tokens"] == 0
    assert response.json()["median_tokens"] == 0
    assert response.json()["completion_tokens_p50"] == 0
    assert response.json()["prompt_tokens_p50"] == 0
    assert response.json()["tokens_p99"] == 0
    assert response.json()["completion_tokens_p99"] == 0
    assert response.json()["prompt_tokens_p99"] == 0

    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()
    run_id_5 = uuid4()
    run_id_6 = uuid4()
    post = [
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar", "metadata": {"mkey": 1}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "metadata": {"mkey": 1}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
        },
        {
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "batch_size": 1,
                "metadata": {
                    "ls_model_name": "gpt-4o",
                    "ls_provider": "openai",
                    "mkey": 2,
                },
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 401,
                        "total_tokens": 1000,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}",
        },
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "metadata": {
                    "ls_model_name": "gpt-4o",
                    "ls_provider": "openai",
                    "mkey": 2,
                },
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 200,
                        "completion_tokens": 300,
                        "total_tokens": 500,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
        },
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "foo": "bar",
                "metadata": {
                    "ls_model_name": "gpt-4o",
                    "ls_provider": "openai",
                    "mkey": 2,
                },
            },
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": "How many people live in canada as of 2023?"},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 200,
                        "completion_tokens": 300,
                        "total_tokens": 500,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "llm",
            "id": str(run_id_5),
            "trace_id": str(run_id_5),
            "dotted_order": f"20230505T051324571809Z{run_id_5}",
        },
        {
            # Will be ignored in group by name queries
            "name": "RunnableSequence",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar", "metadata": {"mkey": 1}},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "RunnableSequence"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_6),
            "trace_id": str(run_id_6),
            "dotted_order": f"20230505T051324571809Z{run_id_6}",
        },
    ]
    patch = [
        {
            "id": str(run_id_1),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        }
    ]

    await post_runs(ingest_endpoint, http_tenant_one, post, patch)

    # add another tenant just to test multi-tenancy isolation
    tenant_two_run_id = uuid4()
    response = await http_tenant_two.post(
        "/runs/batch",
        json={
            "post": [
                {
                    "name": "AgentExecutor",
                    "start_time": "2023-05-05T05:13:24.571809",
                    "end_time": "2023-05-05T05:13:32.022361",
                    "extra": {
                        "foo": "bar",
                        "secret": "extra",
                        "metadata": {"secret": "metadata", "mkey": 1},
                    },
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {
                        "secret": "inputs",
                        "input": "How many people live in canada as of 2023?",
                    },
                    "outputs": {"secret": "outputs", "output": "39,566,248"},
                    "session_id": str(tenant_two_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(tenant_two_run_id),
                    "trace_id": str(tenant_two_run_id),
                    "dotted_order": f"20230505T051324571809Z{tenant_two_run_id}",
                }
            ],
            "patch": [],
        },
    )
    assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "foo",
            "score": 100,
            "value": "green",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_1),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "foo",
            "score": 0,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "bar",
            "score": 0,
            "value": "red",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "bar",
            "score": 100,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "bar",
            "score": 100,
            "value": "red",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "bar",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": str(run_id_6),
            "key": "foo",
            "score": 100,
            "value": "blue",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    # tenant_two feedback for multi tenancy test
    response = await http_tenant_two.post(
        "/feedback",
        json={
            "run_id": str(tenant_two_run_id),
            "key": "secret",
            "score": 100,
            "value": "hush",
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # Test with runs join
    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": True,
            "run_type": "chain",  # add this so we test the join
        },
    )
    assert response.status_code == 200
    assert "secret" not in orjson.dumps(response.json()).decode("utf-8"), (
        "IMPORTANT!! Should not contain 'secret' text, which is from another tenant!"
    )
    assert response.json()["completion_tokens"] == 701
    assert response.json()["prompt_tokens"] == 799
    assert response.json()["total_tokens"] == 1500
    assert response.json()["run_count"] == 2
    assert response.json()["streaming_rate"] == 0.0
    assert response.json()["last_run_start_time"] == "2023-05-05T05:13:24.571809"
    assert response.json()["latency_p50"] == 7.451
    assert response.json()["latency_p99"] == 7.451
    feedback_stats = response.json()["feedback_stats"]
    assert feedback_stats
    assert feedback_stats["foo"], feedback_stats
    assert feedback_stats["foo"]["values"] == {"blue": 4, "green": 1}, feedback_stats
    assert feedback_stats["foo"]["n"] == 5
    assert feedback_stats["foo"]["avg"] == 60.0
    assert math.isclose(feedback_stats["foo"]["stdev"], 48.989794855664, rel_tol=1e-9)

    assert feedback_stats["bar"]["values"] == {"blue": 1, "red": 2}
    assert feedback_stats["bar"]["n"] == 4
    assert feedback_stats["bar"]["avg"] == 75.0
    assert math.isclose(feedback_stats["bar"]["stdev"], 43.301270189222, rel_tol=1e-9)

    # check input / output run facets
    run_facets = response.json()["run_facets"]
    assert run_facets is not None
    assert {
        "key": "input_key",
        "query": 'eq(input_key, "input")',
        "value": "input",
    } in run_facets
    assert {
        "key": "output_key",
        "query": 'eq(output_key, "output")',
        "value": "output",
    } in run_facets

    # Ensure that the same answer is given when querying against the runs history table
    # This will be true until we enforce TTLs
    history_response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": True,
            "data_source_type": "historical",
            "run_type": "chain",
        },
    )
    assert history_response.status_code == 200
    # compare history with runs response, but sort the facets response due to ordering
    assert sorted(
        history_response.json()["run_facets"], key=lambda x: x["query"]
    ) == sorted(response.json()["run_facets"], key=lambda x: x["query"])
    assert {k: v for k, v in history_response.json().items() if k != "run_facets"} == {
        k: v for k, v in response.json().items() if k != "run_facets"
    }

    # test skipping runs join
    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": True,
            "id": [str(run_id_1)],
        },
    )
    assert response.status_code == 200
    assert response.json()["completion_tokens"] == 701
    assert response.json()["prompt_tokens"] == 799
    assert response.json()["total_tokens"] == 1500
    assert response.json()["completion_tokens_p50"] == 701
    assert response.json()["prompt_tokens_p50"] == 799
    assert response.json()["median_tokens"] == 1500
    assert response.json()["completion_tokens_p99"] == 701
    assert response.json()["prompt_tokens_p99"] == 799
    assert response.json()["tokens_p99"] == 1500
    assert response.json()["run_count"] == 1
    assert response.json()["streaming_rate"] == 0.0
    assert response.json()["last_run_start_time"] == "2023-05-05T05:13:24.571809"
    assert response.json()["latency_p50"] == 7.451
    assert response.json()["latency_p99"] == 7.451
    feedback_stats = response.json()["feedback_stats"]
    assert feedback_stats["foo"]["values"] == {"blue": 2}
    assert feedback_stats["foo"]["n"] == 2
    assert feedback_stats["foo"]["avg"] == 50.0
    assert feedback_stats["foo"]["stdev"] == 50.0
    assert "secret" not in orjson.dumps(response.json()).decode("utf-8"), (
        "IMPORTANT!! Should not contain 'secret' text, which is from another tenant!"
    )

    # assert the same answer is given against the runs history table
    # This will be true until we enforce TTLs
    history_response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": True,
            "id": [str(run_id_1)],
            "data_source_type": "historical",
        },
    )
    assert history_response.status_code == 200
    # compare history with runs response, but sort the facets response due to ordering
    assert sorted(
        history_response.json()["run_facets"], key=lambda x: x["query"]
    ) == sorted(response.json()["run_facets"], key=lambda x: x["query"])
    assert {k: v for k, v in history_response.json().items() if k != "run_facets"} == {
        k: v for k, v in response.json().items() if k != "run_facets"
    }

    # test is_root False
    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": False,
        },
    )
    assert response.status_code == 200
    assert response.json()["completion_tokens"] == 701
    assert response.json()["prompt_tokens"] == 799
    assert response.json()["total_tokens"] == 1500
    assert response.json()["completion_tokens_p50"] == 350
    assert response.json()["prompt_tokens_p50"] == 399
    assert response.json()["median_tokens"] == 750
    assert response.json()["completion_tokens_p99"] == 399
    assert response.json()["prompt_tokens_p99"] == 595
    assert response.json()["tokens_p99"] == 995
    assert response.json()["run_count"] == 3
    assert "secret" not in orjson.dumps(response.json()).decode("utf-8"), (
        "IMPORTANT!! Should not contain 'secret' text, which is from another tenant!"
    )

    # test topk agg merge tree
    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
            "is_root": True,
        },
    )
    assert response.status_code == 200
    assert response.json()["completion_tokens"] == 1001
    assert response.json()["prompt_tokens"] == 999
    assert response.json()["total_tokens"] == 2000
    assert response.json()["completion_tokens_p50"] == 500
    assert response.json()["prompt_tokens_p50"] == 499
    assert response.json()["median_tokens"] == 1000
    assert response.json()["completion_tokens_p99"] == 696
    assert response.json()["prompt_tokens_p99"] == 793
    assert response.json()["tokens_p99"] == 1490
    run_facets = response.json()["run_facets"]
    assert run_facets is not None
    assert {
        "key": "input_key",
        "query": 'eq(input_key, "input")',
        "value": "input",
    } in run_facets
    assert {
        "key": "output_key",
        "query": 'eq(output_key, "output")',
        "value": "output",
    } in run_facets
    assert {
        "key": "name",
        "query": 'eq(name, "AgentExecutor")',
        "value": "AgentExecutor",
    } in run_facets
    assert {
        "key": "input_key_value",
        "value": 'input == "how many people live in canada as of 2023?"',
        "query": "and(eq(input_key, 'input'), eq(input_value, \"how many people live in canada as of 2023?\"))",
    } in run_facets
    assert {
        "key": "output_key_value",
        "value": 'output == "39,566,248"',
        "query": "and(eq(output_key, 'output'), eq(output_value, \"39,566,248\"))",
    } in run_facets

    # Test group by metadata
    common = {
        "session": [str(tenant_one_custom_tracer_session_id)],
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-15T05:13:32.022361",
    }
    queries: dict[str, dict] = {
        "group_by_metadata": {
            "group_by": {"attribute": "metadata", "path": "mkey"},
        },
        "group_by_name": {
            "group_by": {"attribute": "name"},
        },
        "group_by_tags": {
            "group_by": {"attribute": "tag"},
        },
        "group_by_run_type": {
            "group_by": {"attribute": "run_type"},
        },
        "run_type_chain_group_by_metadata": {
            "group_by": {"attribute": "metadata", "path": "mkey"},
            "run_type": "chain",  # test with run join
        },
        "is_root_group_by_name": {
            "group_by": {"attribute": "name"},
            "is_root": True,
        },
        "is_root_run_type_chain_group_by_name": {
            "group_by": {"attribute": "name"},
            "run_type": "chain",
            "is_root": True,
        },
    }
    for name, request in queries.items():
        response = await http_tenant_one.post("/runs/stats", json={**common, **request})
        assert response.status_code == 200
        actual = response.json()
        serialized = orjson.dumps(actual).decode("utf-8")
        assert "secret" not in serialized, (
            "IMPORTANT!! Should not contain 'secret' text, which is from another tenant!"
        )
        # Grouped queries are no longer perfectly accurate, so some non-deterministic
        # variation is possible. We keep around the snapshots for reference as to what
        # the results are supposed to look like.
        try:
            assert actual == snapshot(name=name, matcher=_json_matcher), name
        except AssertionError as e:
            print(f"Snapshot failed: {e}")
        for stats in actual.values():
            for k in (
                "total_tokens",
                "completion_tokens",
                "prompt_tokens",
                "completion_cost",
                "prompt_cost",
                "total_cost",
                "median_tokens",
            ):
                stats[k] = stats[k] or 0
            assert (
                stats["total_tokens"]
                == stats["completion_tokens"] + stats["prompt_tokens"]
            ), name + ": total token consistency"
            assert not bool(stats["total_tokens"]) ^ bool(stats["total_cost"]), name
            assert not bool(stats["completion_tokens"]) ^ bool(
                stats["completion_cost"]
            ), name
            assert not bool(stats["prompt_tokens"]) ^ bool(stats["prompt_cost"]), name
            assert (
                stats["total_cost"] == stats["completion_cost"] + stats["prompt_cost"]
            ), name
            if stats["run_count"] == 1:
                assert stats["median_tokens"] == stats["total_tokens"], (
                    name + ": median token consistency"
                )


def _json_matcher(data: Any, path: tuple) -> Any:
    if isinstance(data, dict):
        return {k: data[k] for k in sorted(data)}
    elif isinstance(data, list):
        return sorted(data, key=lambda x: orjson.dumps(x))
    else:
        return data


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@PARAM_INGEST_ENDPOINT
async def test_run_stats_uint64_tokens(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test case where total tokens exceeds uint32."""
    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_custom_tracer_session_id = new_session.id

    run_ids = [str(uuid4()) for _ in range(2)]
    post = [
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "execution_order": 1,
            "inputs": {},
            "extra": {},
            "error": None,
            "serialized": None,
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "llm",
            "id": run_id,
            "trace_id": run_id,
            "dotted_order": f"20230505T051324571809Z{run_id}",
        }
        for run_id in run_ids
    ]
    patch = [
        {
            "end_time": "2023-05-05T05:13:25.571809",
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    # individual runs with > 4B tokens get ignored in stats query
                    "token_usage": {
                        "prompt_tokens": 2_000_000_000,
                        "completion_tokens": 1_999_999_999,
                        "total_tokens": 3_999_999_999,
                    },
                },
            },
            "id": run_id,
            "trace_id": run_id,
            "dotted_order": f"20230505T051324571809Z{run_id}",
        }
        for run_id in run_ids
    ]
    await post_runs(ingest_endpoint, http_tenant_one, post, patch)
    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-15T05:13:32.022361",
        },
    )
    assert response.status_code == 200
    assert response.json()["run_count"] == 2
    assert response.json()["total_tokens"] == 7_999_999_998
    assert response.json()["prompt_tokens"] == 4_000_000_000
    assert response.json()["completion_tokens"] == 3_999_999_998


def test_run_stats_endpoint_consistency() -> None:
    """Assert that the multipart and batch runs ingestion endpoints yield same stats."""
    with open(CUR_DIR / "__snapshots__/test_runs.ambr", "r") as f:
        snapshot = f.read()

    header_pattern = re.compile(r"^# name:\s*(.+)$", re.MULTILINE)
    test_cases = list(header_pattern.finditer(snapshot))
    results: dict = defaultdict(dict)

    for i, test_case in enumerate(test_cases):
        full_header = test_case.group(
            1
        ).strip()  # e.g. "test_run_stats[/runs/batch|go][group_by_metadata]"

        start_index = test_case.end()
        end_index = (
            test_cases[i + 1].start() if i + 1 < len(test_cases) else len(snapshot)
        )
        test_snapshot = snapshot[start_index:end_index].strip()
        endpoint, query = [x.strip("]") for x in full_header.split("[")[1:3]]
        results[query][endpoint] = test_snapshot

    failed = []
    for query, snapshots in results.items():
        if len(set(snapshots.values())) != 1:
            snapshot_lines = {
                param: snap.split("\n") for param, snap in snapshots.items()
            }
            max_len = max(len(sl) for sl in snapshot_lines.values())
            diff = {
                i: {
                    param: snap_lines[i].strip()
                    for param, snap_lines in snapshot_lines.items()
                }
                for i in range(max_len)
                if len(set(sl[i] for sl in snapshot_lines.values())) > 1
            }
            failed.append((query, diff))
    assert not failed


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@PARAM_INGEST_ENDPOINT
@pytest.mark.parametrize(
    "patch_min_max_time_filtered_query", [False, True], indirect=True
)
async def test_runs_stats_with_min_max_time_cte(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    patch_min_max_time_filtered_query: bool,
) -> None:
    """Test runs stats with min max time cte"""

    with patch("app.models.runs.stats.settings.STATS_MAX_RANGE_MINUTES", None):
        new_session = await crud.create_tracer_session(
            auth_tenant_one, schemas.TracerSessionCreate()
        )
        tenant_one_custom_tracer_session_id = new_session.id

        run_id_1 = uuid4()
        run_id_2 = uuid4()
        run_id_3 = uuid4()
        run_id_4 = uuid4()
        run_id_5 = uuid4()
        run_id_6 = uuid4()
        post = [
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_id": str(tenant_one_custom_tracer_session_id),
                "run_type": "chain",
                "id": str(run_id_1),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}",
            },
            {
                "name": "LLMChain",
                "start_time": "2023-05-05T05:13:24.581809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(tenant_one_custom_tracer_session_id),
                "parent_run_id": str(run_id_1),
                "run_type": "chain",
                "id": str(run_id_2),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
            },
            {
                "name": "LLM1",
                "start_time": "2023-05-05T05:13:24.591809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {
                    "foo": "bar",
                    "batch_size": 1,
                    "metadata": {
                        "ls_model_name": "gpt-4o",
                        "ls_provider": "openai",
                    },
                },
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"prompts": ["How many people live in canada as of 2025?"]},
                "outputs": {
                    "generations": [[{"text": "49,566,249"}]],
                    "llm_output": {
                        "token_usage": {
                            "prompt_tokens": 599,
                            "completion_tokens": 401,
                            "total_tokens": 1000,
                        },
                    },
                },
                "session_id": str(tenant_one_custom_tracer_session_id),
                "parent_run_id": str(run_id_2),
                "run_type": "llm",
                "id": str(run_id_3),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}",
            },
            {
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.601809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {
                    "foo": "bar",
                    "metadata": {
                        "ls_model_name": "gpt-4o",
                        "ls_provider": "openai",
                    },
                },
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
                "outputs": {
                    "generations": [[{"text": "39,566,248"}]],
                    "llm_output": {
                        "token_usage": {
                            "prompt_tokens": 200,
                            "completion_tokens": 300,
                            "total_tokens": 500,
                        },
                    },
                },
                "session_id": str(tenant_one_custom_tracer_session_id),
                "parent_run_id": str(run_id_2),
                "run_type": "llm",
                "id": str(run_id_4),
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
            },
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_id": str(tenant_one_custom_tracer_session_id),
                "run_type": "chain",
                "id": str(run_id_5),
                "trace_id": str(run_id_5),
                "dotted_order": f"20230505T051324571809Z{run_id_5}",
            },
            {
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_id": str(tenant_one_custom_tracer_session_id),
                "run_type": "chain",
                "id": str(run_id_6),
                "trace_id": str(run_id_6),
                "dotted_order": f"20230505T051324571809Z{run_id_6}",
            },
        ]
        patched_runs = [
            {
                "id": str(run_id_1),
                "end_time": "2023-05-05T05:13:32.022361",
                "outputs": {"output": "39,566,248"},
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}",
            }
        ]

        await post_runs(ingest_endpoint, http_tenant_one, post, patched_runs)

        await wait_until_task_queue_empty()

        #### Test metadata key value filter ####

        response = await http_tenant_one.post(
            "/runs/stats",
            json={
                "session": [str(tenant_one_custom_tracer_session_id)],
                "start_time": "2023-05-05T05:13:24.571809",
                "filter": 'and(eq(metadata_key, "ls_model_name"), eq(metadata_value, "gpt-4o"), eq(name, "LLM1"))',
            },
        )
        assert response.status_code == 200

        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["total_tokens"] == 1000
        assert response.json()["run_count"] == 1

        run_facets = response.json()["run_facets"]
        assert run_facets is not None
        assert {
            "key": "input_key",
            "query": 'eq(input_key, "prompts")',
            "value": "prompts",
        } in run_facets
        assert {
            "key": "output_key",
            "query": 'eq(output_key, "llm_output.token_usage.completion_tokens")',
            "value": "llm_output.token_usage.completion_tokens",
        } in run_facets
        assert {
            "key": "name",
            "query": 'eq(name, "LLM1")',
            "value": "LLM1",
        } in run_facets
        assert {
            "key": "input_key_value",
            "value": 'prompts == "how many people live in canada as of 2025?"',
            "query": "and(eq(input_key, 'prompts'), eq(input_value, \"how many people live in canada as of 2025?\"))",
        } in run_facets
        assert {
            "key": "output_key_value",
            "value": 'generations.text == "49,566,249"',
            "query": "and(eq(output_key, 'generations.text'), eq(output_value, \"49,566,249\"))",
        } in run_facets
        assert {
            "key": "metadata_key",
            "query": 'eq(metadata_key, "ls_model_name")',
            "value": "ls_model_name",
        } in run_facets
        assert {
            "key": "metadata_key_value",
            "query": "and(eq(metadata_key, 'ls_model_name'), eq(metadata_value, \"gpt-4o\"))",
            "value": 'ls_model_name == "gpt-4o"',
        } in run_facets

        #### Test input key value filter ####

        response = await http_tenant_one.post(
            "/runs/stats",
            json={
                "session": [str(tenant_one_custom_tracer_session_id)],
                "start_time": "2023-05-05T05:13:24.571809",
                "filter": 'and(eq(input_key, "prompts"), eq(input_value, "how many people live in canada as of 2025?"))',
            },
        )
        assert response.status_code == 200

        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["total_tokens"] == 1000
        assert response.json()["run_count"] == 1

        run_facets = response.json()["run_facets"]
        assert run_facets is not None
        assert {
            "key": "input_key",
            "query": 'eq(input_key, "prompts")',
            "value": "prompts",
        } in run_facets
        assert {
            "key": "output_key",
            "query": 'eq(output_key, "llm_output.token_usage.completion_tokens")',
            "value": "llm_output.token_usage.completion_tokens",
        } in run_facets
        assert {
            "key": "name",
            "query": 'eq(name, "LLM1")',
            "value": "LLM1",
        } in run_facets
        assert {
            "key": "input_key_value",
            "value": 'prompts == "how many people live in canada as of 2025?"',
            "query": "and(eq(input_key, 'prompts'), eq(input_value, \"how many people live in canada as of 2025?\"))",
        } in run_facets
        assert {
            "key": "output_key_value",
            "value": 'generations.text == "49,566,249"',
            "query": "and(eq(output_key, 'generations.text'), eq(output_value, \"49,566,249\"))",
        } in run_facets
        assert {
            "key": "metadata_key",
            "query": 'eq(metadata_key, "ls_model_name")',
            "value": "ls_model_name",
        } in run_facets
        assert {
            "key": "metadata_key_value",
            "query": "and(eq(metadata_key, 'ls_model_name'), eq(metadata_value, \"gpt-4o\"))",
            "value": 'ls_model_name == "gpt-4o"',
        } in run_facets

        #### Test output key value filter ####

        response = await http_tenant_one.post(
            "/runs/stats",
            json={
                "session": [str(tenant_one_custom_tracer_session_id)],
                "start_time": "2023-05-05T05:13:24.571809",
                "filter": 'and(eq(output_key, "generations.text"), eq(output_value, "49,566,249"))',
            },
        )

        assert response.status_code == 200
        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["total_tokens"] == 1000
        assert response.json()["run_count"] == 1

        run_facets = response.json()["run_facets"]
        assert run_facets is not None
        assert {
            "key": "input_key",
            "query": 'eq(input_key, "prompts")',
            "value": "prompts",
        } in run_facets
        assert {
            "key": "output_key",
            "query": 'eq(output_key, "llm_output.token_usage.completion_tokens")',
            "value": "llm_output.token_usage.completion_tokens",
        } in run_facets
        assert {
            "key": "name",
            "query": 'eq(name, "LLM1")',
            "value": "LLM1",
        } in run_facets
        assert {
            "key": "output_key_value",
            "value": 'generations.text == "49,566,249"',
            "query": "and(eq(output_key, 'generations.text'), eq(output_value, \"49,566,249\"))",
        } in run_facets
        assert {
            "key": "metadata_key",
            "query": 'eq(metadata_key, "ls_model_name")',
            "value": "ls_model_name",
        } in run_facets
        assert {
            "key": "metadata_key_value",
            "query": "and(eq(metadata_key, 'ls_model_name'), eq(metadata_value, \"gpt-4o\"))",
            "value": 'ls_model_name == "gpt-4o"',
        } in run_facets

        #### Test feedback key value filter ####

        # add feedback to run
        response = await http_tenant_one.post(
            "/feedback",
            json={
                "run_id": str(run_id_3),
                "key": "foo",
                "score": 0,
                "value": "blue",
                "feedback_source": {"type": "api"},
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()

        response = await http_tenant_one.post(
            "/runs/stats",
            json={
                "session": [str(tenant_one_custom_tracer_session_id)],
                "start_time": "2023-05-05T05:13:24.571809",
                "filter": 'and(eq(metadata_key, "ls_model_name"), eq(metadata_value, "gpt-4o"), eq(name, "LLM1"), eq(feedback_key, "foo"))',
            },
        )
        assert response.status_code == 200
        # check if it's reflected in the stats
        assert response.json()["run_count"] == 1
        assert response.json()["feedback_stats"] == {
            "foo": {
                "n": 1,
                "avg": 0.0,
                "stdev": 0.0,
                "errors": 0,
                "values": {"blue": 1},
            }
        }
        assert response.json()["completion_tokens"] == 401, response.json()
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["completion_tokens"] == 401
        assert response.json()["prompt_tokens"] == 599
        assert response.json()["total_tokens"] == 1000

        run_facets = response.json()["run_facets"]
        assert {
            "key": "input_key",
            "query": 'eq(input_key, "prompts")',
            "value": "prompts",
        } in run_facets
        assert {
            "key": "output_key",
            "query": 'eq(output_key, "llm_output.token_usage.completion_tokens")',
            "value": "llm_output.token_usage.completion_tokens",
        } in run_facets
        assert {
            "key": "name",
            "query": 'eq(name, "LLM1")',
            "value": "LLM1",
        } in run_facets
        assert {
            "key": "input_key_value",
            "value": 'prompts == "how many people live in canada as of 2025?"',
            "query": "and(eq(input_key, 'prompts'), eq(input_value, \"how many people live in canada as of 2025?\"))",
        } in run_facets
        assert {
            "key": "output_key_value",
            "value": 'generations.text == "49,566,249"',
            "query": "and(eq(output_key, 'generations.text'), eq(output_value, \"49,566,249\"))",
        } in run_facets
        assert {
            "key": "metadata_key",
            "query": 'eq(metadata_key, "ls_model_name")',
            "value": "ls_model_name",
        } in run_facets
        assert {
            "key": "metadata_key_value",
            "query": "and(eq(metadata_key, 'ls_model_name'), eq(metadata_value, \"gpt-4o\"))",
            "value": 'ls_model_name == "gpt-4o"',
        } in run_facets
        assert {
            "key": "feedback_key",
            "value": "foo",
            "query": 'eq(feedback_key, "foo")',
        } in run_facets
        assert {
            "key": "feedback_value",
            "value": 'foo == "blue"',
            "query": 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
        } in run_facets

        response = await http_tenant_one.post(
            "/runs/stats",
            json={
                "session": [str(tenant_one_custom_tracer_session_id)],
                "start_time": "2023-05-05T05:13:24.571809",
                "filter": 'and(eq(metadata_key, "ls_model_name"), eq(metadata_value, "gpt-4o"), eq(name, "LLM1"), eq(feedback_key, "foo"), gt(feedback_score, 0))',
            },
        )
        assert response.status_code == 200
        assert response.json()["run_count"] == 0
        assert response.json()["feedback_stats"] == {}


@PARAM_INGEST_ENDPOINT
async def test_run_stats_start_time_dotted_order_mismatch(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test token counts not reflected in run stats due to
    mismatch of start_time and timestampts in dotted_order (JS only)"""

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_custom_tracer_session_id = new_session.id

    run_id = uuid4()
    start_time = "2023-05-05T05:13:24.571000"

    # Due to https://github.com/langchain-ai/langchainjs/pull/5278
    # we need to populate the microsecond portion with the execution_order.
    # However, we forgot to do so in start_time as well.
    # which means there's a mismatch between start_time in runs payload and start_time
    # in dotted_order itself.
    dotted_order = f"20230505T051324571001Z{run_id}"

    post = [
        {
            "name": "ChatOpenAI",
            "start_time": start_time,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "llm",
            "id": str(run_id),
            "trace_id": str(run_id),
            "dotted_order": dotted_order,
        },
    ]
    patch = [
        {
            "id": str(run_id),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "usage_metadata": {
                    "input_tokens": 10,
                    "output_tokens": 20,
                    "total_tokens": 30,
                },
            },
            "trace_id": str(run_id),
            "dotted_order": dotted_order,
        }
    ]

    await post_runs(ingest_endpoint, http_tenant_one, post, patch)

    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs/stats",
        json={
            "session": [str(tenant_one_custom_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571000",
            "is_root": True,
        },
    )
    assert response.status_code == 200
    assert response.json()["run_count"] == 1
    assert response.json()["completion_tokens"] == 20
    assert response.json()["prompt_tokens"] == 10
    assert response.json()["total_tokens"] == 30


@PARAM_INGEST_ENDPOINT
async def test_run_inline_processing_batch(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test inline token processing"""

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_custom_tracer_session_id = new_session.id

    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()
    post = [
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
        },
        {
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar", "batch_size": 1},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 401,
                        "total_tokens": 1000,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}",
        },
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 200,
                        "completion_tokens": 300,
                        "total_tokens": 500,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
        },
    ]
    patch = [
        {
            "id": str(run_id_1),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        }
    ]
    attachments = {
        (str(run_id_3), "dog.jpg"): ("image/jpeg", b"woof"),
        (str(run_id_3), "cat"): ("image/jpeg", b"meow"),
    }

    await post_runs(ingest_endpoint, http_tenant_one, post, patch, attachments)

    await wait_until_task_queue_empty()

    llm_run_id3 = await crud.get_run(auth_tenant_one, run_id_3)
    assert llm_run_id3.parent_run_id == run_id_2
    assert llm_run_id3.trace_id == run_id_1
    assert llm_run_id3.s3_urls is not None

    expected_attachment_keys = {"attachment.cat", "attachment.dog.jpg"}
    extra_keys = {"extra", "serialized"}
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        expected_attachment_keys.update(extra_keys)

    assert set(llm_run_id3.s3_urls.keys()) == expected_attachment_keys
    async with AsyncClient() as client:
        for key in llm_run_id3.s3_urls:
            response = await client.get(llm_run_id3.s3_urls[key]["presigned_url"])
            assert response.status_code == 200
            if key not in extra_keys:
                assert (
                    response.content
                    == attachments[(str(run_id_3), key.replace("attachment.", ""))][1]
                )

    llm_run_id4 = await crud.get_run(auth_tenant_one, run_id_4)
    assert llm_run_id4.parent_run_id == run_id_2
    assert llm_run_id4.trace_id == run_id_1

    run_2 = await crud.get_run(auth_tenant_one, run_id_2)
    assert run_2.parent_run_id == run_id_1
    assert run_2.trace_id == run_id_1

    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.parent_run_id is None
    assert root_run.trace_id == run_id_1
    assert root_run.end_time == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)

    run_id3_row = await ch_client.fetchrow(
        f"SELECT s3_urls FROM runs FINAL WHERE id = '{run_id_3}'",
    )
    assert set(orjson.loads(run_id3_row["s3_urls"]).keys()) == expected_attachment_keys


@PARAM_INGEST_ENDPOINT
async def test_batch_processing_multiple_sessions(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test inline token processing"""

    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_custom_tracer_session_id = new_session.id

    new_session_2 = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_custom_tracer_session_2_id = new_session_2.id

    run_id_1 = uuid4()
    run_id_2 = uuid4()

    session_2_run_id_1 = uuid4()
    session_2_run_id_2 = uuid4()

    post = [
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
        },
        # second session
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_2_id),
            "run_type": "chain",
            "id": str(session_2_run_id_1),
            "trace_id": str(session_2_run_id_1),
            "dotted_order": f"20230505T051324571809Z{session_2_run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_custom_tracer_session_2_id),
            "parent_run_id": str(session_2_run_id_1),
            "run_type": "chain",
            "id": str(session_2_run_id_2),
            "trace_id": str(session_2_run_id_1),
            "dotted_order": f"20230505T051324571809Z{session_2_run_id_1}.20230505T051324581809Z{session_2_run_id_2}",
        },
    ]
    patch = [
        {
            "id": str(run_id_1),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "id": str(session_2_run_id_1),
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {"output": "39,566,248"},
            "trace_id": str(session_2_run_id_1),
            "dotted_order": f"20230505T051324571809Z{session_2_run_id_1}",
        },
    ]

    await post_runs(ingest_endpoint, http_tenant_one, post, patch)

    await wait_until_task_queue_empty()

    run_2 = await crud.get_run(auth_tenant_one, run_id_2)
    assert run_2.parent_run_id == run_id_1
    assert run_2.trace_id == run_id_1
    assert run_2.session_id == tenant_one_custom_tracer_session_id
    if settings.FF_TRACE_TIERS_ENABLED:
        assert run_2.trace_tier == schemas.TraceTier.shortlived.value
        assert run_2.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
        assert run_2.trace_first_received_at is not None
        assert not run_2.trace_upgrade
    else:
        assert run_2.trace_tier is None
        assert run_2.ttl_seconds is None

    root_run = await crud.get_run(auth_tenant_one, run_id_1)
    assert root_run.parent_run_id is None
    assert root_run.trace_id == run_id_1
    assert root_run.end_time == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert root_run.session_id == tenant_one_custom_tracer_session_id
    if settings.FF_TRACE_TIERS_ENABLED:
        assert root_run.trace_tier == schemas.TraceTier.shortlived.value
        assert root_run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
        assert not run_2.trace_upgrade
        assert root_run.trace_first_received_at is not None
    else:
        assert root_run.trace_tier is None
        assert root_run.ttl_seconds is None

    sess_2_run_2 = await crud.get_run(auth_tenant_one, session_2_run_id_2)
    assert sess_2_run_2.parent_run_id == session_2_run_id_1
    assert sess_2_run_2.trace_id == session_2_run_id_1
    assert sess_2_run_2.session_id == tenant_one_custom_tracer_session_2_id
    if settings.FF_TRACE_TIERS_ENABLED:
        assert sess_2_run_2.trace_tier == schemas.TraceTier.longlived.value
        assert sess_2_run_2.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
        assert sess_2_run_2.trace_first_received_at is not None
        assert not sess_2_run_2.trace_upgrade
    else:
        assert sess_2_run_2.trace_tier is None
        assert sess_2_run_2.ttl_seconds is None

    sess_2_root_run = await crud.get_run(auth_tenant_one, session_2_run_id_1)
    assert sess_2_root_run.parent_run_id is None
    assert sess_2_root_run.trace_id == session_2_run_id_1
    assert sess_2_root_run.end_time == datetime.datetime(2023, 5, 5, 5, 13, 32, 22361)
    assert sess_2_root_run.session_id == tenant_one_custom_tracer_session_2_id
    if settings.FF_TRACE_TIERS_ENABLED:
        assert sess_2_root_run.trace_tier == schemas.TraceTier.longlived.value
        assert sess_2_root_run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
        assert sess_2_root_run.trace_first_received_at is not None
        assert not sess_2_run_2.trace_upgrade
    else:
        assert sess_2_root_run.trace_tier is None
        assert sess_2_root_run.ttl_seconds is None


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
async def test_run_upgrade_completed(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test upgrades after a run has completed"""
    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_tracer_session_id = new_session.id

    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra = {"foo": "bar"}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": extra,
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.trace_tier == schemas.TraceTier.shortlived.value
    assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
    assert not run.trace_upgrade

    # get row to track
    before_upgrade_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id}'",
    )

    await upgrade_trace_tier(auth_tenant_one, tenant_one_tracer_session_id, [run.id])
    await wait_until_task_queue_empty()

    # get straight from ch for easier comparison
    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id}'",
    )

    # these fields are expected to vary
    ignored_fields = {
        "trace_tier": None,
        "ttl_seconds": None,
        "modified_at": None,
        "inserted_at": None,
        "outputs_s3_urls": None,
        "inputs_s3_urls": None,
        "s3_urls": None,
        "trace_upgrade": None,
    }

    # make sure other data is not changed besides expected columns
    assert {
        **before_upgrade_row,
        **ignored_fields,
    } == {
        **row,
        **ignored_fields,
    }

    updated_run = {
        **await ch_client.fetchrow(f"SELECT * FROM runs FINAL WHERE id = '{run_id}'")
    }

    assert updated_run["trace_tier"] == schemas.TraceTier.longlived.value
    assert updated_run["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds
    assert updated_run["trace_upgrade"]

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        input_s3_urls = orjson.loads(updated_run["inputs_s3_urls"])
        outputs_s3_urls = orjson.loads(updated_run["outputs_s3_urls"])
        s3_urls = orjson.loads(updated_run["s3_urls"])
        assert input_s3_urls and ROOT_S3_KEY in input_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EVENTS_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls

        assert "ttl_l" in input_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in s3_urls[ERROR_S3_KEY]
        assert "ttl_l" in s3_urls[EVENTS_S3_KEY]
        assert "ttl_l" in s3_urls[EXTRA_S3_KEY]
        assert "ttl_s" not in input_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" not in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" not in s3_urls[ERROR_S3_KEY]
        assert "ttl_s" not in s3_urls[EVENTS_S3_KEY]
        assert "ttl_s" not in s3_urls[EXTRA_S3_KEY]


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
@pytest.mark.parametrize("is_multipart", [True, False])
async def test_run_and_patch_tier_upgrade(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    is_multipart: bool,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test Upgrades created with a post and then patch"""
    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_tracer_session_id = new_session.id

    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra = {"foo": "bar", "metadata": {"side": "event"}}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": None,
        "extra": extra,
        "error": error,
        "events": events,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": inputs,
        "outputs": outputs,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "trace_id": str(run_id),
    }

    if is_multipart:
        run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
        run_data["trace_id"] = str(run_id)
        await post_runs(
            "/runs/multipart|go",
            http_tenant_one,
            post=[run_data],
        )
    else:
        response = await http_tenant_one.post("/runs/batch", json={"post": [run_data]})
        assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.trace_tier == schemas.TraceTier.shortlived.value
    assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
    assert not run.trace_upgrade

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_row = {
            **await ch_client.fetchrow(
                f"SELECT * FROM runs FINAL WHERE id = '{run_id}'"
            )
        }
        inputs_s3_urls = orjson.loads(run_row["inputs_s3_urls"])
        outputs_s3_urls = orjson.loads(run_row["outputs_s3_urls"])
        s3_urls = orjson.loads(run_row["s3_urls"])
        assert inputs_s3_urls and ROOT_S3_KEY in inputs_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EVENTS_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls
        assert "ttl_s" in inputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" in s3_urls[ERROR_S3_KEY]
        assert "ttl_s" in s3_urls[EVENTS_S3_KEY]
        assert "ttl_s" in s3_urls[EXTRA_S3_KEY]

    # upgrade trace
    await upgrade_trace_tier(
        auth_tenant_one, tenant_one_tracer_session_id, [run.trace_id]
    )

    new_outputs = {"output": "39,566,248"}
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        new_outputs["data"] = "u" * 1024

    response = await http_tenant_one.patch(
        f"/runs/{run_id}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": new_outputs,
        },
    )

    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.trace_tier == schemas.TraceTier.longlived.value
    assert run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
    assert not run.trace_upgrade  # not upgrade - since we patched also with the upgrade

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_row = {
            **await ch_client.fetchrow(
                f"SELECT * FROM runs FINAL WHERE id = '{run_id}'"
            )
        }
        inputs_s3_urls = orjson.loads(run_row["inputs_s3_urls"])
        outputs_s3_urls = orjson.loads(run_row["outputs_s3_urls"])
        s3_urls = orjson.loads(run_row["s3_urls"])
        assert inputs_s3_urls and ROOT_S3_KEY in inputs_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EVENTS_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls
        assert "ttl_l" in inputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in s3_urls[ERROR_S3_KEY]
        assert "ttl_l" in s3_urls[EVENTS_S3_KEY]
        assert "ttl_l" in s3_urls[EXTRA_S3_KEY]


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
@pytest.mark.parametrize("is_multipart", [True, False])
async def test_run_after_post_tier_upgrade(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    is_multipart: bool,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test upgrades after a POST."""
    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_tracer_session_id = new_session.id

    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra = {"foo": "bar", "metadata": {"side": "even"}}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": None,
        "extra": extra,
        "error": error,
        "events": events,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": inputs,
        "outputs": outputs,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }

    if is_multipart:
        run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
        run_data["trace_id"] = str(run_id)
        await post_runs(
            "/runs/multipart|go",
            http_tenant_one,
            post=[run_data],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data)
        assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.session_id == tenant_one_tracer_session_id
    assert run.trace_tier == schemas.TraceTier.shortlived.value
    assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
    assert not run.trace_upgrade

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_row = {
            **await ch_client.fetchrow(
                f"SELECT * FROM runs FINAL WHERE id = '{run_id}'"
            )
        }
        inputs_s3_urls = orjson.loads(run_row["inputs_s3_urls"])
        outputs_s3_urls = orjson.loads(run_row["outputs_s3_urls"])
        s3_urls = orjson.loads(run_row["s3_urls"])
        assert inputs_s3_urls and ROOT_S3_KEY in inputs_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EVENTS_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls
        assert "ttl_s" in inputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_s" in s3_urls[ERROR_S3_KEY]
        assert "ttl_s" in s3_urls[EVENTS_S3_KEY]
        assert "ttl_s" in s3_urls[EXTRA_S3_KEY]

    # upgrade trace
    await upgrade_trace_tier(
        auth_tenant_one, tenant_one_tracer_session_id, [run.trace_id]
    )

    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.trace_tier == schemas.TraceTier.longlived.value
    assert run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
    assert run.trace_upgrade

    if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_row = {
            **await ch_client.fetchrow(
                f"SELECT * FROM runs FINAL WHERE id = '{run_id}'"
            )
        }
        inputs_s3_urls = orjson.loads(run_row["inputs_s3_urls"])
        outputs_s3_urls = orjson.loads(run_row["outputs_s3_urls"])
        s3_urls = orjson.loads(run_row["s3_urls"])
        assert inputs_s3_urls and ROOT_S3_KEY in inputs_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EVENTS_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls
        assert "ttl_l" in inputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in outputs_s3_urls[ROOT_S3_KEY]
        assert "ttl_l" in s3_urls[ERROR_S3_KEY]
        assert "ttl_l" in s3_urls[EVENTS_S3_KEY]
        assert "ttl_l" in s3_urls[EXTRA_S3_KEY]


@PARAM_INGEST_ENDPOINT
@pytest.mark.parametrize(["with_endtime"], [[True], [False]])
@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
async def test_run_batch_tier_upgrade(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    with_endtime: bool,
    ingest_endpoint: str,
) -> None:
    """Test upgrades for batch runs"""

    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_custom_tracer_session_id = new_session.id

    run_id_1 = uuid4()
    run_id_2 = uuid4()
    run_id_3 = uuid4()
    run_id_4 = uuid4()
    post: list[dict] = [
        {
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "run_type": "chain",
            "id": str(run_id_1),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}",
        },
        {
            "name": "LLMChain",
            "start_time": "2023-05-05T05:13:24.581809",
            "end_time": "2023-05-05T05:13:32.022361" if with_endtime else None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_1),
            "run_type": "chain",
            "id": str(run_id_2),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}",
        },
        {
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361" if with_endtime else None,
            "extra": {"foo": "bar", "batch_size": 1},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 401,
                        "total_tokens": 1000,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_3),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324591809Z{run_id_3}",
        },
        {
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "end_time": "2023-05-05T05:13:32.022361" if with_endtime else None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 200,
                        "completion_tokens": 300,
                        "total_tokens": 500,
                    },
                },
            },
            "session_id": str(tenant_one_custom_tracer_session_id),
            "parent_run_id": str(run_id_2),
            "run_type": "llm",
            "id": str(run_id_4),
            "trace_id": str(run_id_1),
            "dotted_order": f"20230505T051324571809Z{run_id_1}.20230505T051324581809Z{run_id_2}.20230505T051324601809Z{run_id_4}",
        },
    ]
    patch: list[dict] = (
        [
            {
                "id": str(run_id_1),
                "end_time": "2023-05-05T05:13:32.022361",
                "outputs": {"output": "39,566,248"},
                "trace_id": str(run_id_1),
                "dotted_order": f"20230505T051324571809Z{run_id_1}",
            },
        ]
        if with_endtime
        else []
    )

    await post_runs(ingest_endpoint, http_tenant_one, post, patch)

    await wait_until_task_queue_empty()

    for run_id in [run_id_1, run_id_2, run_id_3, run_id_4]:
        run = await crud.get_run(auth_tenant_one, run_id)
        assert run.trace_tier == schemas.TraceTier.shortlived.value
        assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
        assert run.trace_first_received_at is not None
        assert not run.trace_upgrade

    await upgrade_trace_tier(
        auth_tenant_one, tenant_one_custom_tracer_session_id, [run_id_1]
    )

    await wait_until_task_queue_empty()

    for i, run_id in enumerate([run_id_1, run_id_2, run_id_3, run_id_4]):
        run = await crud.get_run(auth_tenant_one, run_id)
        assert run.trace_tier == schemas.TraceTier.longlived.value, (
            f"Expected run {i} {run_id} to be longlived"
        )
        assert run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds, (
            f"Expected run {i} {run_id} to have longlived TTL"
        )
        assert run.trace_upgrade, (
            f"Expected run {i} {run_id} to have been marked upgraded"
        )
        assert run.trace_first_received_at is not None, (
            f"Expected run {i} {run_id} to have been marked received"
        )

        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.READ
        ) as aredis:
            trace_tier = await aredis.hget(
                f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}", "trace_tier"
            )
            assert orjson.loads(trace_tier) == schemas.TraceTier.longlived.value


@pytest.mark.parametrize("with_endtime", [True, False])
@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
async def test_nonbatch_with_children_tier_upgrade(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    with_endtime: bool,
):
    # Create a new session and multiple runs
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    session_id = session.id
    parent_run_id = uuid4()
    child_run_ids = [uuid4() for _ in range(3)]

    # Post the parent run
    parent_run_response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361" if with_endtime else None,
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_id": str(session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(parent_run_id),
        },
    )
    assert parent_run_response.status_code == 202

    # Post child runs
    for child_run_id in child_run_ids:
        child_run_response = await http_tenant_one.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361" if with_endtime else None,
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(session_id),
                "parent_run_id": str(parent_run_id),
                "run_type": "chain",
                "id": str(child_run_id),
            },
        )
        assert child_run_response.status_code == 202

    await wait_until_task_queue_empty()

    # Check initial trace tier
    parent_run = await crud.get_run(auth_tenant_one, parent_run_id)
    assert parent_run.trace_tier == schemas.TraceTier.shortlived.value

    for child_run_id in child_run_ids:
        child_run = await crud.get_run(auth_tenant_one, child_run_id)
        assert child_run.trace_tier == schemas.TraceTier.shortlived.value
        assert not child_run.trace_upgrade

    # Upgrade trace tier
    await upgrade_trace_tier(auth_tenant_one, session_id, [parent_run_id])

    await wait_until_task_queue_empty()

    # Check upgraded trace tier
    parent_run = await crud.get_run(auth_tenant_one, parent_run_id)
    assert parent_run.trace_tier == schemas.TraceTier.longlived.value
    assert parent_run.trace_upgrade

    for child_run_id in child_run_ids:
        child_run = await crud.get_run(auth_tenant_one, child_run_id)
        assert child_run.trace_tier == schemas.TraceTier.longlived.value
        assert child_run.trace_upgrade

    # check that trace_tier is saved to redis
    async with redis.aredis_routed_pool(
        str(auth_tenant_one.tenant_id), redis_operation=redis.RedisOperation.READ
    ) as aredis:
        for run_id in [parent_run_id, *child_run_ids]:
            trace_tier = await aredis.hget(
                f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}", "trace_tier"
            )
            assert orjson.loads(trace_tier) == schemas.TraceTier.longlived.value


async def _create_run(
    auth: AuthInfo,
    run_id: UUID,
    child_ids: list[UUID],
    session_id: UUID,
    inputs: dict,
    outputs: dict,
    error: Optional[str] = None,
    events: Optional[list[Any]] = [],
    extra: Optional[dict] = {},
) -> UUID:
    """Create a run."""
    root_start_time = datetime.datetime.utcnow()
    child_start_time = root_start_time + datetime.timedelta(seconds=1)

    def _payload(id: UUID, start_time: datetime.datetime) -> dict:
        return dict(
            id=str(id),
            name=random_lower_string(),
            start_time=start_time.isoformat(),
            run_type="llm",
            end_time=(start_time + datetime.timedelta(seconds=1)).isoformat(),
            extra=extra,
            error=error,
            events=events,
            serialized={},
            inputs=inputs,
            outputs=outputs,
            parent_run_id=None,
            reference_example_id=None,
            session_name=None,
            session_id=session_id,
            tags=None,
        )

    await models.runs.ingest.upsert_runs(
        [
            models.runs.ingest.RunInsert(
                payload=_payload(run_id, root_start_time),
                trace_id=str(run_id),
                dotted_order=f"{root_start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{run_id.hex}",
                received_at=datetime.datetime.now(datetime.timezone.utc).isoformat(),
                modified_at=datetime.datetime.now(datetime.timezone.utc).isoformat(),
                hash_key="",
                should_insert=True,
                done=False,
            ),
            *[
                models.runs.ingest.RunInsert(
                    payload=_payload(child_id, child_start_time),
                    trace_id=str(run_id),
                    dotted_order=f"{root_start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{run_id.hex}"
                    + f".{child_start_time.isoformat(timespec='microseconds').replace(':', '').replace('.', '').replace('-', '')}Z{child_id.hex}",
                    received_at=datetime.datetime.now(
                        datetime.timezone.utc
                    ).isoformat(),
                    modified_at=datetime.datetime.now(
                        datetime.timezone.utc
                    ).isoformat(),
                    hash_key="",
                    should_insert=True,
                    done=False,
                )
                for child_id in child_ids
            ],
        ],
        defaultdict(
            lambda: models.runs.ingest.TokenTracker(
                prompt_tokens=None,
                completion_tokens=None,
                total_tokens=None,
                first_token_time=None,
                prompt_cost=None,
                completion_cost=None,
                total_cost=None,
            )
        ),
        auth,
        asyncio.Semaphore(),
        wait_for_async_insert=True,
    )

    return run_id


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
@pytest.mark.parametrize(
    "inputs,outputs,compare_kv",
    [
        (
            {"input": "How many people live in canada as of 2023?"},
            {"output": "39,566,248 people"},
            True,
        ),
        (
            {
                "user_input": "this is an invalid pair(with some parens) and this, lets see if it works"
            },
            {"output": "OK let's see "},
            False,  # TODO: need to fix driver issue with invalid tuple
        ),
    ],
)
@PARAM_INGEST_ENDPOINT
async def test_clickhouse_upgrade_path(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    inputs: dict,
    outputs: dict,
    compare_kv: bool,
    ingest_endpoint: str,
):
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra = {"foo": "bar", "metadata": {"side": "even"}}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    # create run manually to simulate run outside of Redis path
    run_id = uuid4()
    child_id = uuid4()

    base_time = datetime.datetime.utcnow()

    parent_dotted_order = base_time.strftime("%Y%m%dT%H%M%S%fZ") + str(run_id)
    child_dotted_order = (
        parent_dotted_order
        + "."
        + base_time.strftime("%Y%m%dT%H%M%S%fZ")
        + str(child_id)
    )

    parent_run = {
        "name": "LLM",
        "start_time": base_time.isoformat(),
        "end_time": (base_time + datetime.timedelta(seconds=1)).isoformat(),
        "execution_order": 1,
        "inputs": inputs,
        "outputs": outputs,
        "error": error,
        "events": events,
        "extra": extra,
        "session_id": str(session.id),
        "run_type": "chain",
        "id": str(run_id),
        "trace_id": str(run_id),
        "dotted_order": parent_dotted_order,
        "parent_run_id": None,
    }
    child_run = {
        **parent_run,
        "id": str(child_id),
        "dotted_order": child_dotted_order,
        "parent_run_id": str(run_id),
        "execution_order": 2,
    }

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[parent_run, child_run],
    )
    await wait_until_task_queue_empty()

    # flush redis to ensure that the run is not in redis
    async with redis.aredis_routed_pool(
        str(auth_tenant_one.tenant_id), redis_operation=redis.RedisOperation.WRITE
    ) as aredis:
        keys_to_delete = [
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}",
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{child_id}",
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}:extra",
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{child_id}:extra",
            f"smith:runs:trace_runs:{auth_tenant_one.tenant_id}:{run_id}",
            f"smith:runs:trace_runs:{auth_tenant_one.tenant_id}:{child_id}",
            f"smith:runs:children:{auth_tenant_one.tenant_id}:{run_id}",
            f"smith:runs:children:{auth_tenant_one.tenant_id}:{child_id}",
        ]
        await aredis.delete(*keys_to_delete)

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.trace_tier == schemas.TraceTier.shortlived.value
    assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
    assert not run.trace_upgrade

    # get row to track
    before_upgrade_row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id}'",
    )
    assert before_upgrade_row is not None, "Run should exist in ClickHouse pre-upgrade"

    await upgrade_trace_tier(auth_tenant_one, session.id, [run_id])

    await wait_until_task_queue_empty(n_empty_threshold=5)  # type: ignore[call-arg]

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs FINAL WHERE id = '{run_id}' and trace_tier='longlived'",
    )

    assert row is not None, "Upgraded run should exist in ClickHouse"

    ignored_fields = {
        "trace_tier": None,
        "ttl_seconds": None,
        "modified_at": None,
        "inserted_at": None,
        "outputs_s3_urls": None,
        "inputs_s3_urls": None,
        "s3_urls": None,
        "trace_upgrade": None,
    }
    if not compare_kv:
        # TODO: Fix these likely to do to driver and tuple handling
        ignored_fields["inputs_kv"] = None
        ignored_fields["outputs_kv"] = None

    # make sure other data is not changed besides expected columns
    assert {
        **before_upgrade_row,
        **ignored_fields,
    } == {
        **row,
        **ignored_fields,
    }
    parent_run_row = {
        **await ch_client.fetchrow(f"SELECT * FROM runs FINAL WHERE id = '{run_id}'")
    }
    child_run_row = {
        **await ch_client.fetchrow(f"SELECT * FROM runs FINAL WHERE id = '{child_id}'")
    }
    for updated_run, parent_or_child in zip(
        [parent_run_row, child_run_row], ["parent", "child"]
    ):
        assert updated_run["trace_tier"] == schemas.TraceTier.longlived.value, (
            f"{parent_or_child} run should be longlived"
        )
        assert updated_run["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds, (
            f"{parent_or_child} run should have longlived ttl"
        )
        assert updated_run["trace_upgrade"]

        if settings.FF_BLOB_STORAGE_ENABLED and settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
            input_s3_urls = orjson.loads(updated_run["inputs_s3_urls"])
            outputs_s3_urls = orjson.loads(updated_run["outputs_s3_urls"])
            s3_urls = orjson.loads(updated_run["s3_urls"])
            assert input_s3_urls and ROOT_S3_KEY in input_s3_urls
            assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
            assert s3_urls and ERROR_S3_KEY in s3_urls
            assert s3_urls and EVENTS_S3_KEY in s3_urls
            assert s3_urls and EXTRA_S3_KEY in s3_urls

            assert "ttl_l" in input_s3_urls[ROOT_S3_KEY]
            assert "ttl_l" in outputs_s3_urls[ROOT_S3_KEY]
            assert "ttl_l" in s3_urls[ERROR_S3_KEY]
            assert "ttl_l" in s3_urls[EVENTS_S3_KEY]
            assert "ttl_l" in s3_urls[EXTRA_S3_KEY]
            assert "ttl_s" not in input_s3_urls[ROOT_S3_KEY]
            assert "ttl_s" not in outputs_s3_urls[ROOT_S3_KEY]
            assert "ttl_s" not in s3_urls[ERROR_S3_KEY]
            assert "ttl_s" not in s3_urls[EVENTS_S3_KEY]
            assert "ttl_s" not in s3_urls[EXTRA_S3_KEY]


@PARAM_INGEST_ENDPOINT
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.skipif(config.settings.BASIC_AUTH_ENABLED, reason="single org")
async def test_longlived_usage_limit_on_feedback_and_annotation_queues(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # STEP 1: Set up TTL default, usage limit, and annotation queue
        client = authed_client.client

        resp = await client.put(
            "/orgs/ttl-settings",
            json={"default_trace_tier": schemas.TraceTier.shortlived.value},
        )
        assert resp.status_code == 200

        payload = user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_LONGLIVED_TRACES,
            limit_value=2,
        )
        await user_defined_limits.upsert_user_defined_usage_limit(
            authed_client.auth, payload
        )
        resp = await client.post(
            "/annotation-queues", json={"name": "test", "description": "test"}
        )
        assert resp.status_code == 200
        queue_id = resp.json()["id"]

        # STEP 2: Send two runs and ensure they are both shortlived
        def gen_base_run_fields() -> dict[str, Any]:
            return {
                "name": "AgentExecutor",
                "start_time": datetime.datetime.utcnow().isoformat(),
                "end_time": (
                    datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
                ).isoformat(),
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "Agent"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_name": "test_sesion",
                "run_type": "chain",
            }

        def gen_feedback_fields(run_id: UUID) -> dict[str, Any]:
            return {
                "run_id": str(run_id),
                "key": "test",
                "score": 7,
                "feedback_source": {"type": "api"},
            }

        run_one_id = uuid4()
        run_two_id = uuid4()
        run_three_id = uuid4()

        await post_runs(
            ingest_endpoint,
            client,
            [
                {
                    **gen_base_run_fields(),
                    "id": str(run_one_id),
                    "trace_id": str(run_one_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(run_one_id),
                    "parent_run_id": None,
                },
                {
                    **gen_base_run_fields(),
                    "id": str(run_two_id),
                    "trace_id": str(run_two_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(run_two_id),
                    "parent_run_id": None,
                },
                {
                    **gen_base_run_fields(),
                    "id": str(run_three_id),
                    "trace_id": str(run_three_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(run_three_id),
                    "parent_run_id": None,
                },
            ],
        )

        await wait_until_task_queue_empty()

        for run_id in [run_one_id, run_two_id, run_three_id]:
            resp = await client.get(f"/runs/{run_id}")
            assert resp.status_code == 200
            assert resp.json()["trace_tier"] == schemas.TraceTier.shortlived.value

        # STEP 3: Upgrade the trace tier of the first two runs to longlived by posting feedback to
        #         exceed the limit
        for run_id in [run_one_id, run_two_id]:
            resp = await client.post("/feedback", json=gen_feedback_fields(run_id))
            assert resp.status_code == 200

        # wait for the auto-upgrade and see that the run was upgraded
        await wait_until_task_queue_empty()

        for run_id in [run_one_id, run_two_id]:
            resp = await client.get(f"/runs/{run_id}")
            assert resp.status_code == 200
            assert resp.json()["trace_tier"] == schemas.TraceTier.longlived.value

        # STEP 4: Try to upgrade the trace tier of the third run to longlived by posting feedback
        resp = await client.post("/feedback", json=gen_feedback_fields(run_three_id))
        assert resp.status_code == 429

        # feedback fails even on longlived traces
        resp = await client.post("/feedback", json=gen_feedback_fields(run_one_id))
        assert resp.status_code == 429

        # Adding to an annotation queue fails for shortlived/longlived traces
        for run_id in [run_one_id, run_three_id]:
            resp = await client.post(
                f"/annotation-queues/{queue_id}/runs",
                json=[str(run_id)],
            )
            assert resp.status_code == 429


@PARAM_INGEST_ENDPOINT
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.skipif(config.settings.BASIC_AUTH_ENABLED, reason="single org")
async def test_longlived_usage_limit_on_ingest_endpoints(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # STEP 1: Set up TTL default and usage limit
        resp = await client.put(
            "/orgs/ttl-settings",
            json={"default_trace_tier": schemas.TraceTier.longlived.value},
        )
        assert resp.status_code == 200

        payload = user_defined_limits.UpsertUsageLimit(
            limit_type=user_defined_limits.UsageLimitType.MONTHLY_LONGLIVED_TRACES,
            limit_value=2,
        )
        await user_defined_limits.upsert_user_defined_usage_limit(
            authed_client.auth, payload
        )

        # STEP 2: Send two runs and ensure they are both shortlived
        def gen_base_run_fields() -> dict[str, Any]:
            return {
                "name": "AgentExecutor",
                "start_time": datetime.datetime.utcnow().isoformat(),
                "end_time": (
                    datetime.datetime.utcnow() + datetime.timedelta(seconds=1)
                ).isoformat(),
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "Agent"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "session_name": "test_session",
                "run_type": "chain",
            }

        run_one_id = uuid4()
        run_two_id = uuid4()
        run_three_id = uuid4()

        # batch works while under the limit (0  -> 1 traces)
        await post_runs(
            ingest_endpoint,
            client,
            [
                {
                    **gen_base_run_fields(),
                    "id": str(run_one_id),
                    "trace_id": str(run_one_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(run_one_id),
                    "parent_run_id": None,
                },
            ],
        )

        await wait_until_task_queue_empty()

        # non batch works while crossing the limit (1 -> 2 traces)
        resp = await client.post(
            "/runs",
            json={
                **gen_base_run_fields(),
                "id": str(run_two_id),
                "parent_run_id": None,
            },
        )

        assert resp.status_code == 202
        await wait_until_task_queue_empty()

        for run_id in [run_one_id, run_two_id]:
            resp = await client.get(f"/runs/{run_id}")
            assert resp.status_code == 200
            assert resp.json()["trace_tier"] == schemas.TraceTier.longlived.value

        # Now we are at the limit, batch and non batch should both fail
        await post_runs(
            ingest_endpoint,
            client,
            [
                {
                    **gen_base_run_fields(),
                    "id": str(run_three_id),
                    "trace_id": str(run_three_id),
                    "dotted_order": datetime.datetime.utcnow().strftime(
                        "%Y%m%dT%H%M%S%fZ"
                    )
                    + str(run_three_id),
                    "parent_run_id": None,
                },
            ],
            status_code=429,
        )

        resp = await client.post(
            "/runs",
            json={
                **gen_base_run_fields(),
                "id": str(uuid4()),
                "parent_run_id": None,
            },
        )
        assert resp.status_code == 429


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
async def test_input_output_error_search(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that a run can be created."""
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {
        "input": "How many people live in canada as of 2023?",
        "single_key": "single_value",
        "other_key": {"nested": "the_value"},
    }
    outputs = {"output": '39,566,248 people live there and enjoy the "cold" weather.'}
    error = "an error flamingo"
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        error += " " + large_data

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": error,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    # run through cases (faster than parametrize to avoid multiple runs)
    test_cases = [
        ('search("unmatched")', 0),
        ('search("canada unmatched")', 0),
        ('like(outputs, "unmatched")', 0),
        ('like(error, "unmatched")', 0),
        ('search("canada")', 1),
        ('search("\\"cold\\"")', 1),
        ('search("Canada people")', 1),  # tokenize words - inputs
        ('search("enjoy cold weather")', 1),  # tokenize words - outputs
        (
            'search("Canada people how")',
            1,
        ),  # how is not tokenized and should not search it
        ('or(search("canada"), search("unmatched"))', 1),  # or clause
        ('or(search("canada"), search("unmatched"))', 1),  # or clause
        (
            'and(like(inputs, "canada"), like(outputs, "weather"))',
            1,
        ),  # inputs and outputs
        (
            'and(like(inputs, "canada people"), like(outputs, "enjoy weather"))',
            1,
        ),  # inputs and outputs w/ tokenization
        ('like(inputs, "cold")', 0),  # don't search outputs if inputs provided
        ('and(like(outputs, "cold"), like(outputs, "enjoy"))', 1),  # multiple outputs
        ('and(like(error, "flamingo"), like(error, "error"))', 1),  # multiple errors
        # input/output kv search
        (
            'and(eq(input_key, "input"), eq(input_value, "How many people live in canada as of 2023?"))',
            1,
        ),
        ('and(eq(input_key, "single_key"), eq(input_value, "single_value"))', 1),
        ('and(eq(input_key, "invalid_key"), eq(input_value, "single_value"))', 0),
        ('and(eq(input_key, "other_key.nested"), eq(input_value, "the_value"))', 1),
        ('and(eq(input_key, "nested"), eq(input_value, "the_value"))', 0),
    ]

    search_tasks = [
        http_tenant_one.post(
            "/runs/query", json={"session": [str(session.id)], "filter": search_filter}
        )
        for search_filter, _ in test_cases
    ]
    responses = await asyncio.gather(*search_tasks)
    for response, (search_filter, expected_result_len) in zip(responses, test_cases):
        assert (
            "runs" in response.json()
            and len(response.json()["runs"]) == expected_result_len
        ), f"search_filter: {search_filter}, expected_result_len: {expected_result_len}"


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
async def test_can_view_non_ttl_rows(
    auth_tenant_one: AuthInfo,
    http_tenant_one: AsyncClient,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=None),
    )
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}

    # create run manually to simulate run outside of Redis path
    run_id = uuid4()
    child_id = uuid4()
    await _create_run(auth_tenant_one, run_id, [child_id], session.id, inputs, outputs)

    # set tier to null
    await ch_client.execute(
        f"""ALTER TABLE runs UPDATE trace_tier = NULL, ttl_seconds=NULL, trace_first_received_at=NULL where trace_id = '{run_id}' SETTINGS mutations_sync = 1"""
    )
    await ch_client.execute("OPTIMIZE TABLE runs FINAL")

    # can still retrieve and tier is set to None
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run.trace_tier is None
    assert run.ttl_seconds is None
    assert run.trace_first_received_at is None
    assert not run.trace_upgrade

    # also try search
    response = await http_tenant_one.post(
        "/runs/query",
        json={"session": [str(session.id)], "filter": "eq(is_root, true)"},
    )
    assert response.status_code == 200
    assert len(response.json()["runs"]) >= 1


async def test_exclude_list(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be created."""
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["data"] = large_data
        outputs["data"] = large_data

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Check that the run was created for the correct session
    run = await crud.get_run(
        auth_tenant_one,
        run_id,
        exclude_select=[
            schemas.RunSelect.inputs,
            schemas.RunSelect.outputs,
            schemas.RunSelect.inputs_preview,
            schemas.RunSelect.outputs_preview,
        ],
    )

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        assert run.inputs is None
        assert run.outputs is None
    else:
        assert run.inputs == inputs
        assert run.outputs == outputs


async def test_preview_inputs_outputs_non_message():
    """This test assures that we just check the first key
    in the case that the payload is not just a chat payload"""
    # NOTE: This test might change in the future
    # Because we are most likely going to allow custom preview logic

    assert (
        preview_inputs(
            {
                "user input": "What is special today?",
                "chat_history": [
                    {
                        "content": 'The AI greets the human and repeatedly asks how it can assist, noting the date as October 16, 2023, and responding to various greetings and potential typos from the human. The human repeatedly says "hello" and variations of greetings without providing specific topics or questions. The AI remains ready to help with any questions or topics the human might have.',
                        "additional_kwargs": {},
                        "response_metadata": {},
                        "type": "system",
                    },
                    {
                        "content": "Hi! How can I assist you today? If you have any questions or need help with something, feel free to let me know.",
                        "additional_kwargs": {},
                        "response_metadata": {},
                        "type": "ai",
                        "example": False,
                        "tool_calls": [],
                        "invalid_tool_calls": [],
                    },
                ],
                "context": [],
            }
        )
        == "What is special today?"
    )

    assert (
        preview_inputs(
            {
                "chat_history": [
                    {
                        "content": 'The AI greets the human and repeatedly asks how it can assist, noting the date as October 16, 2023, and responding to various greetings and potential typos from the human. The human repeatedly says "hello" and variations of greetings without providing specific topics or questions. The AI remains ready to help with any questions or topics the human might have.',
                        "additional_kwargs": {},
                        "response_metadata": {},
                        "type": "system",
                    },
                    {
                        "content": "Hi! How can I assist you today? If you have any questions or need help with something, feel free to let me know.",
                        "additional_kwargs": {},
                        "response_metadata": {},
                        "type": "ai",
                        "example": False,
                        "tool_calls": [],
                        "invalid_tool_calls": [],
                    },
                ],
                "context": [],
                "user input": "What is special today?",
            }
        )
        == "ai: Hi! How can I assist you today? If you have any questions or need help with something, feel free to let me know."
    )


async def test_preview_inputs_outputs():
    # message tuple
    assert (
        preview_inputs({"input": ["human", "how to create service account"]})
        == "human: how to create service account"
    )

    assert (
        preview_inputs({"input": ["invalid", "how to create service account"]})
        == '["invalid","how to create service account"]'
    )

    # serialized message
    assert (
        preview_inputs(
            {
                "input": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain", "schema", "messages", "SystemMessage"],
                    "kwargs": {
                        "content": "You're an assistant who's good at jokes. Respond in 20 words or fewer",
                        "additional_kwargs": {},
                    },
                }
            }
        )
        == "system: You're an assistant who's good at jokes. Respond in 20 words or fewer"
    )

    assert (
        preview_inputs(
            {
                "input": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "What's the weather like in San Francisco, Tokyo, and Paris?",
                        "additional_kwargs": {},
                    },
                }
            }
        )
        == "human: What's the weather like in San Francisco, Tokyo, and Paris?"
    )

    # stored message
    assert (
        preview_inputs(
            {
                "input": {
                    "type": "human",
                    "data": {
                        "id": "0.****************",
                        "content": "how to create service account",
                    },
                },
            }
        )
        == "human: how to create service account"
    )

    # message fields
    assert (
        preview_inputs(
            {
                "input": {
                    "content": "What's the weather like in San Francisco, Tokyo, and Paris?",
                    "type": "human",
                }
            }
        )
        == "human: What's the weather like in San Francisco, Tokyo, and Paris?"
    )

    assert (
        preview_inputs(
            {
                "input": {
                    "content": "What's the weather like in San Francisco, Tokyo, and Paris?",
                    "role": "human",
                }
            }
        )
        == "human: What's the weather like in San Francisco, Tokyo, and Paris?"
    )

    # generations
    assert (
        preview_outputs(
            {
                "llmOutput": {},
                "generations": [
                    {
                        "text": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                                "additional_kwargs": {},
                            },
                        },
                    },
                ],
            }
        )
        == "ai: The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C"
    )

    # output deduplication
    assert (
        preview_outputs(
            {
                "asking": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "What's the weather like in San Francisco, Tokyo, and Paris?",
                        "additional_kwargs": {},
                    },
                },
                "output": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "AIMessage"],
                    "kwargs": {
                        "content": "The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C",
                        "additional_kwargs": {},
                    },
                },
            },
            inputs={
                "asking": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "What's the weather like in San Francisco, Tokyo, and Paris?",
                        "additional_kwargs": {},
                    },
                },
            },
        )
        == "ai: The current weather is as follows:\n- San Francisco: 72°F\n- Tokyo: 10°C\n- Paris: 22°C"
    )

    # message limit
    assert (
        preview_inputs({"input": ["human", "how to create service account"]}, 10)
        == "human: ..."
    )

    # multiple keys ordering
    assert (
        preview_inputs(
            {"message": "where is sara schaefer's office", "history": [], "ui": "web"}
        )
        == "where is sara schaefer's office"
    )

    # test for empty inputs and outputs
    assert preview_inputs({}) == ""
    assert preview_outputs({}, {}) == ""

    # test for single string input
    assert preview_inputs({"input": "Hello, world!"}) == "Hello, world!"

    # test for single string output
    assert preview_outputs({"output": "Goodbye, world!"}) == "Goodbye, world!"

    # test for nested message content
    assert (
        preview_outputs(
            {
                "response": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "AIMessage"],
                    "kwargs": {
                        "content": [
                            {"type": "text", "text": "This is a text message."},
                            {
                                "type": "image_url",
                                "image_url": "http://example.com/image.png",
                            },
                        ],
                        "additional_kwargs": {},
                    },
                },
            },
            inputs={
                "query": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "HumanMessage"],
                    "kwargs": {
                        "content": "Show me an example image.",
                        "additional_kwargs": {},
                    },
                },
            },
        )
        == "ai: This is a text message.\n![image](http://example.com/image.png)"
    )

    # test for multiple generations
    assert (
        preview_outputs(
            {
                "generations": [
                    {
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "First generation message.",
                                "additional_kwargs": {},
                            },
                        }
                    },
                    {
                        "message": {
                            "lc": 1,
                            "type": "constructor",
                            "id": ["langchain_core", "messages", "AIMessage"],
                            "kwargs": {
                                "content": "Second generation message.",
                                "additional_kwargs": {},
                            },
                        }
                    },
                ]
            }
        )
        == "ai: First generation message.\nai: Second generation message."
    )

    # test for message with additional kwargs
    assert (
        preview_outputs(
            {
                "response": {
                    "lc": 1,
                    "type": "constructor",
                    "id": ["langchain_core", "messages", "AIMessage"],
                    "kwargs": {
                        "content": "This is a message with additional kwargs.",
                        "additional_kwargs": {"key": "value"},
                    },
                },
            }
        )
        == "ai: This is a message with additional kwargs."
    )

    # Array input/output
    assert preview_inputs({"input": ["Hello", "World"]}) == '["Hello","World"]'
    assert preview_outputs({"output": ["Goodbye", "World"]}) == '["Goodbye","World"]'

    # Non-string input/output
    assert preview_inputs({"input": 123}) == "123"
    assert preview_outputs({"output": True}) == "true"

    # Null/Undefined input/output
    assert preview_inputs({"input": None}) == "null"
    assert preview_outputs({"output": None}) == "null"

    # Test unnested values
    assert preview_inputs({"type": "human", "content": "Goodbye"}) == "human: Goodbye"
    assert preview_inputs({"role": "human", "content": "Goodbye"}) == "human: Goodbye"
    assert (
        preview_inputs({"data": {"content": "Hello"}, "type": "human"})
        == "human: Hello"
    )
    assert (
        preview_inputs({"data": {"content": "Hello"}, "role": "human"})
        == "human: Hello"
    )

    assert (
        preview_inputs(
            {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain_core", "messages", "AIMessage"],
                "kwargs": {
                    "content": "This is a message with additional kwargs.",
                    "additional_kwargs": {"key": "value"},
                },
            }
        )
        == "ai: This is a message with additional kwargs."
    )

    assert preview_inputs({"type": "human", "content": ""}) == 'human: ""'
    assert preview_inputs({"role": "human", "content": ""}) == 'human: ""'
    assert (
        preview_inputs(
            {
                "lc": 1,
                "type": "constructor",
                "id": ["langchain_core", "messages", "HumanMessage"],
                "kwargs": {
                    "content": "",
                    "additional_kwargs": {},
                    "response_metadata": {},
                },
            }
        )
        == 'human: ""'
    )


async def test_preview_inputs_outputs_ingest(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Preview I/O is properly ingested in CH / computed in flight"""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": ["human", "how to create service account"]},
            "outputs": {
                "generations": [{"text": "39,566,248"}],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    ch_run = await ch_client.fetchrow(
        f"""SELECT inputs_preview, outputs_preview FROM runs FINAL WHERE id = '{run_id}'"""
    )
    assert ch_run is not None

    if config.settings.FF_CH_SEARCH_ENABLED:
        assert ch_run["inputs_preview"] == "human: how to create service account"
        assert ch_run["outputs_preview"] == "39,566,248"
    else:
        assert ch_run["inputs_preview"] is None
        assert ch_run["outputs_preview"] is None

    run = await crud.get_run(
        auth_tenant_one,
        run_id,
        exclude_select=[
            schemas.RunSelect.inputs,
            schemas.RunSelect.inputs_or_signed_url,
            schemas.RunSelect.inputs_s3_urls,
            schemas.RunSelect.outputs,
            schemas.RunSelect.outputs_or_signed_url,
            schemas.RunSelect.outputs_s3_urls,
        ],
    )

    assert run is not None
    assert run.inputs is None

    assert run.outputs is None


async def test_exclude_s3(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Preview I/O is properly ingested in CH / computed in flight"""

    inputs = {"input": "How many people live in czechia as of 2023?"}
    outputs = {"output": "110,498,692"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    extra = {"foo": "bar", "metadata": {"side": "even"}}

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        large_data = "x" * 1024
        inputs["large_input"] = large_data
        outputs["large_output"] = large_data
        events[0]["data"] = large_data
        error = "e" * 1024
        extra["data"] = large_data

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "error": error,
            "extra": extra,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(
        auth_tenant_one,
        run_id,
        exclude_select=[
            schemas.RunSelect.inputs,
            schemas.RunSelect.events,
            schemas.RunSelect.outputs,
            schemas.RunSelect.error,
            schemas.RunSelect.extra,
            schemas.RunSelect.inputs_preview,
            schemas.RunSelect.outputs_preview,
        ],
    )

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        assert run.inputs is None
        assert run.inputs_s3_urls
        assert run.outputs is None
        assert run.outputs_s3_urls
        assert run.s3_urls
        for key in (EVENTS_S3_KEY, ERROR_S3_KEY, EXTRA_S3_KEY):
            assert key in run.s3_urls
        assert run.events == []
        assert run.error is None
    else:
        assert run.inputs == inputs
        assert run.outputs == outputs
        assert run.error == error
        assert run.events == events


async def test_preview_inputs_outputs_ingest_long(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
) -> None:
    """Preview I/O is capped at limited amount of chars"""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": ["human", "how to create service account" * 100]},
            "outputs": {
                "generations": [{"text": "39,566,248"}],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    ch_run = await ch_client.fetchrow(
        f"""SELECT inputs_preview, outputs_preview FROM runs FINAL WHERE id = '{run_id}'"""
    )
    assert ch_run is not None

    assert (
        ch_run["inputs_preview"]
        == ("human: " + "how to create service account" * 100)[:147] + "..."
    )


@pytest.mark.skipif(
    not config.settings.FF_BLOB_STORAGE_ENABLED,
    reason="Only if storing runs in S3 is enabled",
)
async def test_preview_inputs_outputs_skip_s3(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test if we skip S3 if preview is in CH and preview is requested"""

    run_id = uuid4()
    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM1",
            "start_time": "2023-05-05T05:13:24.591809",
            "end_time": "2023-05-05T05:13:32.022361",
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": ["human", "how to create service account"]},
            "outputs": {
                "generations": [{"text": "39,566,248"}],
                "llm_output": {
                    "token_usage": {
                        "prompt_tokens": 599,
                        "completion_tokens": 101,
                    },
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "llm",
            "id": str(run_id),
        },
    )

    assert response.status_code == 202
    await wait_until_task_queue_empty()

    with patch(
        "app.models.runs.fetch_ch.get_run_data",
        side_effect=Exception("Attempting to fetch from S3"),
    ):
        res = await fetch_runs(
            auth_tenant_one,
            schemas.BodyParamsForRunSchema(
                id=[run_id],
                select=[
                    schemas.RunSelect.inputs_preview,
                    schemas.RunSelect.outputs_preview,
                ],
            ),
        )
        run = res["runs"][0]

        assert run is not None
        assert run["inputs"] is None
        assert run["inputs_preview"] == "human: how to create service account"

        assert run["outputs"] is None
        assert run["outputs_preview"] == "39,566,248"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cached_stats(
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
):
    mock_func = AsyncMock(side_effect=list(range(1, 10)))

    params = {
        "tenant_id__eq": "a653f747-3821-5d8c-946e-851bd9e74a6f",
        "session_id__in": ["200f8dad-5b87-4e2c-b6f8-63e56ee6693e"],
        "start_time__gte": "2024-08-13 22:34:23.981000",
        "is_trace_expired__eq": False,
        "is_root__eq": True,
    }

    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 1
    # should cache same args:
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 1
    # should not cache for next tenant
    assert await cached_stats(None, auth_tenant_two, mock_func, [], params=params) == 2

    # it shouldn't mutate start_time
    assert params["start_time__gte"] == "2024-08-13 22:34:23.981000"

    # it should chop the start time to hourly and cache the results
    params["start_time__gte"] = "2024-08-13 22:55:23.981000"
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 1
    params["start_time__gte"] = "2024-08-13 22:00:00.981000"
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 1

    # changing other params should not return cached values
    params["session_id__in"] = ["200f8dad-5b87-4e2c-b6f8-63e56ee54321"]
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 3
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 3

    # start_time for next hour should not return cached values
    params["start_time__gte"] = "2024-08-13 21:55:23.981000"
    assert await cached_stats(None, auth_tenant_one, mock_func, [], params=params) == 4


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cached_stats_max_range(
    auth_tenant_one: AuthInfo,
):
    mock_func = AsyncMock(return_value=[1])

    params = {
        "tenant_id__eq": "a653f747-3821-5d8c-946e-851bd9e74a6f",
        "session_id__in": ["200f8dad-5b87-4e2c-b6f8-63e56ee6693e"],
        "start_time__gte": "2024-08-13 22:34:23.981000",
        "is_trace_expired__eq": False,
        "is_root__eq": True,
    }

    # larger time range return empty results
    # Test for start_time more than 60 minutes ago
    params["start_time__gte"] = (
        datetime.datetime.now() - datetime.timedelta(minutes=61)
    ).isoformat()
    assert await cached_stats(60, auth_tenant_one, mock_func, [], params=params) == []

    # Test for start_time within 60 minutes
    params["start_time__gte"] = (
        datetime.datetime.now() - datetime.timedelta(minutes=50)
    ).isoformat()
    assert await cached_stats(60, auth_tenant_one, mock_func, [], params=params) == [1]

    # Test for end_time more than 60 minutes after start_time
    start_time = datetime.datetime.now() - datetime.timedelta(minutes=30)
    end_time = start_time + datetime.timedelta(minutes=61)
    params_over_60 = params.copy()
    params_over_60["start_time__gte"] = start_time.isoformat()
    params_over_60["start_time__lte"] = end_time.isoformat()
    assert (
        await cached_stats(60, auth_tenant_one, mock_func, [], params=params_over_60)
        == []
    )

    # Test for end_time within 60 minutes of start_time
    end_time = start_time + datetime.timedelta(minutes=59)
    params_within_60 = params.copy()
    params_within_60["start_time__gte"] = start_time.isoformat()
    params_within_60["start_time__lte"] = end_time.isoformat()
    assert await cached_stats(
        60, auth_tenant_one, mock_func, [], params=params_within_60
    ) == [1]

    # don't pass start date, in this case return none
    params_without_start = params.copy()
    params_without_start.pop("start_time__gte")
    assert (
        await cached_stats(
            60, auth_tenant_one, mock_func, [], params=params_without_start
        )
        == []
    )

    # Test for end_time without start_time
    params_only_end = params.copy()
    params_only_end.pop("start_time__gte")
    end_time = datetime.datetime.now()
    params_only_end["start_time__lte"] = end_time.isoformat()
    assert (
        await cached_stats(60, auth_tenant_one, mock_func, [], params=params_only_end)
        == []
    )


@pytest.mark.skipif(
    not config.settings.FF_TRACE_TIERS_ENABLED, reason="Only applicable for trace tiers"
)
@pytest.mark.parametrize("is_invalid_dict", [True, False])
async def test_run_invalid_json(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    is_invalid_dict: bool,
) -> None:
    """Test case where multipart has invalid JSON - should save run without inputs and outputs"""
    new_session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.shortlived),
    )
    tenant_one_tracer_session_id = new_session.id

    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    error = "an error"
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]
    run_id = uuid4()
    if is_invalid_dict:
        # invalid type, dict vs list
        inputs = orjson.dumps([inputs])  # type: ignore
        outputs = orjson.dumps([outputs])  # type: ignore
        events = orjson.dumps(events[0])  # type: ignore
    else:
        # invalid JSON, cut off last char
        inputs = orjson.dumps(inputs)[:-1]  # type: ignore
        outputs = orjson.dumps(outputs)[:-1]  # type: ignore
        events = orjson.dumps(events)[:-1]  # type: ignore

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": None,
        "extra": {"foo": "bar"},
        "error": error,
        "events": events,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": inputs,
        "outputs": outputs,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }

    run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
    run_data["trace_id"] = str(run_id)
    await post_runs(
        "/runs/multipart|go",
        http_tenant_one,
        post=[run_data],
    )
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run is not None
    assert run.outputs is None
    assert run.inputs is None
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        # save off into special error key
        assert run.outputs_s3_urls is not None
        assert ROOT_ERROR_S3_KEY in run.outputs_s3_urls
        assert run.inputs_s3_urls is not None
        assert ROOT_ERROR_S3_KEY in run.inputs_s3_urls
        assert run.s3_urls is not None
        assert EVENTS_ERROR_S3_KEY in run.s3_urls


@pytest.mark.parametrize("skip_pagination", [True, False])
async def test_skip_pagination(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ch_client: aiochclient.ChClient,
    skip_pagination: bool,
):
    """Test that a run can be created."""
    run_id = uuid4()
    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": "2024-05-05T05:13:24.571809",
                    "end_time": "2024-05-05T05:13:32.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2024?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(run_id),
                },
            )
            for _ in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "skip_pagination": skip_pagination,
        "limit": 1,
        "start_time": "2024-05-05T05:13:23.571809",
    }
    post_response = await http_tenant_one.post("/runs/query", json=query_params)

    assert post_response.status_code == 200
    res = post_response.json()
    assert len(res["runs"]) == 1
    if skip_pagination:
        assert res["cursors"]["next"] is None
        assert res["cursors"]["prev"] is None
    else:
        assert res["cursors"]["next"] is not None


@PARAM_INGEST_ENDPOINT
async def test_ingest_runs_with_example_metadata(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that example metadata is correctly propagated to runs that reference the example."""
    # First create a dataset
    response = await http_tenant_one.post(
        "/datasets",
        json={
            "name": random_lower_string(),
            "description": "test",
        },
    )
    assert response.status_code == 200
    dataset_id = response.json()["id"]

    # Create an example with metadata
    example_metadata = {"category": "test", "priority": "high"}
    response = await http_tenant_one.post(
        "/examples",
        json={
            "inputs": {"input": "test input"},
            "outputs": {"output": "test output"},
            "dataset_id": dataset_id,
            "metadata": example_metadata,
        },
    )
    assert response.status_code == 200
    example_id = response.json()["id"]

    # Create a session to associate with the run
    response = await http_tenant_one.post(
        "/sessions",
        json={
            "name": random_lower_string(),
            "reference_dataset_id": dataset_id,
            "start_time": "2023-05-05T05:13:24.571809",
        },
    )
    assert response.status_code == 200
    session_id = response.json()["id"]

    # Create a run that references the example
    run_id = uuid4()
    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "execution_order": 1,
        "inputs": {"input": "test input"},
        "outputs": {"output": "test output"},
        "session_id": session_id,
        "reference_example_id": example_id,
        "run_type": "chain",
        "id": str(run_id),
        "trace_id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "extra": {"foo": "bar", "metadata": {"heyo": "hi"}},
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_data["extra"]["data"] = "x" * 1024

    # Create another run that references the example
    run_id_2 = uuid4()
    run_data_2 = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "execution_order": 1,
        "inputs": {"input": "test input"},
        "outputs": {"output": "test output"},
        "session_id": session_id,
        "reference_example_id": example_id,
        "run_type": "chain",
        "id": str(run_id_2),
        "trace_id": str(run_id_2),
        "dotted_order": f"20230505T051324571809Z{run_id_2}",
        "extra": {},
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run_data_2["extra"]["data"] = "x" * 1024

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[run_data, run_data_2],
    )
    await wait_until_task_queue_empty()

    # Verify run has the metadata prefixed with ls_example_
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run is not None
    assert run.extra is not None
    assert "metadata" in run.extra

    # Check that the example metadata is correctly prefixed in the run metadata
    expected_metadata: dict[str, Any] = {
        f"ls_example_{k}": v for k, v in example_metadata.items()
    }
    expected_metadata["ls_example_dataset_split"] = ["base"]
    expected_metadata["heyo"] = "hi"
    expected_metadata["ls_run_depth"] = 0
    expected_extra = {
        "foo": "bar",
        "metadata": expected_metadata,
    }
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run.extra.pop("data")
    assert run.extra == expected_extra

    # Verify run has the metadata prefixed with ls_example_
    run2 = await crud.get_run(auth_tenant_one, run_id_2)
    assert run2 is not None
    assert run2.extra is not None
    assert "metadata" in run2.extra

    # Check that the example metadata is correctly prefixed in the run metadata
    expected_metadata2: dict[str, Any] = {
        f"ls_example_{k}": v for k, v in example_metadata.items()
    }
    expected_metadata2["ls_example_dataset_split"] = ["base"]
    expected_metadata2["ls_run_depth"] = 0
    expected_extra = {"metadata": expected_metadata2}
    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        run2.extra.pop("data")
    assert run2.extra == expected_extra


@PARAM_INGEST_ENDPOINT
async def test_query_runs_clickhouse_overlapping(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that runs can be queried from clickhouse."""

    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_tracer_session_id = new_session.id

    run_ids = [uuid4() for _ in range(10)]
    names = ["FooChain" for _ in range(5)] + ["BarChain" for _ in range(5)]
    metadata = [{"foo": "bar"} for _ in range(5)] + [
        {"bar": "foo", "foo": "other_val"} for _ in range(5)
    ]

    inputs = {"input": "How many people live in czechia as of 2023?"}
    outputs = {"output": "110,498,692"}
    error = None
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ]

    run_data = [
        {
            "name": name,
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {
                "metadata": meta,
                "runtime": {"library": "langsmith"},
                "excluded": {"foo": "bar"},
            },
            "error": error,
            "events": events,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": inputs,
            "outputs": outputs,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }
        for name, run_id, meta in zip(names, run_ids, metadata)
    ]

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=run_data,
    )

    await wait_until_task_queue_empty(timeout=120)  # type: ignore[call-arg]

    query_params = {
        "session": [str(tenant_one_tracer_session_id)],
        "ids": [str(run_id) for run_id in run_ids],
        "filter": 'and(eq(metadata_key, "foo"), eq(metadata_value, "foo"))',
    }
    response = await http_tenant_one.post("/runs/query", json=query_params)
    assert response.status_code == 200
    assert len(response.json()["runs"]) == 0


def _build_tree(runs: list[dict[str, Any]]) -> list[str]:
    tree = {}
    root = None
    for run in runs:
        tree[run["id"]] = run["parent_run_id"]
        if run["parent_run_id"] is None:
            root = run["id"]
    bfs_nodes = []
    visited = set()
    to_process = [root]
    while to_process:
        curr = to_process.pop(0)
        if curr in visited:
            continue
        visited.add(curr)
        bfs_nodes.append(str(curr))
        for run_id, parent_run_id in tree.items():
            if parent_run_id == curr:
                if run_id not in visited:
                    to_process.append(run_id)

    return bfs_nodes


# Helper to create a run dict
def _create_run_dict(
    name: str,
    run_id: UUID,
    session_id: UUID,
    root_id: UUID,
    parent_id: Optional[UUID] = None,
    dotted_order_suffix: str = "",
) -> dict[str, Any]:
    return {
        "name": name,
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"metadata": {"foo": "bar"}},
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "session_id": str(session_id),
        "parent_run_id": str(parent_id) if parent_id else None,
        "run_type": "chain",
        "id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{root_id}",
        "trace_id": str(root_id),
    }


async def test_run_tree_construction(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    new_session = await crud.create_tracer_session(
        auth_tenant_one, schemas.TracerSessionCreate()
    )
    tenant_one_tracer_session_id = new_session.id

    # Generate UUIDs for all nodes
    root_id = uuid4()
    child_ids = [uuid4() for _ in range(10)]
    grandchild_ids = [[uuid4() for _ in range(10)] for _ in range(10)]
    great_grandchild_ids = [
        [[uuid4() for _ in range(2)] for _ in range(10)] for _ in range(10)
    ]

    # Create timestamps for each level
    root_timestamp = "20230505T051324571809Z"
    child_timestamp = "20230505T051325571809Z"
    grandchild_timestamp = "20230505T051326571809Z"
    great_grandchild_timestamp = "20230505T051327571809Z"

    # Build the run data
    run_data = []

    # Add root
    run_data.append(
        _create_run_dict("Root", root_id, tenant_one_tracer_session_id, root_id)
    )

    # Add children
    for i, child_id in enumerate(child_ids):
        child_dotted_order = f"{root_timestamp}{root_id}.{child_timestamp}{child_id}"
        run_data.append(
            {
                **_create_run_dict(
                    f"Child_{i}",
                    child_id,
                    tenant_one_tracer_session_id,
                    root_id,
                    root_id,
                ),
                "dotted_order": child_dotted_order,
            }
        )

        # Add grandchildren for this child
        for j, grandchild_id in enumerate(grandchild_ids[i]):
            grandchild_dotted_order = (
                f"{child_dotted_order}.{grandchild_timestamp}{grandchild_id}"
            )
            run_data.append(
                {
                    **_create_run_dict(
                        f"Grandchild_{i}_{j}",
                        grandchild_id,
                        tenant_one_tracer_session_id,
                        root_id,
                        child_id,
                    ),
                    "dotted_order": grandchild_dotted_order,
                }
            )

            # Add great-grandchildren for this grandchild
            for k, great_grandchild_id in enumerate(great_grandchild_ids[i][j]):
                great_grandchild_dotted_order = f"{grandchild_dotted_order}.{great_grandchild_timestamp}{great_grandchild_id}"
                run_data.append(
                    {
                        **_create_run_dict(
                            f"GreatGrandchild_{i}_{j}_{k}",
                            great_grandchild_id,
                            tenant_one_tracer_session_id,
                            root_id,
                            grandchild_id,
                        ),
                        "dotted_order": great_grandchild_dotted_order,
                    }
                )

    batch_size = 50
    for i in range(0, len(run_data), batch_size):
        batch = run_data[i : i + batch_size]
        await post_runs(
            "/runs/batch",
            http_tenant_one,
            post=batch,
        )

    await wait_until_task_queue_empty()

    response = await http_tenant_one.post(
        "/runs/query",
        json={
            "trace": str(root_id),
            "session": [str(tenant_one_tracer_session_id)],
            "start_time": "2023-05-05T05:13:24.571809",
        },
    )

    assert response.status_code == 200
    runs = response.json()["runs"]
    assert len(runs) == 311

    bfs_tree = _build_tree(runs)
    assert len(bfs_tree) == 311

    assert bfs_tree[0] == str(root_id)

    child_set = set(str(id) for id in child_ids)
    assert set(bfs_tree[1:11]) == child_set

    grandchild_set = set(str(id) for sublist in grandchild_ids for id in sublist)
    assert set(bfs_tree[11:111]) == grandchild_set

    great_grandchild_set = set(
        str(id)
        for sublist in great_grandchild_ids
        for subsublist in sublist
        for id in subsublist
    )
    assert set(bfs_tree[111:]) == great_grandchild_set


async def test_get_cost_for_tokens_none_extra():
    token_costs = _get_cost_for_tokens(
        TokenTracker(
            prompt_tokens=100,
            completion_tokens=100,
        ),
        run={
            "start_time": "2023-05-05T05:13:24.571809",
            "extra": {"invocation_params": None, "metadata": None},
            "run_type": "llm",
        },
        model_price_map=[],
    )
    assert token_costs.prompt_cost is None
    assert token_costs.completion_cost is None
    assert token_costs.total_cost is None


@PARAM_INGEST_ENDPOINT
async def test_create_run_large_payloads_and_upgrade(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    ch_client: aiochclient.ChClient,
):
    """
    Test creating a run with inputs, outputs, extra that each exceed 1000KB, and then upgrade
    the run. This ensures that even large payloads can be offloaded (if needed) and that the run
    upgrade successfully updates the trace tier and TTL settings in persistent storage.
    """
    # Generate a large string that is slightly more than 1000KB
    large_str = "a" * (1024 * 1000 + 1)
    run_id = uuid4()
    run_data = {
        "name": "LLM_LARGE",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"data": large_str},
        "error": large_str,
        "execution_order": 1,
        "serialized": {"data": large_str},
        "inputs": {"input": large_str},
        "outputs": {"output": large_str},
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "llm",
        "id": str(run_id),
    }
    # For multipart endpoints, add the required dotted_order and trace_id fields.
    if ingest_endpoint.startswith("/runs/multipart"):
        run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
        run_data["trace_id"] = str(run_id)
        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
        )
    else:
        response = await http_tenant_one.post("/runs", json=run_data)
        assert response.status_code == 202

    await wait_until_task_queue_empty()

    # Retrieve the run from the DB.
    run = await crud.get_run(auth_tenant_one, run_id)
    assert run is not None, "Run should be created successfully."

    # Verify initial trace tier properties (should be shortlived before upgrade)
    if config.settings.FF_TRACE_TIERS_ENABLED:
        assert run.trace_tier == schemas.TraceTier.shortlived.value
        assert run.ttl_seconds == schemas.TraceTier.shortlived.ttl_seconds
        assert not run.trace_upgrade

    # Upgrade the run's trace tier.
    await upgrade_trace_tier(auth_tenant_one, tenant_one_tracer_session_id, [run.id])
    await wait_until_task_queue_empty()

    # Retrieve the upgraded run.
    upgraded_run = await crud.get_run(auth_tenant_one, run_id)
    if config.settings.FF_TRACE_TIERS_ENABLED:
        assert upgraded_run.trace_tier == schemas.TraceTier.longlived.value
        assert upgraded_run.ttl_seconds == schemas.TraceTier.longlived.ttl_seconds
        assert upgraded_run.trace_upgrade

        input_s3_urls = upgraded_run.inputs_s3_urls
        outputs_s3_urls = upgraded_run.outputs_s3_urls
        s3_urls = upgraded_run.s3_urls
        assert input_s3_urls and ROOT_S3_KEY in input_s3_urls
        assert outputs_s3_urls and ROOT_S3_KEY in outputs_s3_urls
        assert s3_urls and ERROR_S3_KEY in s3_urls
        assert s3_urls and EXTRA_S3_KEY in s3_urls

        assert "ttl_l" in input_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert "ttl_l" in outputs_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert "ttl_l" in s3_urls[ERROR_S3_KEY]["s3_url"]
        assert "ttl_l" in s3_urls[EXTRA_S3_KEY]["s3_url"]
        assert "ttl_s" not in input_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert "ttl_s" not in outputs_s3_urls[ROOT_S3_KEY]["s3_url"]
        assert "ttl_s" not in s3_urls[ERROR_S3_KEY]["s3_url"]
        assert "ttl_s" not in s3_urls[EXTRA_S3_KEY]["s3_url"]

    # Verify in ClickHouse that the upgrade has been persisted.
    row = await ch_client.fetchrow(f"SELECT * FROM runs FINAL WHERE id = '{run_id}'")
    assert row is not None, "Run must exist in ClickHouse after upgrade."
    if config.settings.FF_TRACE_TIERS_ENABLED:
        assert row["trace_tier"] == schemas.TraceTier.longlived.value
        assert row["ttl_seconds"] == schemas.TraceTier.longlived.ttl_seconds
        assert row["trace_upgrade"]


@PARAM_INGEST_ENDPOINT
async def test_create_run_with_null_metadata(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
    tenant_one_tracer_session_id: UUID,
) -> None:
    """Test that a run can be created when extra.metadata is explicitly set to None."""
    run_id = uuid4()
    inputs = {"input": "How many people live in Canada?"}
    outputs = {"output": "37,000,000"}
    extra: dict[str, Any] = {"foo": "bar", "metadata": None}

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": extra,
        "error": None,
        "execution_order": 1,
        "serialized": {"name": "AgentExecutor"},
        "inputs": inputs,
        "outputs": outputs,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }

    if ingest_endpoint.startswith("/runs/multipart"):
        run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
        run_data["trace_id"] = str(run_id)
        await post_runs(ingest_endpoint, http_tenant_one, post=[run_data])
    else:
        response = await http_tenant_one.post("/runs", json=run_data)
        assert response.status_code == 202, response.text

    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id)
    assert run is not None
    assert run.inputs == inputs
    assert run.outputs == outputs
    extra_with_ls_run_depth = extra.copy()
    extra_with_ls_run_depth["metadata"] = {"ls_run_depth": 0}
    assert run.extra == extra_with_ls_run_depth


async def test_delete_run(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    ch_client: aiochclient.ChClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be created."""
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ] * (1000 if settings.MIN_BLOB_STORAGE_SIZE_KB == 0 else 1)
    serialized = {"name": "AgentExecutor"}
    extra: dict[str, Any] = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": extra,
        "error": "an error",
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
        "dotted_order": f"20230505T051324571809Z{run_id}",
        "trace_id": str(run_id),
    }

    feedback_data = {
        "id": str(uuid4()),
        "run_id": str(run_id),
        "trace_id": str(run_id),
        "key": "correctness",
        "score": 1,
    }

    await post_runs(
        "/runs/multipart|go",
        http_tenant_one,
        post=[run_data],
        feedback=[feedback_data],
    )

    await wait_until_task_queue_empty()

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is not None

    feedback_row = await db_asyncpg.fetchrow(
        "SELECT * FROM feedbacks WHERE run_id = $1",
        run_id,
    )
    assert feedback_row is not None

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        assert row["s3_urls"]
        assert row["inputs_s3_urls"]
        assert row["outputs_s3_urls"]

        s3_urls = orjson.loads(row["s3_urls"])
        assert s3_urls.get(SERIALIZED_S3_KEY) is not None
        serialized_key = s3_urls[SERIALIZED_S3_KEY]
        if "#" in serialized_key:
            serialized_key, ranges = serialized_key.split("#")
            start, end = ranges.split("-")
        else:
            start = None
            end = None
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": serialized_key,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
        )
        assert json.loads(result.body) == serialized

    await ch_client.execute(
        "ALTER TABLE runs MODIFY SETTING min_age_to_force_merge_seconds=1",
    )

    response = await http_tenant_one.post(
        "/runs/delete",
        json={
            "trace_ids": [str(run_id)],
            "session_id": str(tenant_one_tracer_session_id),
        },
    )
    assert response.status_code == 202

    async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
        await queue.enqueue(
            "process_batched_trace_deletes",
            scheduled=(
                datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(seconds=1)
            ).timestamp(),
            retry_delay=1,
            timeout=30,
            retries=0,
            retry_backoff=False,
        )

    await wait_until_task_queue_empty()

    await asyncio.sleep(2)

    row = await ch_client.fetchrow(
        f"SELECT * FROM runs WHERE id = '{run_id}'",
    )
    assert row is None

    feedback_row = await db_asyncpg.fetchrow(
        "SELECT * FROM feedbacks WHERE run_id = $1",
        run_id,
    )
    assert feedback_row is None

    if settings.MIN_BLOB_STORAGE_SIZE_KB == 0:
        result = await internal_platform_request(
            "GET",
            "/internal/download",
            params={
                "path": serialized_key,
                **(
                    {"start": start, "end": end}
                    if start is not None and end is not None
                    else {}
                ),
            },
            raise_error=False,
        )

        if result.code != 404:
            assert result.body == b"\x00" * len(orjson.dumps(serialized))


async def test_check_can_use_aggregated_merge_tree(auth_tenant_one: AuthInfo):
    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter="eq(is_root, true)",
        ),
        auth_tenant_one.tenant_id,
    )

    assert _can_use_topk_agg_merge_tree(params.where.arguments)
    assert _can_use_session_agg_merge_tree(params.where.arguments)

    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter="eq(is_root, false)",
        ),
        auth_tenant_one.tenant_id,
    )
    assert _can_use_topk_agg_merge_tree(params.where.arguments)
    assert _can_use_session_agg_merge_tree(params.where.arguments)

    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter=None,
        ),
        auth_tenant_one.tenant_id,
    )
    assert _can_use_topk_agg_merge_tree(params.where.arguments)
    assert _can_use_session_agg_merge_tree(params.where.arguments)

    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter='and(eq(is_root, true), eq(name, "HELLO"))',
        ),
        auth_tenant_one.tenant_id,
    )
    assert not _can_use_topk_agg_merge_tree(params.where.arguments)
    assert not _can_use_session_agg_merge_tree(params.where.arguments)

    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter='eq(name, "HELLO")',
        ),
        auth_tenant_one.tenant_id,
    )
    assert not _can_use_topk_agg_merge_tree(params.where.arguments)
    assert not _can_use_session_agg_merge_tree(params.where.arguments)

    params = await process_query_params(
        auth_tenant_one,
        schemas.RunStatsQueryParams(
            session=["1266d63f-3c48-47e8-bf66-799a6936d71c"],
            start_time="2023-05-05T05:13:24.571809",
            end_time="2023-05-15T05:13:32.022361",
            filter='and(eq(is_root, true), and(eq(metadata_key, "foo"), eq(metadata_value, "bar")))',
        ),
        auth_tenant_one.tenant_id,
    )
    assert not _can_use_topk_agg_merge_tree(params.where.arguments)
    assert not _can_use_session_agg_merge_tree(params.where.arguments)


async def test_optimized_fetch_runs_query(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that a run can be created."""

    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": f"2024-05-05T05:13:2{i}.571809",
                    "end_time": f"2024-05-05T05:13:2{i + 1}.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2024?"},
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(uuid4()),
                },
            )
            for i in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
    )

    res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
    )
    assert len(res["runs"]) == 3
    assert res["cursors"]["next"] is not None

    optimized_res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
        use_optimized_fetch=True,
    )
    assert len(optimized_res["runs"]) == 3
    assert optimized_res["cursors"]["next"] is not None

    for i, run in enumerate(optimized_res["runs"]):
        assert res["runs"][i]["id"] == run["id"]
        assert res["runs"][i]["start_time"] == run["start_time"]

    assert optimized_res["cursors"]["next"] == res["cursors"]["next"]
    assert optimized_res["cursors"]["prev"] == res["cursors"]["prev"]


async def test_fetch_runs_calls_ch_fetch_once(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
):
    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
    )

    with patch(
        "lc_database.clickhouse.InstrumentedChClient.fetch", autospec=True
    ) as mock_fetch:
        mock_fetch.return_value = []
        await fetch_runs(
            auth_tenant_one,
            query_params,
            additional_sql_where=None,
            cursor_field="start_time",
        )

        # Count how many times 'fetch_runs' was the first argument
        fetch_calls = [c for c in mock_fetch.call_args_list if c[0][1] == "fetch_runs"]
        assert len(fetch_calls) == 1

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
    )

    with patch(
        "lc_database.clickhouse.InstrumentedChClient.fetch", autospec=True
    ) as mock_fetch:
        mock_fetch.return_value = []
        await fetch_runs(
            auth_tenant_one,
            query_params,
            additional_sql_where=None,
            cursor_field="start_time",
            use_optimized_fetch=True,
        )

        # Count how many times 'fetch_runs' was the first argument
        fetch_calls = [
            c
            for c in mock_fetch.call_args_list
            if c[0][1] == "fetch_runs_optimized_parallel_fetch"
        ]
        assert (
            len(fetch_calls) == 2
        )  # called twice (once for is_root = 0 and once for is_root = 1)


async def test_input_kv_optimized(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that input_key nested filter is optimized and works correctly"""

    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": f"2024-05-05T05:13:2{i}.571809",
                    "end_time": f"2024-05-05T05:13:2{i + 1}.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {
                        "input": "How many people live in canada as of 2024?",
                        "nested": {"foo": "bar"},
                    },
                    "outputs": {"output": "39,566,248"},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(uuid4()),
                },
            )
            for i in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
        use_optimized_fetch=False,
        filter="eq(input_key, 'nested.foo')",
    )

    res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
    )
    assert len(res["runs"]) == 3
    assert res["cursors"]["next"] is not None

    optimized_res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
        use_optimized_fetch=True,
    )
    assert len(optimized_res["runs"]) == 3
    assert optimized_res["cursors"]["next"] is not None

    runs = res["runs"]
    runs.sort(key=lambda x: x["start_time"])

    optimized_runs = optimized_res["runs"]
    optimized_runs.sort(key=lambda x: x["start_time"])

    for i, run in enumerate(optimized_runs):
        assert runs[i]["id"] == run["id"]
        assert runs[i]["start_time"] == run["start_time"]

    assert optimized_res["cursors"]["next"] == res["cursors"]["next"]
    assert optimized_res["cursors"]["prev"] == res["cursors"]["prev"]


async def test_output_kv_optimized(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that output_key nested filter is optimized and works correctly"""

    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": f"2024-05-05T05:13:2{i}.571809",
                    "end_time": f"2024-05-05T05:13:2{i + 1}.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2024?"},
                    "outputs": {"output": "39,566,248", "nested": {"foo": "bar"}},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(uuid4()),
                },
            )
            for i in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
        filter="eq(output_key, 'nested.foo')",
    )

    res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
    )
    assert len(res["runs"]) == 3
    assert res["cursors"]["next"] is not None

    optimized_res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
        use_optimized_fetch=True,
    )
    assert len(optimized_res["runs"]) == 3
    assert optimized_res["cursors"]["next"] is not None

    runs = res["runs"]
    runs.sort(key=lambda x: x["start_time"])

    optimized_runs = optimized_res["runs"]
    optimized_runs.sort(key=lambda x: x["start_time"])

    for i, run in enumerate(optimized_runs):
        assert runs[i]["id"] == run["id"]
        assert runs[i]["start_time"] == run["start_time"]

    assert optimized_res["cursors"]["next"] == res["cursors"]["next"]
    assert optimized_res["cursors"]["prev"] == res["cursors"]["prev"]


async def test_tags_optimized(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that tags filter is optimized and works correctly"""

    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": f"2024-05-05T05:13:2{i}.571809",
                    "end_time": f"2024-05-05T05:13:2{i + 1}.022361",
                    "extra": {"foo": "bar"},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2024?"},
                    "outputs": {"output": "39,566,248", "nested": {"foo": "bar"}},
                    "session_id": str(tenant_one_tracer_session_id),
                    "tags": ["foo", "bar"],
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(uuid4()),
                },
            )
            for i in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
        filter="eq(tag, 'foo')",
    )

    res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
    )
    assert len(res["runs"]) == 3
    assert res["cursors"]["next"] is not None

    optimized_res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
        use_optimized_fetch=True,
    )
    assert len(optimized_res["runs"]) == 3
    assert optimized_res["cursors"]["next"] is not None

    runs = res["runs"]
    runs.sort(key=lambda x: x["start_time"])

    optimized_runs = optimized_res["runs"]
    optimized_runs.sort(key=lambda x: x["start_time"])

    for i, run in enumerate(optimized_runs):
        assert runs[i]["id"] == run["id"]
        assert runs[i]["start_time"] == run["start_time"]

    assert optimized_res["cursors"]["next"] == res["cursors"]["next"]
    assert optimized_res["cursors"]["prev"] == res["cursors"]["prev"]


async def test_metadata_optimized(
    auth_tenant_one: AuthInfo,
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """Test that metadata filter is optimized and works correctly"""

    responses = await asyncio.gather(
        *(
            http_tenant_one.post(
                "/runs",
                json={
                    "name": "AgentExecutor",
                    "start_time": f"2024-05-05T06:13:2{i}.571809",
                    "end_time": f"2024-05-05T06:13:2{i + 1}.022361",
                    "extra": {"metadata": {"fiz": "baz"}},
                    "error": None,
                    "execution_order": 1,
                    "serialized": {"name": "AgentExecutor"},
                    "inputs": {"input": "How many people live in canada as of 2024?"},
                    "outputs": {"output": "39,566,248", "nested": {"foo": "bar"}},
                    "session_id": str(tenant_one_tracer_session_id),
                    "parent_run_id": None,
                    "run_type": "chain",
                    "id": str(uuid4()),
                },
            )
            for i in range(5)
        )
    )

    assert len(responses) == 5

    await wait_until_task_queue_empty()

    query_params = schemas.BodyParamsForRunSchema(
        session=[str(tenant_one_tracer_session_id)],
        skip_pagination=False,
        limit=3,
        start_time="2024-05-05T05:13:20.000000",
        filter="eq(metadata_key, 'fiz')",
    )

    res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
    )
    assert len(res["runs"]) == 3
    assert res["cursors"]["next"] is not None

    runs = res["runs"]
    runs.sort(key=lambda x: x["start_time"])

    optimized_res = await fetch_runs(
        auth_tenant_one,
        query_params,
        additional_sql_where=None,
        cursor_field="start_time",
        use_optimized_fetch=True,
    )
    assert len(optimized_res["runs"]) == 3
    assert optimized_res["cursors"]["next"] is not None

    optimized_runs = optimized_res["runs"]
    optimized_runs.sort(key=lambda x: x["start_time"])

    for i, run in enumerate(optimized_runs):
        assert runs[i]["id"] == run["id"]
        assert runs[i]["start_time"] == run["start_time"]

    assert optimized_res["cursors"]["next"] == res["cursors"]["next"]
    assert optimized_res["cursors"]["prev"] == res["cursors"]["prev"]


async def test_set_run_token_costs(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
) -> None:
    """Test cases for token costs"""

    async def test_set_run_token_costs_helper(
        run_assertions: list,
        session_assertions: list,
        usage_metadata: dict,
    ):
        now = datetime.datetime.now(datetime.timezone.utc)

        response = await http_tenant_one.post(
            "/sessions",
            json={
                "name": random_lower_string(),
                "start_time": (now - datetime.timedelta(days=5)).isoformat(),
            },
        )
        assert response.status_code == 200
        await wait_until_task_queue_empty()
        session_id = response.json()["id"]
        run_id = uuid4()
        response = await http_tenant_one.post(
            "/runs",
            json={
                "name": "LLM1",
                "start_time": (now - datetime.timedelta(seconds=30)).isoformat(),
                "end_time": now.isoformat(),
                "extra": {
                    "foo": "bar",
                    "batch_size": 1,
                    "invocation_params": {"model": "gpt-3.5-turbo"},
                },
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "LLM"},
                "inputs": {"prompts": ["How many people live in canada as of 2023?"]},
                "outputs": {
                    "generations": [[{"text": "39,566,248"}]],
                    "usage_metadata": usage_metadata,
                },
                "session_id": str(session_id),
                "parent_run_id": None,
                "run_type": "llm",
                "id": str(run_id),
            },
        )

        assert response.status_code == 202
        await wait_until_task_queue_empty()

        run = await crud.get_run(auth_tenant_one, run_id)
        assert run.prompt_tokens == run_assertions[0]
        assert run.completion_tokens == run_assertions[1]
        assert run.total_tokens == run_assertions[2]
        assert run.prompt_token_details == run_assertions[3]
        assert run.completion_token_details == run_assertions[4]
        assert run.prompt_cost == run_assertions[5]
        assert run.completion_cost == run_assertions[6]
        assert run.total_cost == run_assertions[7]
        assert run.prompt_cost_details == run_assertions[8]
        assert run.completion_cost_details == run_assertions[9]

        response = await http_tenant_one.get(
            f"/sessions/{session_id}?include_stats=true"
        )

        assert response.status_code == 200
        session = response.json()
        assert session["prompt_tokens"] == session_assertions[0]
        assert session["completion_tokens"] == session_assertions[1]
        assert session["total_tokens"] == session_assertions[2]
        assert session["prompt_cost"] == session_assertions[3]
        assert session["completion_cost"] == session_assertions[4]
        assert session["total_cost"] == session_assertions[5]

    # Test case 1 - only details for cost/tokens
    await test_set_run_token_costs_helper(
        [
            6,
            3,
            9,
            {"a": 2, "b": 4},
            {"c": 3},
            Decimal("3"),
            Decimal("1"),
            Decimal("4"),
            {"a": Decimal("1"), "b": Decimal("2")},
            {"c": Decimal("1")},
        ],
        [6, 3, 9, 3.0, 1.0, 4.0],
        {
            "input_cost_details": {"a": 1, "b": 2},
            "output_cost_details": {"c": 1},
            "input_token_details": {"a": 2, "b": 4},
            "output_token_details": {"c": 3},
        },
    )

    # Test 2 - totals override details
    await test_set_run_token_costs_helper(
        [
            3,
            4,
            7,
            {"a": 2, "b": 4},
            {"c": 3},
            Decimal("2"),
            Decimal("3"),
            Decimal("5"),
            {"a": Decimal("1"), "b": Decimal("2")},
            {"c": Decimal("1")},
        ],
        [3, 4, 7, 2.0, 3.0, 5.0],
        {
            "input_cost_details": {"a": 1, "b": 2},
            "input_cost": 2,
            "output_cost_details": {"c": 1},
            "output_cost": 3,
            "input_token_details": {"a": 2, "b": 4},
            "input_tokens": 3,
            "output_token_details": {"c": 3},
            "output_tokens": 4,
        },
    )

    # Test 3 - can calculate tokens and cost separately
    await test_set_run_token_costs_helper(
        [12, 5, 17, None, None, Decimal("2"), Decimal("3"), Decimal("5"), None, None],
        [12, 5, 17, 2.0, 3.0, 5.0],
        {
            "input_cost": 2,
            "output_cost": 3,
        },
    )

    model_prompt_cost = 0.0000005
    model_completion_cost = 0.0000015

    # Test 4 - can calculate cost and tokens separately
    await test_set_run_token_costs_helper(
        [
            2,
            3,
            5,
            None,
            None,
            Decimal(str(model_prompt_cost * 2)),
            Decimal(str(model_completion_cost * 3)),
            Decimal(str(model_prompt_cost * 2 + model_completion_cost * 3)),
            None,
            None,
        ],
        [
            2,
            3,
            5,
            model_prompt_cost * 2,
            model_completion_cost * 3,
            model_prompt_cost * 2 + model_completion_cost * 3,
        ],
        {
            "input_tokens": 2,
            "output_tokens": 3,
        },
    )

    # Test 5 - Incorrect details are ignored
    await test_set_run_token_costs_helper(
        [
            12,
            5,
            17,
            None,
            None,
            Decimal(str(model_prompt_cost * 12)),
            Decimal(str(model_completion_cost * 5)),
            Decimal(str(model_prompt_cost * 12 + model_completion_cost * 5)),
            None,
            None,
        ],
        [
            12,
            5,
            17,
            model_prompt_cost * 12,
            model_completion_cost * 5,
            model_prompt_cost * 12 + model_completion_cost * 5,
        ],
        {
            "input_cost_details": {"a": "foo"},
            "output_cost_details": {"b": "bar"},
            "input_token_details": {"a": "foo"},
            "output_token_details": {"b": "bar"},
        },
    )

    # Test 6 - zeros are respected
    await test_set_run_token_costs_helper(
        [0, 0, 0, None, None, Decimal("0"), Decimal("0"), Decimal("0"), None, None],
        [0, 0, 0, 0, 0, 0],
        {
            "input_tokens": 0,
            "output_tokens": 0,
            "input_cost": 0,
            "output_cost": 0,
        },
    )

    # Test 7 - totals take precedence
    await test_set_run_token_costs_helper(
        [0, 0, 2, None, None, Decimal("0"), Decimal("0"), Decimal("3"), None, None],
        [0, 0, 2, 0, 0, 3.0],
        {
            "input_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 2,
            "input_cost": 0,
            "output_cost": 0,
            "total_cost": 3,
        },
    )

    # Test 8, max precision
    await test_set_run_token_costs_helper(
        [
            12,
            5,
            17,
            None,
            None,
            Decimal("0.000000000001"),
            # prompt_cost CH column has max precision 12 decimals
            Decimal("0"),
            Decimal("0.000000000001"),
            # details are stored as json blobs so no precision limits
            {"a": Decimal("1e-32")},
            None,
        ],
        [12, 5, 17, 1e-12, 0, 1e-12],
        {
            "input_cost": 1e-12,
            "output_cost": 1e-13,
            "input_cost_details": {"a": 1e-32},
        },
    )


async def test_patch_run_token_details(
    tenant_one_tracer_session_id: UUID,
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    run_id_1, run_id_2 = uuid4(), uuid4()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "usage_metadata": {
                    "input_cost_details": {"a": 1, "b": 1},
                    "output_cost_details": {"c": 2, "d": 2},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "llm",
            "id": str(run_id_1),
        },
    )
    assert response.status_code == 202

    # Finish the root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_1}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {
                "usage_metadata": {
                    "input_cost_details": {"a": 2, "b": 2},
                    "output_cost_details": {"c": 1, "d": 1},
                }
            },
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id_1)
    assert run.total_cost == Decimal("6")
    assert run.prompt_cost == Decimal("4")
    assert run.completion_cost == Decimal("2")
    assert run.prompt_cost_details == {"a": Decimal("2"), "b": Decimal("2")}
    assert run.completion_cost_details == {"c": Decimal("1"), "d": Decimal("1")}

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "LLM",
            "start_time": "2023-05-05T05:13:24.601809",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"prompts": ["How many people live in canada as of 2022?"]},
            "outputs": {
                "generations": [[{"text": "39,566,248"}]],
                "usage_metadata": {
                    "input_token_details": {"a": 1, "b": 1},
                    "output_token_details": {"c": 2, "d": 2},
                },
            },
            "session_id": str(tenant_one_tracer_session_id),
            "run_type": "llm",
            "id": str(run_id_2),
        },
    )
    assert response.status_code == 202

    # Finish the root run
    response = await http_tenant_one.patch(
        f"/runs/{run_id_2}",
        json={
            "end_time": "2023-05-05T05:13:32.022361",
            "outputs": {
                "usage_metadata": {
                    "input_token_details": {"a": 2, "b": 2},
                    "output_token_details": {"c": 1, "d": 1},
                }
            },
        },
    )
    assert response.status_code == 202
    await wait_until_task_queue_empty()

    run = await crud.get_run(auth_tenant_one, run_id_2)
    assert run.total_tokens == Decimal("6")
    assert run.prompt_tokens == Decimal("4")
    assert run.completion_tokens == Decimal("2")
    assert run.prompt_token_details == {"a": Decimal("2"), "b": Decimal("2")}
    assert run.completion_token_details == {"c": Decimal("1"), "d": Decimal("1")}
