"""Test Feedback endpoints."""

import asyncio
import random
import uuid
from typing import Any, Awaitable, Callable
from unittest import mock
from urllib.parse import quote_plus
from uuid import UUID, uuid4

import aiochclient
import asyncpg
import orj<PERSON>
import pytest
from fastapi import HTTPEx<PERSON>
from httpx import ASGITransport, AsyncClient
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.curl import platform_request
from pydantic import ValidationError

from app import config, crud, models, schemas
from app.api.auth import AuthInfo
from app.main import app
from app.schemas import APIFeedbackSource, Role
from app.tests.utils import (
    create_session,
    create_test_run,
    fresh_tenant_client,
    jwt_for_user,
    post_runs,
    random_lower_string,
)

pytestmark = pytest.mark.anyio


@pytest.fixture
async def run(auth_tenant_one: AuthInfo) -> UUID:
    return await create_test_run(auth_tenant_one, uuid4())


@pytest.fixture
async def run_two(auth_tenant_one: AuthInfo) -> UUID:
    return await create_test_run(auth_tenant_one, uuid4())


@pytest.fixture
async def run_session_two(
    auth_tenant_two: AuthInfo,
) -> UUID:
    return await create_test_run(auth_tenant_two, uuid4())


@pytest.fixture
async def run_three(
    auth_tenant_low_payload_size_limit: AuthInfo,
) -> UUID:
    return await create_test_run(auth_tenant_low_payload_size_limit, uuid4())


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_get_feedback(
    auth_tenant_one: AuthInfo,
    run: UUID,
    tenant_two_headers: dict[str, Any],
    ch_client: aiochclient.ChClient,
    db_asyncpg: asyncpg.Connection,
    http_tenant_one: AsyncClient,
    http_tenant_two: AsyncClient,
) -> None:
    """Test that a feedback can be retrieved."""
    feedback_id = uuid4()
    feedback_key = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=0.5,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
        ),
    )

    response = await http_tenant_one.get(f"/feedback/{feedback_id}")
    assert response.status_code == 200
    assert response.json()["id"] == str(feedback_id)
    assert response.json()["run_id"] == str(run)
    response = await http_tenant_two.get(f"/feedback/{feedback_id}")
    assert response.status_code == 404
    # Check that the feedback was persisted in clickhouse
    row = await db_asyncpg.fetchrow(
        f"SELECT * FROM feedbacks WHERE id = '{feedback_id}'"
    )
    assert row is not None
    assert row["id"] == feedback_id
    assert row["run_id"] == run
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["trace_id"] == run
    assert row["comment"] is None

    # Check that the feedback config was persisted in clickhouse
    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(type="continuous").model_dump()
    )

    # update the feedback
    response = await http_tenant_one.patch(
        f"/feedback/{feedback_id}",
        json={"comment": "test"},
    )
    assert response.status_code == 200, response.text

    # Check that the feedback was updated in clickhouse
    row = await db_asyncpg.fetchrow(
        f"SELECT * FROM feedbacks WHERE id = '{feedback_id}'"
    )
    assert row is not None
    assert row["comment"] == "test"

    # Delete the feedback
    response = await http_tenant_one.delete(f"/feedback/{feedback_id}")
    assert response.status_code == 200
    # Check that the feedback was deleted
    response = await http_tenant_one.get(f"/feedback/{feedback_id}")
    assert response.status_code == 404
    # Check that the feedback was deleted from clickhouse
    row = await db_asyncpg.fetchrow(
        f"SELECT * FROM feedbacks WHERE id = '{feedback_id}'"
    )
    assert row is None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_feedback_via_token(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a feedback can be retrieved."""
    feedback_key = random_lower_string()

    response = await http_tenant_one.post(
        "/feedback/tokens",
        json={"run_id": str(run), "feedback_key": feedback_key},
    )
    # missing expiry
    assert response.status_code == 400, response.text

    response = await http_tenant_one.post(
        "/feedback/tokens",
        json={
            "run_id": str(run),
            "feedback_key": feedback_key,
            "expires_in": {"minutes": 5},
        },
    )
    assert response.status_code == 200, response.text
    url = response.json()["url"]

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get(url, params={"score": 0.25, "comment": "test"})
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key}'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "test"

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.post(
            url,
            json={"score": 0.75, "comment": "another", "metadata": {"user_id": "1"}},
        )
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key}' and comment = 'another'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "another"
        assert row["feedback_source"] == '{"type":"api","metadata":{"user_id":"1"}}'


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_feedback_via_token_multi(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a feedback can be retrieved."""
    feedback_key_one = random_lower_string()
    feedback_key_two = random_lower_string()

    response = await http_tenant_one.post(
        "/feedback/tokens",
        json=[
            {
                "run_id": str(run),
                "feedback_key": feedback_key_one,
                "expires_in": {"minutes": 5},
            },
            {"run_id": str(run), "feedback_key": feedback_key_two},
        ],
    )
    # missing expiry in 2nd one
    assert response.status_code == 400, response.text

    response = await http_tenant_one.post(
        "/feedback/tokens",
        json=[
            {
                "run_id": str(run),
                "feedback_key": feedback_key_one,
                "expires_in": {"minutes": 5},
            },
            {
                "run_id": str(run),
                "feedback_key": feedback_key_two,
                "expires_in": {"minutes": 5},
            },
        ],
    )
    assert response.status_code == 200, response.text
    url_one = response.json()[0]["url"]
    url_two = response.json()[1]["url"]

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get(url_one, params={"score": 0.25, "comment": "test"})
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key_one}'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "test"

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.post(
            url_two,
            json={"score": 0.75, "comment": "another", "metadata": {"user_id": "1"}},
        )
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key_two}' and comment = 'another'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "another"
        assert row["feedback_source"] == '{"type":"api","metadata":{"user_id":"1"}}'


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_create_feedback_via_token_with_config(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test that a feedback can be retrieved."""
    feedback_key = random_lower_string()

    response = await http_tenant_one.post(
        "/feedback/tokens",
        json={
            "run_id": str(run),
            "feedback_key": feedback_key,
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
            "expires_in": {"minutes": 5},
        },
    )
    assert response.status_code == 200, response.text
    url = response.json()["url"]

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get(url, params={"score": 0.25, "comment": "test"})
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key}'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "test"

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.post(
            url,
            json={"score": 1.75, "comment": "another", "metadata": {"user_id": "1"}},
        )
        assert response.status_code == 400, response.text

    # Creating another token with same config works as long as identical
    response = await http_tenant_one.post(
        "/feedback/tokens",
        json={
            "run_id": str(run),
            "feedback_key": feedback_key,
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
            "expires_in": {"minutes": 5},
        },
    )
    assert response.status_code == 200, response.text
    assert response.json()["url"] != url
    url = response.json()["url"]

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get(url, params={"score": 0.25, "comment": "third"})
        assert response.status_code == 200, response.text

        # Check that the feedback was persisted in clickhouse
        rows = await ch_client.fetch(
            f"SELECT * FROM feedbacks_rmt FINAL WHERE key = '{feedback_key}' and comment = 'third'"
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["run_id"] == run
        assert row["tenant_id"] == auth_tenant_one.tenant_id
        assert row["trace_id"] == run
        assert row["comment"] == "third"


async def test_feedback_config(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
    ch_client: aiochclient.ChClient,
) -> None:
    """Test proper feedback_config behavior."""
    feedback_id = uuid4()
    feedback_key = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=0,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(
                type="categorical",
                categories=[
                    schemas.FeedbackCategory(value=0, label="bad"),
                    schemas.FeedbackCategory(value=1, label="good"),
                ],
            ),
        ),
    )
    # Check to see that the feedback config was persisted in clickhouse
    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(
            type="categorical",
            categories=[
                schemas.FeedbackCategory(value=0, label="bad"),
                schemas.FeedbackCategory(value=1, label="good"),
            ],
        ).model_dump()
    )
    modified_at = row["modified_at"]

    await asyncio.sleep(1)

    # Create another feedback with no feedback_config
    feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=1,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
        ),
    )

    # Check that the feedback config was not replaced
    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert row is not None
    # Fine to use == here because these are datetime objects
    assert row["modified_at"] == modified_at


async def test_feedback_config_correctness(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test creating of correctness feedback, which is a special case used by LangSmith"""

    feedback_id = uuid4()
    feedback_key = "correctness"

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=1,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
        ),
    )

    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=100,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
            ),
        )
    assert exc.value.status_code == 400

    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=-1,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
            ),
        )
    assert exc.value.status_code == 400

    row = await ch_client.fetchrow(
        """SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = {tenant_id}
        AND feedback_key = {feedback_key}""",
        params={"tenant_id": auth_tenant_one.tenant_id, "feedback_key": feedback_key},
    )

    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(
            type="continuous",
            min=0,
            max=1,
            categories=[
                schemas.FeedbackCategory(value=0),
                schemas.FeedbackCategory(value=1),
            ],
        ).model_dump()
    )


async def test_feedback_config_default(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    # test default feedback config being the continuous type
    feedback_id = uuid4()
    feedback_key = random_lower_string()

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=0.5,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=None,
        ),
    )
    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(type="continuous").model_dump()
    )


async def test_feedback_config_note(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that I can create a feedback with a note + fetch all the notes"""
    feedback_key = "note"

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            key=feedback_key,
            comment="this is a note",
            feedback_source=APIFeedbackSource(),
        ),
    )

    response = await http_tenant_one.get(
        "/feedback",
        params={
            "run": str(run),
            "key": feedback_key,
            "source": "api",
            "has_comment": True,
            "has_score": False,
        },
    )

    assert response.status_code == 200
    assert len(response.json()) == 1

    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(type="freeform").model_dump()
    )


async def test_feedback_config_continuous_invalid(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback cannot be created with an invalid feedback_config."""
    feedback_key = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            score=4.5,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(type="continuous", min=0, max=10),
        ),
    )

    feedback_id = uuid4()
    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=20,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
            ),
        )
    assert exc.value.status_code == 400

    # make sure the feedback was not persisted
    feedback_row = await ch_client.fetchrow(
        f"SELECT * FROM feedbacks_rmt FINAL WHERE id = '{feedback_id}'"
    )
    assert feedback_row is None


async def test_feedback_config_no_score(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that validation only occurs when score is present."""
    feedback_key_continuous = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            key=feedback_key_continuous,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(type="continuous", min=0, max=10),
        ),
    )

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            key=feedback_key_continuous,
            feedback_source=APIFeedbackSource(),
        ),
    )

    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=uuid4(),
                run_id=run,
                score=20,
                key=feedback_key_continuous,
                feedback_source=APIFeedbackSource(),
            ),
        )
    assert exc.value.status_code == 400

    feedback_key_init = random_lower_string()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            key=feedback_key_init,
            feedback_source=APIFeedbackSource(),
        ),
    )

    # should be continuous type
    row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key_init}'"""
    )
    assert row is not None
    assert row["tenant_id"] == auth_tenant_one.tenant_id
    assert row["feedback_key"] == feedback_key_init
    assert (
        orjson.loads(row["feedback_config"])
        == schemas.FeedbackConfig(type="continuous").model_dump()
    )


async def test_feedback_config_continuous_init_invalid(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback cannot be created with an invalid feedback_config."""

    # if the feedback is not valid, prevent upserting to the feedback config
    feedback_id = uuid4()
    feedback_key = random_lower_string()
    with pytest.raises(HTTPException):
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=2,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
                feedback_config=schemas.FeedbackConfig(type="continuous", min=0, max=1),
            ),
        )

    # make sure the feedback was not persisted
    feedback_row = await ch_client.fetchrow(
        f"SELECT * FROM feedbacks_rmt FINAL WHERE id = '{feedback_id}'"
    )
    assert feedback_row is None

    # make sure the feedback config is not persisted as well
    config_row = await ch_client.fetchrow(
        f"""SELECT * FROM feedback_configs FINAL
        WHERE tenant_id = '{auth_tenant_one.tenant_id}'
        AND feedback_key = '{feedback_key}'"""
    )
    assert config_row is None


async def test_feedback_config_categorical_invalid(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback cannot be created with an invalid feedback_config."""
    feedback_key = random_lower_string()

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            score=0,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(
                type="categorical",
                categories=[
                    schemas.FeedbackCategory(value=0, label="a"),
                    schemas.FeedbackCategory(value=0.5, label="b"),
                    schemas.FeedbackCategory(value=1, label="c"),
                ],
            ),
        ),
    )

    feedback_id = uuid4()
    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=2,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
            ),
        )
    assert exc.value.status_code == 400

    # make sure the feedback was not persisted
    feedback_row = await ch_client.fetchrow(
        f"SELECT * FROM feedbacks_rmt FINAL WHERE id = '{feedback_id}'"
    )
    assert feedback_row is None

    # reject the feedback even if the run is not ingested yet
    random_run_id = uuid4()
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": random_run_id.hex,
            "key": feedback_key,
            "score": 2,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 400


async def test_create_read_update_feedback_config(
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback config can be created and read."""
    feedback_key = random_lower_string()
    response = await http_tenant_one.post(
        "/feedback-configs/",
        json={
            "feedback_key": feedback_key,
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
        },
    )
    assert response.status_code == 200

    response = await http_tenant_one.get(
        "/feedback-configs/", params={"key": feedback_key}
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["feedback_key"] == feedback_key
    assert response.json()[0]["feedback_config"] == {
        "type": "continuous",
        "min": 0,
        "max": 1,
        "categories": None,
    }
    assert response.json()[0]["is_lower_score_better"] is False

    # Update the feedback config, send an invalid feedback key
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": random_lower_string(),
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
        },
    )
    assert response.status_code == 404

    # Update the feedback config, only the feedback config
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": feedback_key,
            "feedback_config": {
                "type": "categorical",
                "categories": [
                    {"value": 0, "label": "a"},
                    {"value": 0.5, "label": "b"},
                    {"value": 1, "label": "c"},
                ],
            },
        },
    )
    assert response.status_code == 200
    assert response.json()["feedback_key"] == feedback_key
    assert response.json()["feedback_config"] == {
        "type": "categorical",
        "max": None,
        "min": None,
        "categories": [
            {"value": 0, "label": "a"},
            {"value": 0.5, "label": "b"},
            {"value": 1, "label": "c"},
        ],
    }

    response = await http_tenant_one.get(
        "/feedback-configs/", params={"key": feedback_key}
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["feedback_key"] == feedback_key
    assert response.json()[0]["feedback_config"] == {
        "type": "categorical",
        "max": None,
        "min": None,
        "categories": [
            {"value": 0, "label": "a"},
            {"value": 0.5, "label": "b"},
            {"value": 1, "label": "c"},
        ],
    }
    assert response.json()[0]["is_lower_score_better"] is False

    # Update the feedback config is_lower_score_better
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={"feedback_key": feedback_key, "is_lower_score_better": True},
    )
    assert response.status_code == 200
    assert response.json()["feedback_key"] == feedback_key
    assert response.json()["is_lower_score_better"] is True

    response = await http_tenant_one.get(
        "/feedback-configs/", params={"key": feedback_key}
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["feedback_key"] == feedback_key
    assert response.json()[0]["feedback_config"] == {
        "type": "categorical",
        "max": None,
        "min": None,
        "categories": [
            {"value": 0, "label": "a"},
            {"value": 0.5, "label": "b"},
            {"value": 1, "label": "c"},
        ],
    }
    assert response.json()[0]["is_lower_score_better"] is True

    # Update the feedback config, update both the feedback config and is_lower_score_better
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": feedback_key,
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
            "is_lower_score_better": False,
        },
    )
    assert response.status_code == 200
    assert response.json()["feedback_key"] == feedback_key
    assert response.json()["feedback_config"] == {
        "type": "continuous",
        "min": 0,
        "max": 1,
        "categories": None,
    }
    assert response.json()["is_lower_score_better"] is False

    response = await http_tenant_one.get(
        "/feedback-configs/",
        params={"key": feedback_key},
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["feedback_key"] == feedback_key
    assert response.json()[0]["feedback_config"] == {
        "type": "continuous",
        "min": 0,
        "max": 1,
        "categories": None,
    }
    assert response.json()[0]["is_lower_score_better"] is False

    # Update with invalid feedback config
    response = await http_tenant_one.patch(
        "/feedback-configs/",
        json={
            "feedback_key": feedback_key,
            "feedback_config": {"foo": "bar"},
            "is_lower_score_better": True,
        },
    )
    assert response.status_code == 422

    # Create another feedback config
    feedback_key_two = random_lower_string()
    response = await http_tenant_one.post(
        "/feedback-configs/",
        json={
            "feedback_key": feedback_key_two,
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
        },
    )

    # Fetch both by feedback key
    response = await http_tenant_one.get(
        f"/feedback-configs/?key={feedback_key}&key={feedback_key_two}"
    )
    assert response.status_code == 200
    assert len(response.json()) == 2


async def test_get_feedback_invalid_id(http_tenant_one: AsyncClient) -> None:
    """Test that a feedback cannot be retrieved if the id is invalid."""
    response = await http_tenant_one.get(f"/feedback/{uuid4()}")
    assert response.status_code == 404


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
)
async def test_create_feedback_key_too_long(
    run: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a feedback can be created."""
    score = random.random()
    id = uuid4()
    await wait_until_task_queue_empty()
    long_key = "a" * 181

    response = await http_tenant_one.get(
        f"/runs/{run}",
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "id": str(id),
            "run_id": str(run),
            "key": long_key,
            "score": score,
            "feedback_source": {"type": "api"},
        },
    )

    assert response.status_code == 422


async def test_create_feedback_replaced_config(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback ."""
    feedback_key = random_lower_string()

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            score=0,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(type="continuous"),
        ),
    )

    # making sure the feedback config is not being created by default
    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=uuid4(),
                run_id=run,
                score=0,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
                feedback_config=schemas.FeedbackConfig(
                    type="categorical",
                    categories=[
                        schemas.FeedbackCategory(value=0, label="a"),
                        schemas.FeedbackCategory(value=0.5, label="b"),
                        schemas.FeedbackCategory(value=1, label="c"),
                    ],
                ),
            ),
        )
    assert exc.value.status_code == 400


async def test_create_feedback_replaced_config_run_in_queue(
    auth_tenant_one: AuthInfo,
    run: UUID,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback ."""
    feedback_key = random_lower_string()

    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            run_id=run,
            score=0,
            key=feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(type="continuous"),
        ),
    )

    # the run is not found, but make sure that we still conform
    # to the tenant config
    random_run_id = uuid4()
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "run_id": random_run_id.hex,
            "key": feedback_key,
            "feedback_source": {"type": "api"},
            "feedback_config": {
                "type": "categorical",
                "categories": [
                    {"value": 0, "label": "a"},
                    {"value": 0.5, "label": "b"},
                    {"value": 1, "label": "c"},
                ],
            },
        },
    )
    assert response.status_code == 400


async def test_create_feedback_normalize_key(
    auth_tenant_one: AuthInfo,
    run: UUID,
    ch_client: aiochclient.ChClient,
    http_tenant_one: AsyncClient,
) -> None:
    """Test that a feedback ."""
    correct_feedback_key = "\u00f1capitalisedａｂｃａｂｃ👨‍👨‍👦‍👦🇨🇿"
    raw_feedback_key = "  \u006e\u0303cApItaLisEdａｂｃＡＢＣ👨‍👨‍👦‍👦🇨🇿 "

    correct_uuid = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=correct_uuid,
            run_id=run,
            score=0,
            key=correct_feedback_key,
            feedback_source=APIFeedbackSource(),
            feedback_config=schemas.FeedbackConfig(type="continuous"),
        ),
    )

    # making sure the feedback config is not being created by default
    with pytest.raises(HTTPException) as exc:
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=uuid4(),
                run_id=run,
                score=0,
                key=raw_feedback_key,
                feedback_source=APIFeedbackSource(),
                feedback_config=schemas.FeedbackConfig(
                    type="categorical",
                    categories=[
                        schemas.FeedbackCategory(value=0, label="a"),
                        schemas.FeedbackCategory(value=0.5, label="b"),
                        schemas.FeedbackCategory(value=1, label="c"),
                    ],
                ),
            ),
        )
    assert exc.value.status_code == 400

    raw_uuid = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=raw_uuid,
            run_id=run,
            score=1,
            key=raw_feedback_key,
            feedback_source=APIFeedbackSource(),
        ),
    )

    rows = await ch_client.fetch(
        f"SELECT * FROM feedbacks_rmt FINAL WHERE id = '{correct_uuid}' or id = '{raw_uuid}'"
    )

    assert len(rows) == 2
    for row in rows:
        assert row["key"] == correct_feedback_key


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
)
async def test_create_feedback_after_run_persisted(
    run: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a feedback can be created."""
    score = round(random.random(), 4)
    id = uuid4()
    await wait_until_task_queue_empty()

    response = await http_tenant_one.get(
        f"/runs/{run}",
    )

    assert response.status_code == 200

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "id": str(id),
            "run_id": str(run),
            "key": "test",
            "score": score,
            "feedback_source": {"type": "api"},
            "error": True,
        },
    )

    assert response.status_code == 200
    await wait_until_task_queue_empty()

    feedback = await http_tenant_one.get(f"/feedback/{id}")
    assert feedback.status_code == 200
    assert feedback.json()["run_id"] == str(run)
    assert feedback.json()["score"] == score
    assert feedback.json()["extra"] == {"error": True}


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
)
async def test_create_feedback_while_run_in_queue(
    run: UUID,
    http_tenant_one: AsyncClient,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a feedback can be created."""
    score = round(random.random(), 4)
    id = uuid4()
    feedback_key = random_lower_string()

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "id": str(id),
            "run_id": str(run),
            "key": feedback_key,
            "score": score,
            "feedback_source": {"type": "api"},
            "feedback_config": {"type": "continuous", "min": 0, "max": 1},
        },
    )
    assert response.status_code == 200

    # make sure that an invalid feedback in queue is not persisted
    response = await http_tenant_one.post(
        "/feedback",
        json={
            "id": str(id),
            "run_id": str(run),
            "key": feedback_key,
            "score": 2,
            "feedback_source": {"type": "api"},
        },
    )
    assert response.status_code == 400

    await wait_until_task_queue_empty()

    feedback = await http_tenant_one.get(f"/feedback/{id}")
    assert feedback.status_code == 200
    assert feedback.json()["run_id"] == str(run)
    assert feedback.json()["score"] == score


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
)
async def test_delete_feedback(
    auth_tenant_one: AuthInfo,
    db_asyncpg: asyncpg.Connection,
    ch_client: aiochclient.ChClient,
    aclient: AsyncClient,
    run: UUID,
    tenant_one_headers: dict,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a feedback can be deleted."""
    feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=feedback_id,
            run_id=run,
            score=0.5,
            key="test",
            feedback_source=APIFeedbackSource(),
        ),
    )

    await wait_until_task_queue_empty()

    response = await aclient.delete(
        f"/feedback/{feedback_id}", headers=tenant_one_headers
    )
    assert response.status_code == 200
    rows = await db_asyncpg.fetch(
        "SELECT * FROM feedbacks WHERE id = $1",
        feedback_id,
    )
    assert len(rows) == 0

    # Check that the feedback was deleted
    response = await aclient.get(f"/feedback/{feedback_id}", headers=tenant_one_headers)
    assert response.status_code == 404


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_get_feedbacks(
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    tenant_one_headers: dict,
    run: UUID,
    run_session_two: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that all visible feedbacks can be retrieved."""
    valid_feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=valid_feedback_id,
            run_id=run,
            score=0.5,
            key="test",
            feedback_source=APIFeedbackSource(),
        ),
    )
    invalid_feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_two,
        schemas.FeedbackCreateSchemaInternal(
            id=invalid_feedback_id,
            run_id=run_session_two,
            score=0.5,
            key="test",
            feedback_source=APIFeedbackSource(),
        ),
    )
    await wait_until_task_queue_empty()

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        response = await client.get(
            "/feedback",
            headers=tenant_one_headers,
        )
    assert response.status_code == 200
    assert len(response.json()) > 0
    feedback_ids = set([feedback["id"] for feedback in response.json()])
    assert str(valid_feedback_id) in feedback_ids
    assert str(invalid_feedback_id) not in feedback_ids


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "oauth", reason="oauth not support in queue"
)
async def test_get_shared_feedbacks(
    auth_tenant_one: AuthInfo,
    tenant_one_headers: dict,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    http_tenant_one: AsyncClient,
) -> None:
    """Test that all visible feedbacks can be retrieved."""

    run = uuid4()
    run_two = uuid4()
    session_name = random_lower_string()
    run_two_session_name = random_lower_string()

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": session_name,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run),
        },
    )
    assert response.status_code == 202

    response = await http_tenant_one.post(
        "/runs",
        json={
            "name": "AgentExecutor",
            "start_time": "2023-05-05T05:13:24.571809",
            "end_time": "2023-05-05T05:13:32.022361",
            "extra": {"foo": "bar"},
            "error": None,
            "execution_order": 1,
            "serialized": {"name": "AgentExecutor"},
            "inputs": {"input": "How many people live in canada as of 2023?"},
            "outputs": {"output": "39,566,248"},
            "session_name": run_two_session_name,
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_two),
        },
    )
    assert response.status_code == 202

    await wait_until_task_queue_empty()

    valid_feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=valid_feedback_id,
            run_id=run,
            score=0.5,
            key="test",
            feedback_source=APIFeedbackSource(),
        ),
    )
    invalid_feedback_id = uuid4()
    await crud.create_feedback(
        auth_tenant_one,
        schemas.FeedbackCreateSchemaInternal(
            id=invalid_feedback_id,
            run_id=run_two,
            score=0.5,
            key="test",
            feedback_source=APIFeedbackSource(),
        ),
    )
    await wait_until_task_queue_empty()

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        share_response = await client.put(
            f"/runs/{str(run)}/share",
            headers=tenant_one_headers,
        )
        assert share_response.status_code == 200
        share_token = share_response.json()["share_token"]

        response = await client.get(
            f"/public/{share_token}/feedbacks",
            params={"limit": 10, "offset": 0, "id": str(run)},
            headers=tenant_one_headers,
        )
    assert response.status_code == 200
    assert len(response.json()) > 0
    feedback_ids = set([feedback["id"] for feedback in response.json()])
    assert str(valid_feedback_id) in feedback_ids
    assert str(invalid_feedback_id) not in feedback_ids


@pytest.mark.skipif(config.settings.AUTH_TYPE != "supabase", reason="requires user_id")
async def test_get_feedback_filter(
    tenant_one_headers: dict,
    auth_tenant_one: AuthInfo,
    run: UUID,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    """Test filtering functionality for feedbacks."""
    user_id = auth_tenant_one.user_id
    min_created_at = "2023-05-05T05:13:24.571809"
    max_created_at = "2023-05-05T05:15:24.571809"

    with (
        mock.patch.object(settings, "FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL", False),
    ):
        async with AsyncClient(
            transport=ASGITransport(app=app), base_url="http://test"
        ) as client:
            response = await client.post(
                "/feedback",
                headers=tenant_one_headers,
                json={
                    "run_id": run.hex,
                    "score": 0.5,
                    "key": "test_app",
                    "feedback_source": {
                        "type": "app",
                    },
                    "created_at": min_created_at,
                },
            )
            assert response.status_code == 200

            response = await client.post(
                "/feedback",
                headers=tenant_one_headers,
                json={
                    "run_id": run.hex,
                    "key": "test_api",
                    "comment": "hello world",
                    "feedback_source": {
                        "type": "api",
                    },
                    "created_at": max_created_at,
                },
            )
            assert response.status_code == 200

            await wait_until_task_queue_empty()

            # Test by min_created_at and max_created_at
            response = await client.get(
                f"/feedback?min_created_at={min_created_at}&max_created_at={max_created_at}&key=test_api&key=test_app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 2

            # Test by min_created_at
            response = await client.get(
                f"/feedback?min_created_at={min_created_at}&key=test_api&key=test_app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 2

            # Test by max_created_at
            response = await client.get(
                f"/feedback?max_created_at={max_created_at}&key=test_api&key=test_app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 2

            # Test case for invalid min_created_at
            response = await client.get(
                "/feedback?min_created_at=2023-05-05T05:16:24.571809&key=test_api&key=test_app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 0

            # Test case for invalid max_created_at
            response = await client.get(
                "/feedback?max_created_at=2023-05-05T05:12:24.571809&key=test_api&key=test_app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 0

            # Test source filter
            response = await client.get(
                "/feedback?source=app",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert feedback["feedback_source"]["type"] == "app"

            response = await client.get(
                "/feedback?source=api",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert feedback["feedback_source"]["type"] == "api"

            # Test key filter
            response = await client.get(
                "/feedback?key=test_api",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0

            response = await client.get(
                "/feedback?key=test_api_invalid",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) == 0

            # Test comment filter
            response = await client.get(
                "/feedback?has_comment=true",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert feedback["comment"]

            response = await client.get(
                "/feedback?has_comment=false",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert not feedback["comment"]

            # Test score sanity check
            response = await client.get(
                "/feedback?has_score=true",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert feedback["score"] is not None

            response = await client.get(
                "/feedback?has_score=false",
                headers=tenant_one_headers,
            )
            assert response.status_code == 200
            assert len(response.json()) > 0
            for feedback in response.json():
                assert feedback["score"] is None

            # test user filter
            if not use_api_key:
                response = await client.get(
                    "/feedback?source=app&user=" + str(user_id),
                    headers=tenant_one_headers,
                )
                assert response.status_code == 200
                assert len(response.json()) > 0
                for feedback in response.json():
                    assert feedback["feedback_source"]["type"] == "app"
                    assert feedback["feedback_source"]["user_id"] == str(user_id)

                response = await client.get(
                    "/feedback?source=app&user=" + str(uuid4()),
                    headers=tenant_one_headers,
                )
                assert response.status_code == 200
                assert len(response.json()) == 0

                response = await client.get(
                    "/feedback?source=foo" + str(user_id),
                    headers=tenant_one_headers,
                )
                assert response.status_code == 422


@pytest.mark.skipif(config.settings.AUTH_TYPE != "supabase", reason="requires user_id")
async def test_get_feedback_user_name(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    """Test user name is included if requested for feedbacks."""
    if use_api_key:
        pytest.skip("Requires user ID")

    async with fresh_tenant_client(
        db_asyncpg, use_api_key=use_api_key
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client
        user_id = auth.user_id
        user_name = auth.user_full_name
        assert user_name is not None, "Test requires a user name"

        run_id = await create_test_run(auth, uuid4())
        response = await client.post(
            "/feedback",
            json={
                "run_id": run_id.hex,
                "score": 0.5,
                "key": "test_app",
                "feedback_source": {
                    "type": "app",
                },
            },
        )
        assert response.status_code == 200
        # Should be able to read feedback with null source
        response = await client.post(
            "/feedback",
            json={
                "run_id": run_id.hex,
                "score": 0.5,
                "key": "test_no_source",
                "feedback_source": None,
            },
        )
        assert response.status_code == 200

        await wait_until_task_queue_empty()

        response = await client.get(
            "/feedback?include_user_names=true",
        )
        assert response.status_code == 200
        res = response.json()
        assert len(res) == 2
        feedbacks = [schemas.FeedbackSchema(**f) for f in res]
        assert "test_app" in [f.key for f in feedbacks]
        assert "test_no_source" in [f.key for f in feedbacks]
        feedback_app = next(f for f in feedbacks if f.key == "test_app")
        assert feedback_app.feedback_source is not None
        assert user_id == feedback_app.feedback_source.user_id
        assert user_name == feedback_app.feedback_source.user_name, (
            f"User's name should be resolved from {user_id=}"
        )
        feedback_no_source = next(f for f in feedbacks if f.key == "test_no_source")
        assert feedback_no_source.feedback_source is None


@pytest.mark.skipif(config.settings.AUTH_TYPE != "supabase", reason="requires user_id")
async def test_no_feedback_in_run_without_permissions(
    http_tenant_low_payload_size_limit: AsyncClient,
    auth_tenant_low_payload_size_limit: AuthInfo,
    run_three: UUID,
    wait_until_task_queue_empty,
    use_api_key: bool,
):
    """Test user without feedback read permissions cannot see feedback in run"""
    if use_api_key:
        pytest.skip("Api keys do not have a role")

    response = await http_tenant_low_payload_size_limit.post(
        "/feedback",
        json={
            "run_id": str(run_three),
            "key": "test_api",
            "comment": "hello world",
            "feedback_source": {
                "type": "api",
            },
        },
    )
    assert response.status_code == 200

    # Create a new role with runs:read permission
    response = await http_tenant_low_payload_size_limit.post(
        "/orgs/current/roles",
        json={
            "display_name": "runs reader",
            "description": "test role description",
            "permissions": ["runs:read", "workspaces:read"],
        },
    )
    assert response.status_code == 200
    new_role = Role(**response.json())
    assert new_role.display_name == "runs reader"
    assert new_role.description == "test role description"

    # Create a new user
    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {jwt}",
    }
    response = await http_tenant_low_payload_size_limit.get(
        "/tenants", headers=new_user_headers
    )

    assert response.status_code == 200

    # Invite user to tenant
    response = await http_tenant_low_payload_size_limit.post(
        "/orgs/current/members",
        json={
            "email": new_user_email,
            "workspace_ids": [str(auth_tenant_low_payload_size_limit.tenant_id)],
            "workspace_role_id": str(new_role.id),
        },
    )
    assert response.status_code == 200

    # Claim invite
    response = await http_tenant_low_payload_size_limit.post(
        f"/workspaces/pending/{auth_tenant_low_payload_size_limit.tenant_id}/claim",
        headers=new_user_headers,
    )
    assert response.status_code == 200

    await wait_until_task_queue_empty()

    # Verify normal user can fetch feedback
    response = await http_tenant_low_payload_size_limit.post(
        "/runs/query", json={"id": [str(run_three)]}
    )
    assert response.status_code == 200
    run = response.json()["runs"][0]
    # assert feedback is present
    assert run["feedback_stats"]

    # Verify normal user can fetch feedback single run
    response = await http_tenant_low_payload_size_limit.get(f"/runs/{str(run_three)}")
    assert response.status_code == 200
    # assert feedback is present
    assert response.json()["feedback_stats"]

    # Verify user can fetch runs without feedback
    response = await http_tenant_low_payload_size_limit.post(
        "/runs/query", json={"id": [str(run_three)]}, headers=new_user_headers
    )
    assert response.status_code == 200
    run = response.json()["runs"][0]
    # assert no feedback
    assert run["feedback_stats"] is None

    # Verify no feedback perms user can fetch feedback single run
    response = await http_tenant_low_payload_size_limit.get(
        f"/runs/{str(run_three)}", headers=new_user_headers
    )
    assert response.status_code == 200
    # assert no feedback
    assert response.json()["feedback_stats"] is None


async def test_create_session_lvl_feedback(
    http_tenant_one: AsyncClient,
) -> None:
    response = await http_tenant_one.post(
        "/sessions",
        json={"name": random_lower_string()},
    )

    assert response.status_code == 200

    session_id = response.json()["id"]

    response = await http_tenant_one.post(
        "/feedback",
        json={
            "session_id": session_id,
            "key": "test",
            "score": 0.5,
            "feedback_source": {"type": "api"},
        },
    )

    assert response.status_code == 200

    response = await http_tenant_one.get("/feedback", params={"session": session_id})

    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["session_id"] == session_id
    assert response.json()[0]["run_id"] is None
    assert response.json()[0]["key"] == "test"
    assert response.json()[0]["score"] == 0.5
    assert response.json()[0]["feedback_source"]["type"] == "api"

    response = await http_tenant_one.get(
        "/feedback", params={"session": session_id, "level": "session"}
    )

    assert response.status_code == 200, response.text
    assert len(response.json()) == 1
    assert response.json()[0]["session_id"] == session_id
    assert response.json()[0]["run_id"] is None
    assert response.json()[0]["key"] == "test"
    assert response.json()[0]["score"] == 0.5
    assert response.json()[0]["feedback_source"]["type"] == "api"

    response = await http_tenant_one.get(
        "/feedback", params={"session": session_id, "level": "run"}
    )

    assert response.status_code == 200, response.text
    assert len(response.json()) == 0


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_select_sequential_consistency(
    auth_tenant_one: AuthInfo, run: UUID, http_tenant_one: AsyncClient, mocker
) -> None:
    with (
        mock.patch.object(settings, "FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL", False),
    ):
        """Test that a feedback can be retrieved."""
        feedback_key = random_lower_string()

        response = await http_tenant_one.post(
            "/feedback",
            json={
                "run_id": str(run),
                "key": feedback_key,
                "feedback_config": {"type": "continuous", "min": 0, "max": 1},
                "expires_in": {"minutes": 5},
            },
        )
        assert response.status_code == 200, response.text
        async with redis.aredis_pool() as aredis:
            redis_feedback_update = await aredis.get(
                f"smith:feedback_update:{auth_tenant_one.tenant_id}:{str(run)}"
            )
            assert redis_feedback_update.decode("utf-8") == "true"

        response = await http_tenant_one.get(
            "/feedback", params={"key": feedback_key, "run": [run]}
        )
        assert response.status_code == 200, response.text

        # Mock the fetch_feedbacks function
        mock_fetch_feedbacks = mocker.patch(
            "app.api.endpoints.feedback.fetch_feedbacks", return_value=([], 0)
        )

        # Call read_feedbacks endpoint
        mocked_response = await http_tenant_one.get(
            "/feedback", params={"run": [str(run)]}
        )
        assert mocked_response.status_code == 200

        # Verify fetch_feedbacks was called with use_select_sequential_consistency = True
        mock_fetch_feedbacks.assert_called_once()
        args, _ = mock_fetch_feedbacks.call_args
        assert args[1].use_select_sequential_consistency is True

        # wait for the feedback update key to expire
        await asyncio.sleep(settings.REDIS_FEEDBACK_UPDATE_TTL + 1)

        mocked_response = await http_tenant_one.get(
            "/feedback", params={"run": [str(run)]}
        )
        assert mocked_response.status_code == 200

        # verify that the fetch_feedbacks was called with use_select_sequential_consistency = False
        args, _ = mock_fetch_feedbacks.call_args
        assert args[1].use_select_sequential_consistency is False

        response = await http_tenant_one.patch(
            f"/feedback/{response.json()[0]['id']}",
            json={
                "run_id": str(run),
            },
        )
        assert response.status_code == 200, response.text
        async with redis.aredis_pool() as aredis:
            redis_feedback_update = await aredis.get(
                f"smith:feedback_update:{auth_tenant_one.tenant_id}:{str(run)}"
            )
            assert redis_feedback_update.decode("utf-8") == "true"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_stored_postgres(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    async with fresh_tenant_client(
        db_asyncpg, use_api_key=use_api_key
    ) as authed_client:
        auth = authed_client.auth
        run_id = await create_test_run(auth, uuid4())
        user_id = auth.user_id

        assert user_id is not None

        await wait_until_task_queue_empty()

        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": run_id.hex,
                "score": 0.5,
                "key": "test_app",
                "feedback_source": {
                    "type": "app",
                },
            },
        )
        assert response.status_code == 200

        # query postgres
        rows = await db_asyncpg.fetch(
            "SELECT * FROM feedbacks WHERE run_id = $1 AND tenant_id = $2",
            run_id,
            auth.tenant_id,
        )
        assert len(rows) == 1
        assert rows[0]["key"] == "test_app"
        assert float(rows[0]["score"]) == 0.5
        assert rows[0]["user_id"] == user_id
        # PATCH the feedback
        response = await authed_client.client.patch(
            f"/feedback/{rows[0]['id']}",
            json={"score": 0.6},
        )
        assert response.status_code == 200

        # query postgres
        rows = await db_asyncpg.fetch(
            "SELECT * FROM feedbacks WHERE run_id = $1 AND tenant_id = $2",
            run_id,
            auth.tenant_id,
        )
        assert len(rows) == 1
        assert float(rows[0]["score"]) == 0.6
        assert rows[0]["user_id"] == user_id

        # Create a tracer session
        session_id = await create_session(authed_client.client)

        # POST a summary feedback
        response = await authed_client.client.post(
            "/feedback",
            json={
                "run_id": None,
                "session_id": session_id,
                "score": 0.5,
                "value": "string",
                "correction": {"foo": "bar"},
                "key": "test_summary",
                "feedback_source": {
                    "type": "api",
                },
            },
        )
        assert response.status_code == 200

        # query postgres
        rows = await db_asyncpg.fetch(
            "SELECT * FROM feedbacks WHERE run_id IS NULL AND session_id = $1 AND tenant_id = $2",
            session_id,
            auth.tenant_id,
        )
        assert len(rows) == 1
        row = rows[0]
        assert row["key"] == "test_summary"
        assert float(row["score"]) == 0.5
        assert row["run_id"] is None
        assert row["start_time"] is None
        assert row["trace_id"] is None
        assert row["user_id"] == user_id
        assert row["value"] == "string"
        assert row["correction"] == '{"foo":"bar"}'


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_filter_matrix_postgres(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    with (
        mock.patch.object(settings, "FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL", True),
    ):
        async with fresh_tenant_client(db_asyncpg, use_api_key=use_api_key) as authed:
            auth = authed.auth
            session_id = await create_session(authed.client)

            run_id = uuid4()
            run_data = {
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "error": "an error",
                "execution_order": 1,
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248 people"},
                "session_id": session_id,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
                "dotted_order": f"20230505T051324571809Z{run_id}",
                "trace_id": str(run_id),
            }

            feedback_rows = []

            # 6 keys × 3 rows
            for key_idx in range(1, 7):
                for row_idx in range(3):
                    feedback_rows.append(
                        {
                            "id": str(uuid4()),
                            "run_id": str(run_id),
                            "trace_id": str(run_id),
                            "key": f"k{key_idx}",
                            "score": 1 if key_idx != 5 else None,
                            "value": "text" if key_idx == 5 else None,
                            "correction": {"foo": "bar"} if key_idx == 5 else None,
                            "comment": "foo" if row_idx == 0 else None,
                            "feedback_source": {
                                "type": "api" if key_idx % 2 else "app",
                            },
                        }
                    )

            print("feedback_rows", feedback_rows)

            # multipart bulk insert
            await post_runs(
                "/runs/multipart",
                authed.client,
                post=[run_data],
                patch_runs=[],
                feedback=feedback_rows,
            )

            await wait_until_task_queue_empty()

            # 1 summary feedback
            response = await authed.client.post(
                "/feedback",
                json={
                    "run_id": None,
                    "session_id": session_id,
                    "score": 0.5,
                    "key": "test_summary",
                    "feedback_source": {
                        "type": "api",
                    },
                },
            )
            assert response.status_code == 200

            matrix: list[tuple[dict[str, Any], int]] = [
                ({"key": "k1"}, 3),
                ({"run": str(run_id)}, 18),
                ({"session": session_id}, 19),
                ({"source": "app"}, 9),
                ({"has_comment": True}, 6),
                ({"has_comment": False}, 13),
                ({"has_score": True}, 16),
                ({"has_score": False}, 3),
                ({"key": "k1", "has_comment": True}, 1),
                ({"key": "k5", "has_score": False, "has_comment": False}, 2),
                ({"run": str(run_id), "source": "app", "has_score": True}, 9),
                ({"session": session_id, "source": "api", "has_comment": False}, 7),
                ({"user_id": str(auth.user_id)}, 19),
                ({"user": str(auth.user_id)}, 1),
            ]

            for params, expected in matrix:
                qs = "&".join(f"{k}={quote_plus(str(v))}" for k, v in params.items())
                r = await authed.client.get(f"/feedback?{qs}")
                assert r.status_code == 200, f"Bad status for params={params}"
                assert len(r.json()) == expected, f"Bad count for params={params}"


@pytest.mark.parametrize("score", [100_000, 0.00001])
@pytest.mark.parametrize("multiplier", [1, -1])
async def test_invalid_feedback_scores(multiplier: int, score: int | float):
    invalid_value = multiplier * score
    with pytest.raises(ValidationError):
        schemas.FeedbackCreateSchema(
            key="some-key",
            score=invalid_value,
            run_id=uuid4(),
        )

    with pytest.raises(ValidationError):
        schemas.FeedbackUpdateSchema(
            key="some-key",
            score=invalid_value,
        )

    with pytest.raises(ValidationError):
        schemas.FeedbackCreateCoreSchema(
            key="some-key",
            score=invalid_value,
        )

    with pytest.raises(ValidationError):
        schemas.FeedbackConfig(
            type=schemas.FeedbackType.continuous,
            min=invalid_value,
        )

    with pytest.raises(ValidationError):
        schemas.FeedbackConfig(
            type=schemas.FeedbackType.continuous,
            max=invalid_value,
        )


@pytest.mark.parametrize(
    "score",
    [
        0.1234,
        0,
        1,
        99999.9999,
    ],
)
@pytest.mark.parametrize("multiplier", [1, -1])
async def test_valid_feedback_scores(multiplier: int, score: int | float):
    value = multiplier * score
    assert (
        schemas.FeedbackCoreSchema(
            key="some-key",
            score=value,
        )
        is not None
    )
    assert (
        schemas.FeedbackCreateSchema(
            key="some-key",
            score=value,
            run_id=uuid4(),
        )
        is not None
    )
    assert (
        schemas.FeedbackUpdateSchema(
            key="some-key",
            score=value,
        )
        is not None
    )
    assert (
        schemas.FeedbackCreateCoreSchema(
            key="some-key",
            score=value,
        )
        is not None
    )

    assert (
        schemas.FeedbackConfig(
            type=schemas.FeedbackType.continuous,
            min=value,
        )
        is not None
    )

    assert (
        schemas.FeedbackConfig(
            type=schemas.FeedbackType.continuous,
            max=value,
        )
        is not None
    )


@pytest.mark.asyncio
@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_persisted_after_redis_eviction(
    http_tenant_one,  # AsyncClient fixture
    auth_tenant_one,  # AuthInfo fixture
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
):
    """
    1. Create a run through /runs/multipart (so it’s stored in CH/PG + Redis).
    2. Delete every Redis key associated with that run.
    3. Post feedback *only* (no run).
    4. Ensure the feedback is persisted and fetchable.
    """
    ingest_endpoint = "/runs/multipart|go"

    # ── 1. Create a tracer‑session and a run ────────────────────────────────────
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    run_id = uuid4()

    run_data = {
        "id": str(run_id),
        "name": "Unit‑test run",
        "run_type": "chain",
        "start_time": "2025-05-02T10:00:00Z",
        "end_time": "2025-05-02T10:00:01Z",
        "execution_order": 1,
        "inputs": {"foo": "bar"},
        "outputs": {"bar": "baz"},
        "session_id": str(session.id),
        "trace_id": str(run_id),
        "dotted_order": f"20250502T100000000000Z{run_id}",
        "parent_run_id": None,
    }

    await post_runs(ingest_endpoint, http_tenant_one, post=[run_data])
    await wait_until_task_queue_empty()

    # ── 2. Evict every Redis key related to this run ────────────────────────────
    async with redis.aredis_routed_pool(
        str(auth_tenant_one.tenant_id),
        redis_operation=redis.RedisOperation.WRITE,
    ) as aredis:
        keys = [
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}",
            f"smith:runs:pending:{auth_tenant_one.tenant_id}:{run_id}:extra",
            f"smith:runs:trace_runs:{auth_tenant_one.tenant_id}:{run_id}",
            f"smith:runs:children:{auth_tenant_one.tenant_id}:{run_id}",
        ]
        await aredis.delete(*keys)

    # ── 3. Post feedback *only* (no run payload) ────────────────────────────────
    feedback_id = uuid4()
    feedback_payload = {
        "id": str(feedback_id),
        "run_id": str(run_id),
        "trace_id": str(run_id),
        "key": "correctness",
        "score": 1,
    }

    await post_runs(
        ingest_endpoint,
        http_tenant_one,
        post=[],
        feedback=[feedback_payload],
    )
    await wait_until_task_queue_empty()

    # ── 4. Confirm feedback was written to the DB ───────────────────────────────
    # Force PG fetch path so we know the DB is the source of truth.
    fb = await models.feedback.fetch.fetch_feedback_postgres(
        UUID(str(feedback_id)), auth_tenant_one
    )

    assert fb.key == "correctness"
    assert fb.score == 1
    assert fb.run_id == run_id


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single tenant")
async def test_feedback_config_stored_postgres(
    db_asyncpg,
    use_api_key: bool,
):
    """
    Verify feedback-config creation, update, and conflict-upsert are persisted
    to postgres
    """
    async with fresh_tenant_client(db_asyncpg, use_api_key=use_api_key) as authed:
        auth = authed.auth
        client = authed.client
        feedback_key = random_lower_string()

        # ── 1. Create a feedback-config ───────────────────────────────────
        r = await client.post(
            "/feedback-configs/",
            json={
                "feedback_key": feedback_key,
                "feedback_config": {"type": "continuous", "min": 0, "max": 1},
            },
        )
        assert r.status_code == 200, r.text

        row = await db_asyncpg.fetchrow(
            """
            SELECT feedback_config, is_lower_score_better
            FROM feedback_configs
            WHERE tenant_id = $1 AND feedback_key = $2
            """,
            auth.tenant_id,
            feedback_key,
        )
        assert row is not None
        assert orjson.loads(row["feedback_config"]) == {
            "type": "continuous",
            "min": 0.0,
            "max": 1.0,
            "categories": None,
        }
        assert row["is_lower_score_better"] is False

        # ── 2. Patch only the `is_lower_score_better` flag ────────────────
        r = await client.patch(
            "/feedback-configs/",
            json={"feedback_key": feedback_key, "is_lower_score_better": True},
        )
        assert r.status_code == 200, r.text

        row = await db_asyncpg.fetchrow(
            """
            SELECT is_lower_score_better
            FROM feedback_configs
            WHERE tenant_id = $1 AND feedback_key = $2
            """,
            auth.tenant_id,
            feedback_key,
        )
        assert row["is_lower_score_better"] is True


async def test_feedback_config_postgres(
    auth_tenant_one: AuthInfo,
    run: UUID,
    db_asyncpg: asyncpg.Connection,
) -> None:
    with (
        mock.patch.object(
            settings, "FF_USE_PG_FOR_FEEDBACK_CONFIGS_FETCH_ENABLED_ALL", True
        ),
        mock.patch.object(
            settings, "FF_USE_PG_FOR_FEEDBACK_CONFIGS_UPSERT_ENABLED_ALL", True
        ),
    ):
        feedback_id = uuid4()
        feedback_key = random_lower_string()
        await crud.create_feedback(
            auth_tenant_one,
            schemas.FeedbackCreateSchemaInternal(
                id=feedback_id,
                run_id=run,
                score=0,
                key=feedback_key,
                feedback_source=APIFeedbackSource(),
                feedback_config=schemas.FeedbackConfig(
                    type="categorical",
                    categories=[
                        schemas.FeedbackCategory(value=0, label="bad"),
                        schemas.FeedbackCategory(value=1, label="good"),
                    ],
                ),
            ),
        )

    postgres_row = await db_asyncpg.fetchrow(
        """
        SELECT * FROM feedback_configs
        WHERE tenant_id = $1 AND feedback_key = $2
        """,
        auth_tenant_one.tenant_id,
        feedback_key,
    )
    assert postgres_row is not None
    assert postgres_row["feedback_key"] == feedback_key
    assert postgres_row["tenant_id"] == auth_tenant_one.tenant_id

    assert (
        orjson.loads(postgres_row["feedback_config"])
        == schemas.FeedbackConfig(
            type="categorical",
            categories=[
                schemas.FeedbackCategory(value=0, label="bad"),
                schemas.FeedbackCategory(value=1, label="good"),
            ],
        ).model_dump()
    )


async def test_batched_feedback_go(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    session = await crud.create_tracer_session(
        auth_tenant_one,
        schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
    )
    tenant_one_tracer_session_id = session.id
    run_id = uuid4()
    inputs = {"input": "How many people live in canada as of 2023?"}
    outputs = {"output": "39,566,248 people"}
    events = [
        {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
        {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
    ] * 1000
    serialized = {"name": "AgentExecutor"}

    run_data = {
        "name": "LLM",
        "start_time": "2023-05-05T05:13:24.571809",
        "end_time": "2023-05-05T05:13:32.022361",
        "extra": {"foo": "bar", "metadata": {"conversation_id": "112233"}},
        "error": "an error",
        "execution_order": 1,
        "serialized": serialized,
        "inputs": inputs,
        "outputs": outputs,
        "events": events,
        "session_id": str(tenant_one_tracer_session_id),
        "parent_run_id": None,
        "run_type": "chain",
        "id": str(run_id),
    }

    run_data["dotted_order"] = f"20230505T051324571809Z{run_id}"
    run_data["trace_id"] = str(run_id)

    await post_runs(
        "/runs/multipart|go",
        http_tenant_one,
        post=[run_data],
    )

    await wait_until_task_queue_empty()

    forward_headers = dict(http_tenant_one.headers or {})
    feedback_id = uuid4()
    body = orjson.dumps(
        [
            {
                "id": str(feedback_id),
                "run_id": str(run_id),
                "trace_id": str(run_id),
                "key": "correctness",
                "score": 1,
                "value": {"foo": "bar"},
                "comment": "This is a comment",
                "correction": {"foo": "baz"},
            }
        ]
    )

    response = await platform_request(
        "POST",
        "/feedback/batch",
        headers=forward_headers,
        body=body,
        raise_error=False,
    )
    assert response.code == 202, response.body

    await wait_until_task_queue_empty()

    fb = await models.feedback.fetch.fetch_feedback(feedback_id, auth_tenant_one)
    assert fb.key == "correctness"
    assert fb.score == 1
    assert fb.run_id == run_id
    assert fb.value == {"foo": "bar"}
    assert fb.comment == "This is a comment"
    assert fb.correction == {"foo": "baz"}
    assert fb.session_id == tenant_one_tracer_session_id
    assert fb.id == feedback_id
    assert fb.trace_id == run_id
