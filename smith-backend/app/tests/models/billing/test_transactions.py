import asyncio
import datetime
import logging
import typing
import uuid
from contextlib import asynccontextmanager
from itertools import chain
from typing import Any, Callable, NamedTuple, cast
from unittest.mock import MagicMock, patch

import httpx
import orjson
import pytest
from asyncpg.exceptions import ExclusionViolationError
from freezegun import freeze_time
from httpx import HTTPStatusError, Response
from httpx._client import USE_CLIENT_DEFAULT, UseClientDefault
from httpx._types import (
    AuthTypes,
    CookieTypes,
    HeaderTypes,
    QueryParamTypes,
    RequestContent,
    RequestData,
    RequestExtensions,
    RequestFiles,
    TimeoutTypes,
    URLTypes,
)
from lc_config.service_communication_settings import (
    ServiceName,
)
from lc_config.tenant_config import TenantConfig
from lc_database import clickhouse, metronome
from lc_database.clickhouse import ClickhouseClient
from lc_database.database import asyncpg_conn

from app import config, crud, schemas
from app.api.auth.schemas import TenantlessAuthInfo
from app.models.billing.usagereporting import seats, traces
from app.models.billing.usagereporting.transactions import UsageReportingStatus
from app.models.constants import CH_INSERT_TIME
from app.models.identities.seat_txn import (
    SeatChangeBillingEvent,
    SeatChangeOperation,
)
from app.tests.conftest import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
)
from app.tests.ensure import DecodedUserInfo, ensure_user

pytestmark = pytest.mark.serial


class BillableTraceRow(NamedTuple):
    trace_id: uuid.UUID
    session_id: uuid.UUID
    start_time: datetime.datetime
    tenant_id: uuid.UUID
    inserted_at: datetime.datetime
    table_name: str


class TraceTransactionProcessingTestCase(NamedTuple):
    name: str
    existing_traces: list[BillableTraceRow]
    existing_transactions: list[traces.TraceTransactionWithOrg]
    current_time: datetime.datetime
    expected_transactions: list[traces.TraceTransactionWithOrg]
    trace_processor: type[traces.TraceTransactionProcessor]


logger = logging.getLogger(__name__)

TENANT_ONE_ID = uuid.UUID("00000000-0000-0000-0001-000000000000")
ORG_ONE_ID = uuid.UUID("00000000-1000-0000-0000-000000000000")
METRONOME_CUSTOMER_ONE_ID = uuid.UUID("10000000-0000-0000-0000-000000000000")

TENANT_TWO_ID = uuid.UUID("00000000-0000-0000-0002-000000000000")
ORG_TWO_ID = uuid.UUID("00000000-**************-000000000000")
METRONOME_CUSTOMER_TWO_ID = uuid.UUID("*************-0000-0000-000000000000")

METRONOME_CUSTOMERLESS_TENANT_ID = uuid.UUID("00000000-0000-0000-0004-000000000000")
METRONOME_CUSTOMERLESS_ORG_ID = uuid.UUID("00000000-**************-000000000000")

SELF_HOSTED_TENANT_ID = uuid.UUID("00000000-0000-0000-0005-000000000000")
SELF_HOSTED_ORG_ID = uuid.UUID("00000000-**************-000000000000")
SELF_HOSTED_CUSTOMER_ID = uuid.UUID("00000000-**************-000000000000")
SELF_HOSTED_LICENSE_ID = uuid.UUID("12300000-0000-0000-0000-000000000000")
SELF_HOSTED_METRONOME_CUSTOMER_ID = uuid.UUID("*************-0000-0000-000000000000")

TRACE_ONE_ID = uuid.UUID("00000000-0000-0000-0000-000000000001")
TRACE_TWO_ID = uuid.UUID("00000000-0000-0000-0000-000000000002")
TRACE_THREE_ID = uuid.UUID("00000000-0000-0000-0000-000000000003")

SESSION_ONE_ID = uuid.UUID("00000000-0000-0001-0000-000000000000")
SESSION_TWO_ID = uuid.UUID("00000000-0000-0002-0000-000000000000")

TZ_UTC = datetime.timezone.utc

FIRST_INTERVAL_START = datetime.datetime(2022, 2, 22, 0, 0, 0, tzinfo=TZ_UTC)

SHARED_TRACE_TRANSACTION_PROCESSING_TEST_CASES = chain.from_iterable(
    [
        TraceTransactionProcessingTestCase(
            name="test transaction grouping within a tenant",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=1),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=2,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=4),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                )
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test that traces are grouped into separate transactions for different tenants and orgs",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_TWO_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_TWO_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=0),
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_TWO_ID,
                    session_id=SESSION_TWO_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=4),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=4),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_TWO_ID,
                ),
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test grouping traces into separate intervals within a tenant",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=8),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=8),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=8),
                    start_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=10),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test skipping intervals within a tenant",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=13),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=13),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=13),
                    start_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=10),
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=15),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test grouping traces into separate sessions within a tenant",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_TWO_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_TWO_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test billable traces are deduplicated using the earliest insertion time",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=8),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                )
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            "only process intervals that have not yet been processed",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                # Add in a second trace that was not previously counted. This should
                # not be included in the transaction, since it was inserted after the
                # transaction was processed
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                ),
            ],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    # Only include the trace that was inserted before the transaction
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                )
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test that transactions are not processed if the interval is not yet complete",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=1),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
            expected_transactions=[],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test that transactions are not processed if the delay period has not yet occurred even if the interval is complete",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=6),
            expected_transactions=[],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="test that transactions are processed as soon as the delay period has occurred",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=7),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=4),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=4),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                )
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
    ]
    for trace_processor in [
        traces.AllTracesTraceProcessor,
        traces.LonglivedTracesTraceProcessor,
    ]
)

CROSS_TRACE_TYPE_PROCESSING_TEST_CASES = chain.from_iterable(
    [
        TraceTransactionProcessingTestCase(
            name="ignore other types of traces",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=other_trace_type_table,
                ),
                BillableTraceRow(
                    trace_id=TRACE_TWO_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=1),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                    table_name=other_trace_type_table,
                ),
            ],
            existing_transactions=[],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[],
            trace_processor=trace_processor,  # type: ignore
        ),
        TraceTransactionProcessingTestCase(
            name="process starting at last known window for own trace type",
            existing_traces=[
                BillableTraceRow(
                    trace_id=TRACE_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
                    table_name=trace_processor.get_clickhouse_transaction_table_name(),
                ),
                BillableTraceRow(
                    trace_id=TRACE_THREE_ID,
                    session_id=SESSION_ONE_ID,
                    start_time=FIRST_INTERVAL_START - datetime.timedelta(days=2),
                    tenant_id=TENANT_ONE_ID,
                    inserted_at=FIRST_INTERVAL_START + datetime.timedelta(minutes=13),
                    table_name=other_trace_type_table,
                ),
            ],
            existing_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=13),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=13),
                    start_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=10),
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=15),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=other_trace_type_table,
                    organization_id=ORG_ONE_ID,
                ),
            ],
            current_time=FIRST_INTERVAL_START + datetime.timedelta(hours=1),
            expected_transactions=[
                traces.TraceTransactionWithOrg(
                    id=uuid.uuid4(),  # not validated
                    tenant_id=TENANT_ONE_ID,
                    session_id=SESSION_ONE_ID,
                    trace_count=1,
                    start_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    end_insertion_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=3),
                    start_interval_time=FIRST_INTERVAL_START,
                    end_interval_time=FIRST_INTERVAL_START
                    + datetime.timedelta(minutes=5),
                    status=trace_processor._get_post_processing_status(),
                    transaction_type=trace_processor.get_transaction_type(),
                    organization_id=ORG_ONE_ID,
                )
            ],
            trace_processor=trace_processor,  # type: ignore
        ),
    ]
    for trace_processor, other_trace_type_table in [
        (
            traces.AllTracesTraceProcessor,
            traces.LonglivedTracesTraceProcessor.get_clickhouse_transaction_table_name(),
        ),
        (
            traces.LonglivedTracesTraceProcessor,
            traces.AllTracesTraceProcessor.get_clickhouse_transaction_table_name(),
        ),
    ]
)

TRANSACTION_PROCESSING_TEST_CASES = chain.from_iterable(
    [
        SHARED_TRACE_TRANSACTION_PROCESSING_TEST_CASES,
        CROSS_TRACE_TYPE_PROCESSING_TEST_CASES,
    ]
)


async def _create_tenant(
    tenant_id: uuid.UUID,
    org_id: uuid.UUID,
    metronome_customer_id: uuid.UUID | None,
    config: TenantConfig,
) -> schemas.Tenant:
    user_id = uuid.uuid4()
    async with asyncpg_conn() as db:
        user = await ensure_user(
            DecodedUserInfo(
                sub="fake", id=str(user_id), email=f"test+{user_id}@langchain.dev"
            )
        )
        await db.execute(
            "INSERT INTO organizations (id, display_name, metronome_customer_id, created_by_user_id) VALUES ($1, $2, $3, $4)",
            org_id,
            user_id.hex,
            str(metronome_customer_id) if metronome_customer_id else None,
            user_id,
        )
        await db.fetchrow(
            "INSERT INTO identities (id, tenant_id, organization_id, user_id, access_scope, parent_identity_id, ls_user_id) VALUES ($1, $2, $3, $4, $5, $6, $7) returning *",
            uuid.uuid4(),
            None,
            org_id,
            user_id,
            schemas.AccessScope.organization,
            None,
            user["ls_user_id"],
        )
    return await crud.create_tenant(
        TenantlessAuthInfo(
            available_tenants=[], user_id=user_id, ls_user_id=user["ls_user_id"]
        ),
        schemas.TenantCreatePrivileged(
            id=tenant_id,
            organization_id=org_id,
            display_name=user_id.hex,
            config=config,
        ),
    )


async def _create_self_hosted_customer(
    customer_id: uuid.UUID,
    license_id: uuid.UUID,
    metronome_customer_id: uuid.UUID | None,
) -> None:
    async with asyncpg_conn() as db:
        await db.execute(
            "INSERT INTO self_hosted_customers (id, customer_name, metronome_customer_id) VALUES ($1, $2, $3) ON CONFLICT DO NOTHING RETURNING *",
            customer_id,
            "fake_customer_name",
            metronome_customer_id,
        )

        await db.execute(
            "INSERT INTO self_hosted_licenses (id, hashed_license_key, self_hosted_customer_id, license_type, created_at, updated_at, expires_at) VALUES ($1, $2, $3, $4, $5, $6, $7) ON CONFLICT DO NOTHING",
            license_id,
            "fake_license_key",
            customer_id,
            "langsmith",
            datetime.datetime.now(),
            datetime.datetime.now(),
            datetime.datetime.now(),
        )


async def _execute_with_db(stmt: str) -> None:
    async with asyncpg_conn() as db:
        await db.execute(stmt)


@pytest.fixture(autouse=True)
async def setup():
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        await asyncio.gather(
            ch.execute("delete-setup", "DELETE FROM billable_traces WHERE 1=1"),
            ch.execute(
                "delete-setup", "DELETE FROM billable_longlived_traces WHERE 1=1"
            ),
            ch.execute("finalize-setup", "SELECT * FROM billable_traces FINAL"),
            ch.execute(
                "finalize-setup", "SELECT * FROM billable_longlived_traces FINAL"
            ),
        )

    await _execute_with_db("DELETE FROM trace_count_transactions")
    await _execute_with_db("DELETE FROM seat_change_billing_events")
    await _execute_with_db("DELETE FROM organizations")
    await _execute_with_db("DELETE FROM self_hosted_licenses")

    await asyncio.gather(
        _create_tenant(
            TENANT_ONE_ID,
            ORG_ONE_ID,
            METRONOME_CUSTOMER_ONE_ID,
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        ),
        _create_tenant(
            TENANT_TWO_ID,
            ORG_TWO_ID,
            METRONOME_CUSTOMER_TWO_ID,
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        ),
        _create_tenant(
            METRONOME_CUSTOMERLESS_TENANT_ID,
            METRONOME_CUSTOMERLESS_ORG_ID,
            None,
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        ),
        _create_self_hosted_customer(
            SELF_HOSTED_CUSTOMER_ID,
            SELF_HOSTED_LICENSE_ID,
            SELF_HOSTED_METRONOME_CUSTOMER_ID,
        ),
    )

    yield


async def _insert_transactions(
    transactions: list[traces.TraceTransactionWithOrgModel], should_replace=False
):
    # SETUP TEST DATA IN POSTGRES
    async with asyncpg_conn() as db, db.transaction():
        if should_replace:
            delete_query = "DELETE FROM trace_count_transactions WHERE id IN ({})"
            placeholders = ",".join(
                ["$" + str(i) for i in range(1, len(transactions) + 1)]
            )
            formatted_delete_query = delete_query.format(placeholders)
            await db.execute(formatted_delete_query, *[txn.id for txn in transactions])

        query = """
            INSERT INTO trace_count_transactions (
                id,
                tenant_id,
                session_id,
                trace_count,
                insertion_time_range,
                interval,
                status,
                num_failed_send_attempts,
                transaction_type,
                source,
                organization_id,
                self_hosted_license_id
            )
            VALUES (
                $1,
                $2,
                $3,
                $4,
                -- mirrors time range code in transactions.py
                tstzrange($5, $6, '[]'),
                tstzrange($7, $8, '(]'),
                $9,
                $10,
                $11,
                $12,
                $13,
                (select id from self_hosted_licenses where self_hosted_customer_id = $14)
            )
        """
        await db.executemany(
            query,
            [
                (
                    txn.id,
                    txn.tenant_id,
                    txn.session_id,
                    txn.trace_count,
                    txn.start_insertion_time,
                    txn.end_insertion_time,
                    txn.start_interval_time,
                    txn.end_interval_time,
                    txn.status,
                    txn.num_failed_send_attempts,
                    txn.transaction_type,
                    txn.source.value,
                    txn.organization_id,
                    txn.self_hosted_customer_id,
                )
                for txn in transactions
            ],
        )


async def _list_all_transactions():
    async with asyncpg_conn() as db:
        result = await db.fetch(
            """
            SELECT
                tc.id,
                tc.tenant_id,
                tc.session_id,
                tc.trace_count,
                lower(tc.insertion_time_range) as start_insertion_time,
                upper(tc.insertion_time_range) as end_insertion_time,
                lower(tc.interval) as start_interval_time,
                upper(tc.interval) as end_interval_time,
                tc.status,
                tc.num_failed_send_attempts,
                tc.transaction_type,
                tc.source,
                tc.organization_id,
                sl.self_hosted_customer_id as self_hosted_customer_id
            FROM trace_count_transactions tc
            LEFT JOIN self_hosted_licenses sl ON tc.self_hosted_license_id = sl.id
        """
        )
        return [traces.TraceTransactionWithOrg(**row) for row in result]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in TRANSACTION_PROCESSING_TEST_CASES],
    ids=lambda x: f"{x.trace_processor.__name__}: {x.name}",
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_transaction_processing(test_case: TraceTransactionProcessingTestCase):
    # SETUP TEST DATA IN CLICKHOUSE
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        # Insert existing traces
        for trace in test_case.existing_traces:
            table_name = trace.table_name
            await ch.execute(
                f"setup-fake-{table_name}-table",
                f"INSERT INTO {table_name} (trace_id, session_id, start_time, tenant_id, inserted_at) SETTINGS wait_for_async_insert=1 VALUES",
                (
                    str(trace.trace_id),
                    str(trace.session_id),
                    trace.start_time.strftime(CH_INSERT_TIME),
                    str(trace.tenant_id),
                    trace.inserted_at.strftime(CH_INSERT_TIME),
                ),
            )

    # SETUP TEST DATA IN POSTGRES
    await _insert_transactions(test_case.existing_transactions)

    # RUN THE TEST
    with freeze_time(test_case.current_time):
        await test_case.trace_processor.process_trace_transactions()

    # VERIFY THE RESULTS
    async with asyncpg_conn() as db:
        result = await db.fetch(
            f"""
            SELECT
                id,
                tenant_id,
                session_id,
                trace_count,
                lower(insertion_time_range) as start_insertion_time,
                upper(insertion_time_range) as end_insertion_time,
                lower(interval) as start_interval_time,
                upper(interval) as end_interval_time,
                status,
                transaction_type,
                source,
                organization_id
            FROM trace_count_transactions
            WHERE transaction_type = '{test_case.trace_processor.get_transaction_type()}'
        """
        )

        NOT_VALIDATED_UUID = uuid.UUID("00000000-0000-0000-0000-000000000000")

        formatted_result = [
            traces.TraceTransactionWithOrg(
                id=NOT_VALIDATED_UUID,
                tenant_id=row["tenant_id"],
                session_id=row["session_id"],
                trace_count=row["trace_count"],
                start_insertion_time=row["start_insertion_time"],
                end_insertion_time=row["end_insertion_time"],
                start_interval_time=row["start_interval_time"],
                end_interval_time=row["end_interval_time"],
                status=row["status"],
                num_failed_send_attempts=0,
                transaction_type=row["transaction_type"],
                source=traces.TraceTransactionSource(row["source"]),
                organization_id=row["organization_id"],
            )
            for row in result
        ]

        def copy_with_default_uuid(t: traces.TraceTransaction):
            copied = t.copy()
            copied.id = NOT_VALIDATED_UUID
            return copied

        assert sorted(formatted_result, key=lambda x: str(x)) == sorted(
            [copy_with_default_uuid(t) for t in test_case.expected_transactions],
            key=lambda x: str(x),
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_interval_uniqueness_constraints():
    overlapping_transactions = [
        traces.TraceTransactionWithOrg(
            id=uuid.uuid4(),
            tenant_id=TENANT_ONE_ID,
            session_id=SESSION_ONE_ID,
            trace_count=1,
            start_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
            end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
            start_interval_time=FIRST_INTERVAL_START,
            end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
            status=UsageReportingStatus.PENDING.value,
            transaction_type=traces.AllTracesTraceProcessor.get_transaction_type(),
            organization_id=ORG_ONE_ID,
        ),
        traces.TraceTransactionWithOrg(
            id=uuid.uuid4(),
            tenant_id=TENANT_ONE_ID,
            session_id=SESSION_ONE_ID,
            trace_count=1,
            start_insertion_time=FIRST_INTERVAL_START
            + datetime.timedelta(minutes=3, seconds=59),
            end_insertion_time=FIRST_INTERVAL_START
            + datetime.timedelta(minutes=4, seconds=1),
            start_interval_time=FIRST_INTERVAL_START,
            end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
            status=UsageReportingStatus.PENDING.value,
            transaction_type=traces.AllTracesTraceProcessor.get_transaction_type(),
            organization_id=ORG_ONE_ID,
        ),
    ]

    with pytest.raises(ExclusionViolationError):
        await _insert_transactions(overlapping_transactions)


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_interval_uniqueness_constraints_not_across_txn_types():
    overlapping_transactions = [
        traces.TraceTransactionWithOrg(
            id=uuid.uuid4(),
            tenant_id=TENANT_ONE_ID,
            session_id=SESSION_ONE_ID,
            trace_count=1,
            start_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=3),
            end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
            start_interval_time=FIRST_INTERVAL_START,
            end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
            status=UsageReportingStatus.PENDING.value,
            transaction_type=traces.AllTracesTraceProcessor.get_transaction_type(),
            organization_id=ORG_ONE_ID,
        ),
        traces.TraceTransactionWithOrg(
            id=uuid.uuid4(),
            tenant_id=TENANT_ONE_ID,
            session_id=SESSION_ONE_ID,
            trace_count=1,
            start_insertion_time=FIRST_INTERVAL_START
            + datetime.timedelta(minutes=3, seconds=59),
            end_insertion_time=FIRST_INTERVAL_START
            + datetime.timedelta(minutes=4, seconds=1),
            start_interval_time=FIRST_INTERVAL_START,
            end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
            status=UsageReportingStatus.PENDING.value,
            transaction_type=traces.LonglivedTracesTraceProcessor.get_transaction_type(),
            organization_id=ORG_ONE_ID,
        ),
    ]

    await _insert_transactions(overlapping_transactions)


class MockMetronomeClient(metronome.JsonHttpClient):
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    def __init__(self, orgs_to_throw_500_for: list[str] = []):
        self.ingested_data: list[dict] = []
        self.orgs_to_throw_500_for = orgs_to_throw_500_for

    async def post(self, path: str, data: Any | None = None) -> dict:
        if path == "https://api.metronome.com/v1/ingest":
            for event in cast(list, data):
                if event["customer_id"] in self.orgs_to_throw_500_for:
                    raise HTTPStatusError(
                        "bad req",
                        request=MagicMock(),
                        response=Response(status_code=500),
                    )

            self.ingested_data += cast(list, data)
            return {}
        else:
            raise NotImplementedError(
                "POST requests are only supported for ingestion on the mock client"
            )

    async def get(self, path: str, params: Any | None = None) -> dict:
        raise NotImplementedError("GET requests are not supported on the mock client")

    async def delete(self, path: str) -> dict:
        raise NotImplementedError(
            "DELETE requests are not supported on the mock client"
        )


class MetronomeTraceReportingTestCase(NamedTuple):
    name: str
    existing_transactions: list[traces.TraceTransactionWithOrg]
    sent_events: list[dict]
    transactions_after_reporting: list[traces.TraceTransactionWithOrg]
    metronome_client: Callable[..., metronome.JsonHttpClient] = MockMetronomeClient


METRONOME_REPORTING_TEST_CASES = [
    MetronomeTraceReportingTestCase(
        name="test that pending transactions are reported to metronome",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="test that pending transactions are reported to metronome for longlived traces",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_longlived_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="do not sent transactions with should_not_report status",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_NOT_REPORT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_NOT_REPORT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="do not double send sent transactions with sent status",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="Only send transactions with pending status even if others exist",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "8437744d-9dc3-420a-b3e8-52489ced17fc",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": (
                    FIRST_INTERVAL_START + datetime.timedelta(minutes=5)
                ).isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 456,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="Send multiple transactions across batches",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            },
            {
                "transaction_id": "8437744d-9dc3-420a-b3e8-52489ced17fc",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": (
                    FIRST_INTERVAL_START + datetime.timedelta(minutes=5)
                ).isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 456,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            },
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="Send multiple transactions across types",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            },
            {
                "transaction_id": "8437744d-9dc3-420a-b3e8-52489ced17fc",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_longlived_traces",
                "properties": {
                    "count": 456,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            },
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="Only marks bad batches of transactions to be retried on an error to metronome",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_TWO_ID,
                session_id=SESSION_TWO_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_TWO_ID,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "8437744d-9dc3-420a-b3e8-52489ced17fc",
                "customer_id": str(ORG_TWO_ID),
                "timestamp": (
                    FIRST_INTERVAL_START + datetime.timedelta(minutes=5)
                ).isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 456,
                    "project_id": str(SESSION_TWO_ID),
                    "tenant_id": str(TENANT_TWO_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_TWO_ID,
                session_id=SESSION_TWO_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_TWO_ID,
            ),
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
    MetronomeTraceReportingTestCase(
        name="report usage to metronome even if a tenant doesn't have an associated metronome customer",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(METRONOME_CUSTOMERLESS_ORG_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(METRONOME_CUSTOMERLESS_TENANT_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="send self hosted usage to metronome",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=SELF_HOSTED_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=SELF_HOSTED_ORG_ID,
                source=traces.TraceTransactionSource.remote_self_hosted,
                self_hosted_customer_id=SELF_HOSTED_CUSTOMER_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(SELF_HOSTED_CUSTOMER_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(SELF_HOSTED_TENANT_ID),
                    "organization_id": str(SELF_HOSTED_ORG_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=SELF_HOSTED_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=SELF_HOSTED_ORG_ID,
                source=traces.TraceTransactionSource.remote_self_hosted,
                self_hosted_customer_id=SELF_HOSTED_CUSTOMER_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="send self both regular and self hosted usage to metronome in same job",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414c"),
                tenant_id=SELF_HOSTED_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=SELF_HOSTED_ORG_ID,
                source=traces.TraceTransactionSource.remote_self_hosted,
                self_hosted_customer_id=SELF_HOSTED_CUSTOMER_ID,
            ),
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(METRONOME_CUSTOMERLESS_ORG_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(METRONOME_CUSTOMERLESS_TENANT_ID),
                },
            },
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414c",
                "customer_id": str(SELF_HOSTED_CUSTOMER_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(SELF_HOSTED_TENANT_ID),
                    "organization_id": str(SELF_HOSTED_ORG_ID),
                },
            },
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414c"),
                tenant_id=SELF_HOSTED_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=SELF_HOSTED_ORG_ID,
                source=traces.TraceTransactionSource.remote_self_hosted,
                self_hosted_customer_id=SELF_HOSTED_CUSTOMER_ID,
            ),
        ],
    ),
]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in METRONOME_REPORTING_TEST_CASES],
    ids=lambda x: x.name,
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_metronome_reporting(test_case: MetronomeTraceReportingTestCase):
    await _insert_transactions(test_case.existing_transactions)

    with patch(
        "app.models.billing.usagereporting.transactions.metronome.metronome_client"
    ) as metronome_client_ctx_mgr_mock:
        mock_metronome_client = test_case.metronome_client()
        metronome_client_ctx_mgr_mock.return_value.__aenter__.return_value = (
            mock_metronome_client
        )
        await traces.TraceTransactionReporter.report_usage()

    assert sorted(
        mock_metronome_client.ingested_data, key=lambda x: str(orjson.dumps(x))
    ) == sorted(test_case.sent_events, key=lambda x: str(orjson.dumps(x)))

    updated_transactions = await _list_all_transactions()
    assert sorted(updated_transactions, key=lambda x: str(x)) == sorted(
        test_case.transactions_after_reporting, key=lambda x: str(x)
    )


class MetronomeSeatReportingTestCase(NamedTuple):
    name: str
    existing_seat_operations: list[SeatChangeBillingEvent]
    sent_events: list[dict]
    operations_after_reporting: list[SeatChangeBillingEvent]
    metronome_client: Callable[..., metronome.JsonHttpClient] = MockMetronomeClient


SEAT_TRANSACTION_ID_ONE = uuid.uuid4()
TRANSACTION_ONE_TIME = datetime.datetime(2022, 2, 22, 0, 0, 0, tzinfo=TZ_UTC)

DEFAULT_ORG_ADMIN_SEAT_EVENT = SeatChangeBillingEvent(
    id=uuid.uuid4(),
    transaction_id=SEAT_TRANSACTION_ID_ONE,
    event_timestamp=TRANSACTION_ONE_TIME,
    organization_id=ORG_ONE_ID,
    seats_before=1,
    seats_after=1,
    pending_seats_before=0,
    pending_seats_after=1,
    seat_type="ORGANIZATION_ADMIN",
    operation=SeatChangeOperation.INVITE,
    previous_transaction_id=None,
    reporting_status_for_next_invoice=UsageReportingStatus.TODO,
    next_invoice_num_failed_send_attempts=0,
    last_next_invoice_send_attempt=None,
    reporting_status_for_current_invoice=UsageReportingStatus.TODO,
    current_invoice_num_failed_send_attempts=0,
    last_current_invoice_send_attempt=None,
)

DEFAULT_ORGANIZATION_USER_SEAT_EVENT = DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
    update={
        "id": uuid.uuid4(),
        "seat_type": "ORGANIZATION_USER",
        "seats_before": 0,
        "seats_after": 0,
        "pending_seats_before": 0,
        "pending_seats_after": 0,
        # The different events in the same transaction have a slight variation in time
        "event_timestamp": TRANSACTION_ONE_TIME + datetime.timedelta(milliseconds=10),
    }
)

METRONOME_SEAT_REPORTING_TEST_CASES = [
    MetronomeSeatReportingTestCase(
        name="test that TODO seat operations are reported to metronome",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT,
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT,
        ],
        sent_events=[
            {
                "transaction_id": str(SEAT_TRANSACTION_ID_ONE),
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 2,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="test that only one seat event is handled seat operations are reported to metronome",
        existing_seat_operations=[DEFAULT_ORG_ADMIN_SEAT_EVENT],
        sent_events=[
            {
                "transaction_id": str(SEAT_TRANSACTION_ID_ONE),
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 2,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="do not double send events",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
        ],
        sent_events=[],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="send events across orgs",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT,
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT,
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "organization_id": ORG_TWO_ID,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "organization_id": ORG_TWO_ID,
                }
            ),
        ],
        sent_events=[
            {
                "transaction_id": str(SEAT_TRANSACTION_ID_ONE),
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 2,
                },
            },
            {
                "transaction_id": "efa5c31d-986d-4f7d-94a4-80ec24b1acb8",
                "customer_id": str(ORG_TWO_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 2,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "organization_id": ORG_TWO_ID,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "organization_id": ORG_TWO_ID,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="send multiple txns in same org",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT,
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT,
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "pending_seats_before": 1,
                    "pending_seats_after": 2,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                }
            ),
        ],
        sent_events=[
            {
                "transaction_id": str(SEAT_TRANSACTION_ID_ONE),
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 2,
                },
            },
            {
                "transaction_id": "efa5c31d-986d-4f7d-94a4-80ec24b1acb8",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    "total_seats": 3,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={"reporting_status_for_next_invoice": UsageReportingStatus.SENT}
            ),
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "pending_seats_before": 1,
                    "pending_seats_after": 2,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="aggregate read only and admin seat events",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(update={"pending_seats_before": 1}),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "seats_before": 1,
                    "seats_after": 1,
                    "pending_seats_after": 1,
                }
            ),
        ],
        sent_events=[
            {
                "transaction_id": str(SEAT_TRANSACTION_ID_ONE),
                "customer_id": str(ORG_ONE_ID),
                "timestamp": TRANSACTION_ONE_TIME.isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    # one pending and one existing admin seat +
                    # one pending and one existing read only seat
                    "total_seats": 4,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "pending_seats_before": 1,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "seats_before": 1,
                    "seats_after": 1,
                    "pending_seats_after": 1,
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
        ],
    ),
    MetronomeSeatReportingTestCase(
        name="only send the latest transaction for the same org within a second",
        existing_seat_operations=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT,
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT,
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "pending_seats_before": 1,
                    "pending_seats_after": 2,
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "event_timestamp": TRANSACTION_ONE_TIME
                    + datetime.timedelta(milliseconds=200),
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "event_timestamp": TRANSACTION_ONE_TIME
                    + datetime.timedelta(milliseconds=210),
                }
            ),
        ],
        sent_events=[
            {
                "transaction_id": "efa5c31d-986d-4f7d-94a4-80ec24b1acb8",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": (
                    TRANSACTION_ONE_TIME + datetime.timedelta(milliseconds=200)
                ).isoformat(),
                "event_type": "langsmith_seats",
                "properties": {
                    # sends just the seats from the more recent event
                    # in the same second
                    "total_seats": 3,
                },
            },
        ],
        operations_after_reporting=[
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "reporting_status_for_next_invoice": UsageReportingStatus.SKIPPED,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "reporting_status_for_next_invoice": UsageReportingStatus.SKIPPED,
                }
            ),
            DEFAULT_ORG_ADMIN_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("393f5c77-f018-4369-af25-311322a4cb0f"),
                    "pending_seats_before": 1,
                    "pending_seats_after": 2,
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "event_timestamp": TRANSACTION_ONE_TIME
                    + datetime.timedelta(milliseconds=200),
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
            DEFAULT_ORGANIZATION_USER_SEAT_EVENT.copy(
                update={
                    "id": uuid.UUID("5edd4a5f-fa67-4a28-b668-7e22c8ceb9f6"),
                    "transaction_id": uuid.UUID("efa5c31d-986d-4f7d-94a4-80ec24b1acb8"),
                    "previous_transaction_id": SEAT_TRANSACTION_ID_ONE,
                    "event_timestamp": TRANSACTION_ONE_TIME
                    + datetime.timedelta(milliseconds=210),
                    "reporting_status_for_next_invoice": UsageReportingStatus.SENT,
                }
            ),
        ],
    ),
]


async def _insert_seat_change_events(
    events: list[SeatChangeBillingEvent], should_replace=False
):
    async with asyncpg_conn() as db, db.transaction():
        if should_replace:
            delete_query = "DELETE FROM seat_change_billing_events WHERE id IN ({})"
            placeholders = ",".join(["$" + str(i) for i in range(1, len(events) + 1)])
            formatted_delete_query = delete_query.format(placeholders)
            await db.execute(formatted_delete_query, *[event.id for event in events])

        query = """
            INSERT INTO seat_change_billing_events
            (id, transaction_id, event_timestamp, organization_id, seats_before, seats_after, pending_seats_before, pending_seats_after, seat_type, operation, previous_transaction_id, reporting_status_for_next_invoice, next_invoice_num_failed_send_attempts, last_next_invoice_send_attempt, reporting_status_for_current_invoice, current_invoice_num_failed_send_attempts, last_current_invoice_send_attempt)
            VALUES (
                $1,
                $2,
                $3,
                $4,
                $5,
                $6,
                $7,
                $8,
                $9,
                $10,
                $11,
                $12,
                $13,
                $14,
                $15,
                $16,
                $17
            )
        """
        await db.executemany(
            query,
            [
                (
                    event.id,
                    event.transaction_id,
                    event.event_timestamp,
                    event.organization_id,
                    event.seats_before,
                    event.seats_after,
                    event.pending_seats_before,
                    event.pending_seats_after,
                    event.seat_type,
                    event.operation.value,
                    event.previous_transaction_id,
                    event.reporting_status_for_next_invoice.value,
                    event.next_invoice_num_failed_send_attempts,
                    event.last_next_invoice_send_attempt,
                    event.reporting_status_for_current_invoice.value,
                    event.current_invoice_num_failed_send_attempts,
                    event.last_current_invoice_send_attempt,
                )
                for event in events
            ],
        )


async def _list_seat_events():
    async with asyncpg_conn() as db:
        result = await db.fetch(
            """
            SELECT *
            FROM seat_change_billing_events
        """
        )
        return [SeatChangeBillingEvent(**row) for row in result]


async def _list_seat_events_for_org(organization_id: uuid.UUID | None):
    assert organization_id is not None
    async with asyncpg_conn() as db:
        result = await db.fetch(
            """
            SELECT *
            FROM seat_change_billing_events
            WHERE organization_id = $1
            ORDER BY event_timestamp
        """,
            organization_id,
        )
        return [SeatChangeBillingEvent(**row) for row in result]


async def _delete_seat_events_for_org(organization_id: uuid.UUID | None):
    assert organization_id is not None
    async with asyncpg_conn() as db:
        result = await db.fetch(
            """
            DELETE FROM seat_change_billing_events
            WHERE organization_id = $1
        """,
            organization_id,
        )
        return [SeatChangeBillingEvent(**row) for row in result]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in METRONOME_SEAT_REPORTING_TEST_CASES],
    ids=lambda x: x.name,
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_metronome_seat_reporting(test_case: MetronomeSeatReportingTestCase):
    await _insert_seat_change_events(test_case.existing_seat_operations)

    with patch(
        "app.models.billing.usagereporting.transactions.metronome.metronome_client"
    ) as metronome_client_ctx_mgr_mock:
        mock_metronome_client = test_case.metronome_client()
        metronome_client_ctx_mgr_mock.return_value.__aenter__.return_value = (
            mock_metronome_client
        )
        await seats.SeatTransactionReporter.report_usage()

    assert sorted(mock_metronome_client.ingested_data, key=lambda x: str(x)) == sorted(
        test_case.sent_events, key=lambda x: str(x)
    )

    updated_events = await _list_seat_events()
    assert sorted(updated_events, key=lambda x: str(x)) == sorted(
        test_case.operations_after_reporting, key=lambda x: str(x)
    )


FAILURE_RECOVERY_TEST_CASES = [
    MetronomeTraceReportingTestCase(
        name="retry reporting failed transactions",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=1,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="retry reporting failed transactions for longlived traces",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_longlived_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=1,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    MetronomeTraceReportingTestCase(
        name="set transaction as failed if it retries too many times",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=9,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.FAILED.value,
                num_failed_send_attempts=10,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
    MetronomeTraceReportingTestCase(
        name="keep transaction as should_retry and up retry attempts if below max retry threshold",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=8,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=9,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in FAILURE_RECOVERY_TEST_CASES],
    ids=lambda x: x.name,
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_retrying_failed_transactions(test_case: MetronomeTraceReportingTestCase):
    await _insert_transactions(test_case.existing_transactions)

    with patch(
        "app.models.billing.usagereporting.transactions.metronome.metronome_client"
    ) as metronome_client_ctx_mgr_mock:
        mock_metronome_client = test_case.metronome_client()
        metronome_client_ctx_mgr_mock.return_value.__aenter__.return_value = (
            mock_metronome_client
        )
        await traces.TraceTransactionReporter.retry_report_failed_transactions()

    assert mock_metronome_client.ingested_data == test_case.sent_events

    updated_transactions = await _list_all_transactions()
    assert sorted(updated_transactions, key=lambda x: str(x)) == sorted(
        test_case.transactions_after_reporting, key=lambda x: str(x)
    )


class ConcurrentMetronomeTraceReportingTestCase(NamedTuple):
    name: str
    existing_transactions: list[traces.TraceTransactionWithOrg]
    transactions_state_updated_midway: list[traces.TraceTransactionWithOrg]
    sent_events: list[dict]
    transactions_after_reporting: list[traces.TraceTransactionWithOrg]
    metronome_client: Callable[..., metronome.JsonHttpClient] = MockMetronomeClient


CONCURRENT_METRONOME_REPORTING_TEST_CASES = [
    ConcurrentMetronomeTraceReportingTestCase(
        name="do not mark a transaction as should_retry on failure if it is already sent by a concurrent process",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_state_updated_midway=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=1,
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=1,
                organization_id=ORG_ONE_ID,
            )
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
    ConcurrentMetronomeTraceReportingTestCase(
        name="mark a transaction as sent even if it was marked as failed by a concurrent process",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=9,
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_state_updated_midway=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.FAILED.value,
                num_failed_send_attempts=10,
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[
            {
                "transaction_id": "4d23deab-19b4-4550-a556-6bc80451414b",
                "customer_id": str(ORG_ONE_ID),
                "timestamp": FIRST_INTERVAL_START.isoformat(),
                "event_type": "langsmith_traces",
                "properties": {
                    "count": 123,
                    "project_id": str(SESSION_ONE_ID),
                    "tenant_id": str(TENANT_ONE_ID),
                },
            }
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                # This is 10 because it was incremented by the concurrent process
                num_failed_send_attempts=10,
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    ConcurrentMetronomeTraceReportingTestCase(
        name="do not mark a transaction as failed if it was sent by a concurrent process",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=9,
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_state_updated_midway=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=9,
                organization_id=ORG_ONE_ID,
            )
        ],
        sent_events=[],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                num_failed_send_attempts=9,
                organization_id=ORG_ONE_ID,
            )
        ],
        metronome_client=lambda: MockMetronomeClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in CONCURRENT_METRONOME_REPORTING_TEST_CASES],
    ids=lambda x: x.name,
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_concurrent_retry_updates(
    test_case: ConcurrentMetronomeTraceReportingTestCase,
):
    await _insert_transactions(test_case.existing_transactions)

    with patch(
        "app.models.billing.usagereporting.transactions.metronome.metronome_client"
    ) as metronome_client_ctx_mgr_mock:
        mock_metronome_client = test_case.metronome_client()

        async def side_effect():
            await _insert_transactions(
                test_case.transactions_state_updated_midway, should_replace=True
            )
            return mock_metronome_client

        # This is such a hack, but change the state of the transaction once the metronome client
        # is entered to simulate a concurrent process updating the state of the transaction
        #
        # This happens after the transactions are read from the DB and before they are reported to
        # metronome so it should be a safe way to simulate a concurrent process updating the state
        metronome_client_ctx_mgr_mock.return_value.__aenter__.side_effect = side_effect

        await traces.TraceTransactionReporter.retry_report_failed_transactions()

    assert mock_metronome_client.ingested_data == test_case.sent_events

    updated_transactions = await _list_all_transactions()
    assert sorted(updated_transactions, key=lambda x: str(x)) == sorted(
        test_case.transactions_after_reporting, key=lambda x: str(x)
    )


class MockBeaconClient(httpx.AsyncClient):
    def __init__(self, orgs_to_throw_500_for: list[str] = []):
        self.ingested_data: list[dict] = []
        self.orgs_to_throw_500_for = orgs_to_throw_500_for

    async def post(
        self,
        url: URLTypes,
        *,
        content: RequestContent | None = None,
        data: RequestData | None = None,
        files: RequestFiles | None = None,
        json: typing.Any | None = None,
        params: QueryParamTypes | None = None,
        headers: HeaderTypes | None = None,
        cookies: CookieTypes | None = None,
        auth: AuthTypes | UseClientDefault = USE_CLIENT_DEFAULT,
        follow_redirects: bool | UseClientDefault = USE_CLIENT_DEFAULT,
        timeout: TimeoutTypes | UseClientDefault = USE_CLIENT_DEFAULT,
        extensions: RequestExtensions | None = None,
    ) -> Response:
        if url == "v1/beacon/ingest-traces":
            if (
                not json
                or not isinstance(json, dict)
                or "trace_transactions" not in json
            ):
                serialized_events = []
            else:
                serialized_events = cast(list, json["trace_transactions"])
            for event in serialized_events:
                if event["organization_id"] in self.orgs_to_throw_500_for:
                    return Response(
                        status_code=500, request=httpx.Request(method="POST", url=url)
                    )

            self.ingested_data += serialized_events
            return Response(
                status_code=200, request=httpx.Request(method="POST", url=url)
            )
        else:
            raise NotImplementedError(
                "POST requests are only supported for ingestion on the mock client"
            )


class BeaconTraceReportingTestCase(NamedTuple):
    name: str
    existing_transactions: list[traces.TraceTransactionWithOrg]
    transactions_after_reporting: list[traces.TraceTransactionWithOrg]
    mock_beacon_client: Callable[..., httpx.AsyncClient] = MockBeaconClient


BEACON_REPORTING_TEST_CASES = [
    BeaconTraceReportingTestCase(
        name="test that pending transactions are reported to beacon",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    BeaconTraceReportingTestCase(
        name="test that pending transactions are reported to beacon for longlived traces",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    BeaconTraceReportingTestCase(
        name="do not sent transactions with should_not_report status",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_NOT_REPORT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_NOT_REPORT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    BeaconTraceReportingTestCase(
        name="do not double send sent transactions with sent status",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            )
        ],
    ),
    BeaconTraceReportingTestCase(
        name="Only send transactions with pending status even if others exist",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    BeaconTraceReportingTestCase(
        name="Send multiple transactions across batches",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    BeaconTraceReportingTestCase(
        name="Send multiple transactions across types",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="longlived_traces",
                organization_id=ORG_ONE_ID,
            ),
        ],
    ),
    BeaconTraceReportingTestCase(
        name="Only marks bad batches of transactions to be retried on an error to beacon",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_TWO_ID,
                session_id=SESSION_TWO_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=ORG_TWO_ID,
            ),
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=TENANT_ONE_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SHOULD_RETRY.value,
                num_failed_send_attempts=1,
                transaction_type="all_traces",
                organization_id=ORG_ONE_ID,
            ),
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("8437744d-9dc3-420a-b3e8-52489ced17fc"),
                tenant_id=TENANT_TWO_ID,
                session_id=SESSION_TWO_ID,
                trace_count=456,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=8),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=9),
                start_interval_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=5),
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=10),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=ORG_TWO_ID,
            ),
        ],
        mock_beacon_client=lambda: MockBeaconClient(
            orgs_to_throw_500_for=[str(ORG_ONE_ID)]
        ),
    ),
    BeaconTraceReportingTestCase(
        name="report usage to beacon even if a tenant doesn't have an associated metronome customer",
        existing_transactions=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.PENDING.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            )
        ],
        transactions_after_reporting=[
            traces.TraceTransactionWithOrg(
                id=uuid.UUID("4d23deab-19b4-4550-a556-6bc80451414b"),
                tenant_id=METRONOME_CUSTOMERLESS_TENANT_ID,
                session_id=SESSION_ONE_ID,
                trace_count=123,
                start_insertion_time=FIRST_INTERVAL_START
                + datetime.timedelta(minutes=3),
                end_insertion_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=4),
                start_interval_time=FIRST_INTERVAL_START,
                end_interval_time=FIRST_INTERVAL_START + datetime.timedelta(minutes=5),
                status=UsageReportingStatus.SENT.value,
                transaction_type="all_traces",
                organization_id=METRONOME_CUSTOMERLESS_ORG_ID,
            )
        ],
    ),
]


@pytest.mark.parametrize(
    ["test_case"],
    [(test_case,) for test_case in BEACON_REPORTING_TEST_CASES],
    ids=lambda x: x.name,
)
@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_beacon_reporting(test_case: BeaconTraceReportingTestCase):
    await _insert_transactions(test_case.existing_transactions)
    mock_beacon_client = test_case.mock_beacon_client()

    @asynccontextmanager
    async def get_service_client_patched(
        service_name: ServiceName,
        headers: typing.Optional[typing.Dict[str, str]] = None,
    ):
        yield mock_beacon_client

    with patch(
        "app.models.billing.usagereporting.transactions.get_service_client",
        get_service_client_patched,
    ):
        await traces.SelfHostedTraceTransactionReporter.report_usage()

    sent_txns = [
        txn.to_json()
        for txn in test_case.existing_transactions
        if txn.status == UsageReportingStatus.PENDING.value
        and str(txn.organization_id) not in mock_beacon_client.orgs_to_throw_500_for
    ]
    assert sorted(
        mock_beacon_client.ingested_data, key=lambda x: str(orjson.dumps(x))
    ) == sorted(sent_txns, key=lambda x: str(orjson.dumps(x)))

    updated_transactions = await _list_all_transactions()
    assert sorted(updated_transactions, key=lambda x: str(x)) == sorted(
        test_case.transactions_after_reporting, key=lambda x: str(x)
    )
