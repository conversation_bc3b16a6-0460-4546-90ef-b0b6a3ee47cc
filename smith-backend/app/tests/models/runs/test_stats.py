import re

import pytest
from syrupy import SnapshotAssertion

from app import schemas
from app.api.auth import AuthInfo
from app.models.runs.stats import (
    _feedback_stats_query,
    _fetch_kwargs,
    _io_kv_stats_query,
    _metadata_stats_query,
    _runs_stats_for_projection_query,
    _runs_stats_latency_avg_query,
    _token_count_stats_query,
)

_common = {
    "session": ["1266d63f-3c48-47e8-bf66-799a6936d71c"],
    "start_time": "2023-05-05T05:13:24.571809",
    "end_time": "2023-05-15T05:13:32.022361",
}
IS_ROOT = {
    "is_root": True,
}

IS_ROOT_RUN_TYPE_CHAIN = {
    "is_root": True,
    "run_type": "chain",  # add this so we test the join
}

GROUP_BY_METADATA = {
    "group_by": {"attribute": "metadata", "path": "mkey"},
}

RUN_TYPE_LLM_GROUP_BY_TAG = {"run_type": "llm", "group_by": {"attribute": "tag"}}

IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_METADATA = {
    "is_root": True,
    "run_type": "chain",  # add this so we test the join
    "group_by": {"attribute": "metadata", "path": "mkey"},
}

GROUP_BY_NAME = {
    "group_by": {"attribute": "name"},
}

IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_NAME = {
    "is_root": True,
    "run_type": "chain",  # add this so we test the join
    "group_by": {"attribute": "name"},
}

IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_NAME_BUCKETED = {
    "is_root": True,
    "run_type": "chain",  # add this so we test the join
    "group_by": {"attribute": "name"},
    "bucket_info": schemas.CustomChartsRequestBase(
        start_time=_common["start_time"],
        end_time=_common["end_time"],
        stride=schemas.TimedeltaInput(hours=1),
    ),
}

QUERY_PARAMS = [
    ("is_root", IS_ROOT),
    ("is_root_run_type_chain", IS_ROOT_RUN_TYPE_CHAIN),
    ("group_by_metadata", GROUP_BY_METADATA),
    ("run_type_llm_group_by_tag", RUN_TYPE_LLM_GROUP_BY_TAG),
    (
        "is_root_run_type_chain_group_by_metadata",
        IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_METADATA,
    ),
    ("group_by_name", GROUP_BY_NAME),
    ("is_root_run_type_chain_group_by_name", IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_NAME),
    (
        "is_root_run_type_chain_group_by_name_bucketed",
        IS_ROOT_RUN_TYPE_CHAIN_GROUP_BY_NAME_BUCKETED,
    ),
]

STATS_TYPES = [
    "feedback",
    "metadata",
    "input",
    "output",
    "run_stats",
    "run_latency",
    "token_counts",
]


def _standardize_run_type__eq(text):
    pattern = r"\{run_type__eq_[0-9a-zA-Z]{5}\}"
    result = re.sub(pattern, "{run_type__eq_01234}", text)
    return result


async def _get_query(auth: AuthInfo, params: dict, type: str) -> str:
    new_params = {k: v for k, v in params.items() if k not in ("bucket_info",)}
    query_params = schemas.RunStatsQueryParams(**{**_common, **new_params})
    kwargs = await _fetch_kwargs(
        auth, query_params, bucket_info=params.get("bucket_info")
    )
    if type == "feedback":
        query, _ = _feedback_stats_query(**kwargs)
    elif type == "metadata":
        query, _ = _metadata_stats_query(**kwargs)
    elif type == "input":
        query, _ = _io_kv_stats_query(**kwargs, io="input")
    elif type == "output":
        query, _ = _io_kv_stats_query(**kwargs, io="output")
    elif type == "run_stats":
        query, _ = _runs_stats_for_projection_query(
            projection_type="stats_facets", **kwargs
        )
    elif type == "run_latency":
        query, _ = _runs_stats_latency_avg_query(**kwargs)
    elif type == "token_counts":
        query, _ = _token_count_stats_query(**kwargs)
    else:
        raise ValueError(...)
    query = _standardize_run_type__eq(query)
    return query.strip()


@pytest.mark.parametrize("stat_type", STATS_TYPES)
@pytest.mark.parametrize("query_name, query_params", QUERY_PARAMS)
async def test_stats_query(
    auth_tenant_one: AuthInfo,
    snapshot: SnapshotAssertion,
    stat_type: str,
    query_name: str,
    query_params: dict,
) -> None:
    query = await _get_query(auth_tenant_one, query_params, type=stat_type)
    assert query == snapshot, query_name
