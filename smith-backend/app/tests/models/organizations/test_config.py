import uuid
from unittest import mock
from uuid import UUID, uuid4

import asyncpg
import or<PERSON><PERSON>
import pytest
from fastapi import HTT<PERSON>Exception
from fastapi.security import HTTPAuthorizationCredentials
from host.tasks.utils import internal_auth_request
from lc_config import settings
from lc_config.tenant_config import (
    OrganizationConfig,
    TenantConfig,
)
from lc_database.curl import internal_platform_request

from app import config
from app.api.auth.schemas import UnverifiedAuthInfo
from app.api.auth.verify import (
    AuthDict,
    internal_org_auth_request,
    tenant_config_with_nested_org_config,
    verify_auth_info,
    verify_org_auth_info,
)
from app.models.organizations.config import (
    get_org_tenant_config,
)
from app.tests.utils import fresh_tenant_client


async def _set_config(
    db_asyncpg: asyncpg.Connection,
    config: str,
    org_id: UUID,
) -> None:
    await db_asyncpg.execute(
        f"UPDATE organizations SET config = {config} WHERE id = $1",
        org_id,
    )


def _customer_details_with_custom_fields(customer_id: str, custom_fields: dict) -> dict:
    return {
        "data": {
            "id": customer_id,
            "name": "Test",
            "external_id": "b2d9e0d3-7d2f-4e42-8903-d45438c21fed",
            "custom_fields": custom_fields,
            "ingest_aliases": ["b2d9e0d3-7d2f-4e42-8903-d45438c21fed"],
            "customer_config": {"salesforce_account_id": None},
            "current_billable_status": {"value": "billable", "effective_at": None},
        }
    }


def _plan_details_with_custom_fields(plan_id: str, custom_fields: dict) -> dict:
    return {
        "data": {
            "id": plan_id,
            "name": "V3: LangSmith Legacy Plus Plan",
            "minimums": [],
            "description": "Legacy Plan for orgs who joined before pricing was enforced",
            "credit_grants": [],
            "custom_fields": custom_fields,
            "overage_rates": [],
        }
    }


async def test_get_tenant_config_with_nested_org_config():
    # test organization_config is optional
    tenant_config = TenantConfig().model_dump()
    auth_dict = AuthDict(
        organization_id=str(uuid.uuid4()),
        organization_is_personal=False,
        organization_permissions=[],
        tenant_id=str(uuid.uuid4()),
        tenant_config=tenant_config,
        identity_id=str(uuid.uuid4()),
        identity_read_only=False,
        identity_permissions=[],
    )
    result = tenant_config_with_nested_org_config(auth_dict)
    assert result == TenantConfig()

    # test that organization config is nested properly if present
    org_config = OrganizationConfig().model_dump()
    auth_dict["organization_config"] = org_config
    result = tenant_config_with_nested_org_config(auth_dict)
    assert (
        result.model_dump()
        == TenantConfig()
        .model_copy(update={"organization_config": org_config})
        .model_dump()
    )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_auth_info(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    # create a fresh org
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Update a value and confirm it is populated in nested org auth
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_identities\": 42}' WHERE id = $1",
            auth.organization_id,
        )
        token = client.headers["Authorization"].replace("Bearer ", "")
        result = await verify_auth_info(
            UnverifiedAuthInfo(
                x_tenant_id=auth.tenant_id,
                authorization=HTTPAuthorizationCredentials(
                    scheme="Bearer",
                    credentials=token,
                ),
            ),
            "workspaces:read",
        )

        assert result.tenant_config is not None
        assert result.tenant_config.organization_config is not None
        assert result.tenant_config.organization_config.max_identities == 42
        assert result.authorization_scheme == "BEARER"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_org_auth_info(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    # create a fresh org
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Update a value and confirm it is populated in org auth
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_identities\": 42}' WHERE id = $1",
            auth.organization_id,
        )
        token = client.headers["Authorization"].replace("Bearer ", "")
        result = await verify_org_auth_info(
            UnverifiedAuthInfo(
                x_organization_id=auth.organization_id,
                authorization=HTTPAuthorizationCredentials(
                    scheme="Bearer",
                    credentials=token,
                ),
            ),
            "organization:read",
        )

        assert result.org_config is not None
        assert result.org_config.max_identities == 42


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_auth_info_user_id_mismatch(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        token = client.headers["Authorization"].replace("Bearer ", "")
        with pytest.raises(HTTPException) as exc:
            await verify_auth_info(
                UnverifiedAuthInfo(
                    x_tenant_id=auth.tenant_id,
                    authorization=HTTPAuthorizationCredentials(
                        scheme="Bearer",
                        credentials=token,
                    ),
                    x_user_id=str(uuid.uuid4()),
                ),
                "workspaces:read",
            )
        assert exc.value.status_code == 403


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_auth_info_user_id_matches(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        token = client.headers["Authorization"].replace("Bearer ", "")
        result = await verify_auth_info(
            UnverifiedAuthInfo(
                x_tenant_id=auth.tenant_id,
                authorization=HTTPAuthorizationCredentials(
                    scheme="Bearer",
                    credentials=token,
                ),
                x_user_id=str(auth.user_id),
            ),
            "workspaces:read",
        )
        assert result.tenant_config is not None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_org_auth_info_user_id_mismatch(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        token = client.headers["Authorization"].replace("Bearer ", "")
        with pytest.raises(HTTPException) as exc:
            await verify_org_auth_info(
                UnverifiedAuthInfo(
                    x_organization_id=auth.organization_id,
                    authorization=HTTPAuthorizationCredentials(
                        scheme="Bearer",
                        credentials=token,
                    ),
                    x_user_id=str(uuid.uuid4()),
                ),
                "organization:read",
            )
        assert exc.value.status_code == 403


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_verify_org_auth_info_user_id_matches(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        token = client.headers["Authorization"].replace("Bearer ", "")
        result = await verify_org_auth_info(
            UnverifiedAuthInfo(
                x_organization_id=auth.organization_id,
                authorization=HTTPAuthorizationCredentials(
                    scheme="Bearer",
                    credentials=token,
                ),
                x_user_id=str(auth.user_id),
            ),
            "organization:read",
        )
        assert result.org_config is not None


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_tenant_config(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that tenant config takes precedence over org config."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id

        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"

        # Created config should have default values
        created_config = await db_asyncpg.fetchval(
            "SELECT config FROM tenants WHERE id = $1", auth.tenant_id
        )
        assert created_config["max_identities"] is None
        assert created_config["can_add_seats"] is None
        assert created_config["can_use_rbac"] is None
        assert TenantConfig().model_dump() == created_config

        input_config = TenantConfig(
            max_identities=67,
        )
        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )
        await _set_config(
            db_asyncpg,
            'config || \'{"max_identities": 1000, "startup_plan_approval_date": "2024-04-01", "flags": {"rbac_enabled": false}}\'',
            org_id,
        )

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            None,
        )

        assert not has_plan
        assert tenant_config.max_identities == 67
        assert tenant_config.startup_plan_approval_date == "2024-04-01"

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_tenant_config_missing_key(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that org config max_identities value is used when tenant config value is None."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"
        input_config = TenantConfig(max_identities=None)

        # Update org config to differ from tenant config
        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )
        await _set_config(
            db_asyncpg,
            'config || \'{"max_identities": 1000, "startup_plan_approval_date": "2024-04-01", "flags": {"rbac_enabled": true}}\'',
            org_id,
        )

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            None,
        )

        assert not has_plan
        assert tenant_config.max_identities == 1000
        assert tenant_config.startup_plan_approval_date == "2024-04-01"

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_tenant_config_defaults(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that tenant config defaults are used when org config is empty."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"
        input_config = TenantConfig()

        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )

        # Test with empty
        await _set_config(db_asyncpg, "'{}'", org_id)

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            None,
        )

        assert not has_plan
        assert tenant_config == input_config

        # Test with NULL
        await _set_config(db_asyncpg, "NULL", org_id)

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            None,
        )

        assert not has_plan
        assert tenant_config == input_config

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_tenant_config_metronome_overrides(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that Metronome plan and customer details override org config."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth

        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"
        input_config = TenantConfig(
            max_identities=67,
            can_use_rbac=False,
            # pass in default values for nested org config
            organization_config=OrganizationConfig(),
        )
        old_ff_payment_enabled = settings.shared_settings.FF_PAYMENT_ENABLED
        settings.shared_settings.FF_PAYMENT_ENABLED = True

        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )

        # Set org config to be overridden by Metronome
        await _set_config(
            db_asyncpg,
            'config || \'{"max_identities": 1000, "can_add_seats": true, "can_use_rbac": false, "max_workspaces": 3}\'',
            org_id,
        )

        customer_id = "018bf43f-f00d-4b29-a35a-589ecb2297e3"
        plan_id = "fc3a21ca-c68d-44b9-a4ea-c59eb9c9cec6"
        customer_dict = _customer_details_with_custom_fields(
            customer_id,
            {
                "can_add_seats": "false",
            },
        )
        plan_dict = _plan_details_with_custom_fields(
            plan_id,
            {
                "__tier": "plus_legacy",
                "max_identities": "1100",
                "can_use_rbac": "true",
                "max_workspaces": "12",
            },
        )

        await db_asyncpg.execute(
            """
            INSERT INTO org_metronome_cache
                (id, organization_id, customer_id, plan_id, customer_details, plan_details)
            VALUES
                ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (organization_id)
            DO UPDATE SET
                customer_id = $3,
                plan_id = $4,
                customer_details = $5,
                plan_details = $6,
                updated_at = now()
            RETURNING *
            """,
            uuid.uuid4(),
            org_id,
            UUID(customer_id),
            UUID(plan_id),
            customer_dict,
            plan_dict,
        )

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            customer_id,
        )

        assert has_plan is True
        expected_tenant_config = TenantConfig(
            max_identities=1100,
            can_add_seats=False,
            can_use_rbac=True,
            # nested should have updated values from metronome as well
            organization_config=OrganizationConfig(
                max_identities=1100,
                can_add_seats=False,
                can_use_rbac=True,
                max_workspaces=12,
            ),
        )
        assert expected_tenant_config.model_dump() == tenant_config.model_dump()

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )
        await db_asyncpg.execute(
            "DELETE FROM org_metronome_cache WHERE organization_id = $1", org_id
        )
        settings.shared_settings.FF_PAYMENT_ENABLED = old_ff_payment_enabled


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_org_tenant_config_nested(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that nested org config does NOT include inputted tenant config values but DOES include Metronome overrides."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"
        input_config = TenantConfig(
            max_identities=67,
            # pass in values for nested org config
            organization_config=OrganizationConfig(
                max_identities=112,
                max_workspaces=50,
            ),
        )
        old_ff_payment_enabled = settings.shared_settings.FF_PAYMENT_ENABLED
        settings.shared_settings.FF_PAYMENT_ENABLED = True

        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )

        # Set empty org config to show inputted org config used
        await _set_config(
            db_asyncpg,
            "'{}'",
            org_id,
        )

        customer_id = "018bf43f-f00d-4b29-a35a-589ecb2297e3"
        plan_id = "fc3a21ca-c68d-44b9-a4ea-c59eb9c9cec6"
        customer_dict = _customer_details_with_custom_fields(
            customer_id,
            {
                "can_add_seats": "true",
            },
        )
        plan_dict = _plan_details_with_custom_fields(
            plan_id,
            {
                "__tier": "plus_legacy",
                "can_use_rbac": "true",
                "max_workspaces": "12",
                "can_use_langgraph_cloud": "true",
                "flags": {
                    "rbac_enabled": "true",
                },
            },
        )

        await db_asyncpg.execute(
            """
            INSERT INTO org_metronome_cache
                (id, organization_id, customer_id, plan_id, customer_details, plan_details)
            VALUES
                ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (organization_id)
            DO UPDATE SET
                customer_id = $3,
                plan_id = $4,
                customer_details = $5,
                plan_details = $6,
                updated_at = now()
            RETURNING *
            """,
            uuid.uuid4(),
            org_id,
            UUID(customer_id),
            UUID(plan_id),
            customer_dict,
            plan_dict,
        )

        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            auth.organization_is_personal,
            customer_id,
        )

        # Test that inputted max_identities values are used if not overridden by Metronome.
        # Note the different values for max_identities because the org context is not overridden by the tenant context
        # for the nested org config.
        assert has_plan is True
        expected_tenant_config = TenantConfig(
            max_identities=67,
            can_add_seats=True,
            can_use_rbac=True,
            # nested should have updated values from metronome as well
            organization_config=OrganizationConfig(
                max_identities=112,
                can_add_seats=True,
                can_use_rbac=True,
                max_workspaces=12,
                can_use_langgraph_cloud=True,
            ),
        )
        assert expected_tenant_config.model_dump() == tenant_config.model_dump()

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )
        await db_asyncpg.execute(
            "DELETE FROM org_metronome_cache WHERE organization_id = $1", org_id
        )
        settings.shared_settings.FF_PAYMENT_ENABLED = old_ff_payment_enabled


@pytest.mark.skipif(
    config.settings.AUTH_TYPE == "none" or config.settings.BASIC_AUTH_ENABLED,
    reason="single tenant/different config",
)
async def test_org_tenant_config_personal_overrides_org(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a personal org always has one seat if no Metronome plan."""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"
        input_config = TenantConfig(max_identities=67)

        old_config = await db_asyncpg.fetchval(
            "SELECT config FROM organizations WHERE id = $1",
            org_id,
        )

        await _set_config(db_asyncpg, "config || '{\"max_identities\": 1000}'", org_id)

        # Test with personal org
        tenant_config, has_plan = await get_org_tenant_config(
            input_config,
            org_id,
            True,
            None,
        )

        assert not has_plan
        assert (
            TenantConfig(
                max_identities=1,
                can_use_rbac=True,
                can_add_seats=True,
            ).model_dump()
            == tenant_config.model_dump()
        )

        # Reset the config
        await db_asyncpg.execute(
            "UPDATE organizations SET config = $1 WHERE id = $2",
            old_config,
            org_id,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_extra_config_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that organization config and tenant config can have extra fields in the database."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=TenantConfig(
            max_identities=67,
            can_use_rbac=True,
            can_add_seats=True,
            startup_plan_approval_date="2024-04-01",
            partner_plan_approval_date="2024-04-01",
        ),
    ) as authed_client:
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"

        # Add a key to the tenant config and org config
        input_config = await db_asyncpg.fetchval(
            """
            UPDATE tenants SET config = config || '{
                "some_new_field": "tenant"
            }'
            WHERE id = $1
            RETURNING config
            """,
            auth.tenant_id,
        )
        input_tenant_config = TenantConfig.model_validate(input_config)
        org_config = await db_asyncpg.fetchval(
            """
            UPDATE organizations SET config = config || '{
                "some_new_field": "org"
            }'
            WHERE id = $1
            RETURNING config
            """,
            org_id,
        )
        OrganizationConfig.model_validate(org_config)

        await get_org_tenant_config(
            input_tenant_config,
            org_id,
            auth.organization_is_personal,
            None,
        )


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_missing_config_fields(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that organization config and tenant config can have missing fields in the database."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
    ) as authed_client:
        auth = authed_client.auth
        client = authed_client.client

        # set config to have zero fields
        await db_asyncpg.execute(
            "UPDATE organizations SET config = '{}' WHERE id = $1",
            auth.organization_id,
        )
        await db_asyncpg.execute(
            "UPDATE tenants SET config = '{}' WHERE id = $1",
            auth.tenant_id,
        )

        expected_org_config = OrganizationConfig(
            max_identities=5,
            can_add_seats=True,
            can_use_rbac=False,
        )
        # These are set in .env.local_test
        expected_tenant_config = TenantConfig(
            organization_config=expected_org_config,
            max_identities=5,
            can_add_seats=True,
            can_use_rbac=False,
        )

        # Test both workspace and org auth.
        # user_id and user_email are empty for x-service auth
        res = await internal_auth_request(auth.tenant_id)
        assert auth.organization_id == res.organization_id
        assert auth.tenant_id == res.tenant_id
        assert res.user_id is None
        assert res.user_email is None
        assert expected_tenant_config.model_dump() == res.tenant_config.model_dump()
        res = await internal_org_auth_request(auth.organization_id)
        assert auth.organization_id == res.organization_id
        assert res.user_id is None
        assert res.user_email is None
        assert expected_org_config.model_dump() == res.org_config.model_dump()

        # confirm hitting an org endpoint and workspace endpoint succeed as well
        res = await client.get("/orgs/current/info")
        assert res.status_code == 200
        org_config = res.json()["config"]
        res_config = OrganizationConfig(**org_config)
        assert expected_org_config.model_dump() == res_config.model_dump()
        res = await client.get("/workspaces/current/members")
        assert res.status_code == 200


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_missing_config_fields_auth_dict(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test internal auth request defaults when no config is present in the database."""
    # TODO: make this test less brittle once config is in a more stable place
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
    ) as authed_client:
        auth = authed_client.auth

        # set config to have zero fields
        await db_asyncpg.execute(
            "UPDATE organizations SET config = '{}' WHERE id = $1",
            auth.organization_id,
        )
        await db_asyncpg.execute(
            "UPDATE tenants SET config = '{}' WHERE id = $1",
            auth.tenant_id,
        )

        auth_resp = await internal_platform_request(
            "GET",
            "/internal/auth",
            jwt_payload={"tenant_id": str(auth.tenant_id)},
        )
        auth_dict = orjson.loads(auth_resp.body)
        expected_org_config = {
            "max_identities": 5,
            "max_workspaces": 1,
            "can_use_rbac": False,
            "can_add_seats": True,
            "startup_plan_approval_date": None,
            "premier_plan_approval_date": None,
            "can_disable_public_sharing": False,
            "can_serve_datasets": False,
            "can_use_langgraph_cloud": True,
            "max_langgraph_cloud_deployments": 3,
            "max_free_langgraph_cloud_deployments": 0,
            "can_use_saml_sso": False,
            "datadog_rum_session_sample_rate": 20,
            "can_use_bulk_export": False,
            "demo_lgp_new_graph_enabled": False,
            "show_updated_sidenav": False,
            "show_updated_resource_tags": False,
            "kv_dataset_message_support": True,
            "show_playground_prompt_canvas": False,
            "allow_custom_iframes": False,
            "enable_langgraph_pricing": False,
            "enable_thread_view_playground": False,
            "enable_org_usage_charts": False,
            "enable_select_all_traces": False,
            "playground_evaluator_strategy": "sync",
            "langgraph_deploy_own_cloud_enabled": False,
            "enable_k8s_vanilla_platform": False,
            "langgraph_remote_reconciler_enabled": False,
            "langsmith_alerts_poc_enabled": True,
            "lgp_templates_enabled": False,
            "enable_prebuilt_dashboards": False,
            "langsmith_experimental_search_enabled": False,
            "enable_align_evaluators": False,
            "enable_new_filter_bar": False,
            "enable_monthly_usage_charts": False,
        }
        expected_tenant_config = {
            "max_identities": None,
            "max_hourly_tracing_requests": 105000,
            "max_hourly_tracing_bytes": 2000000000,
            "max_monthly_total_unique_traces": 10000000,
            "max_events_ingested_per_minute": 20000,
            "max_run_rules": 100,
            "startup_plan_approval_date": None,
            "premier_plan_approval_date": None,
            "organization_config": expected_org_config,
        }
        expected_auth_dict = {
            "organization_id": str(auth.organization_id),
            "organization_is_personal": auth.organization_is_personal,
            "organization_disabled": False,
            "tenant_id": str(auth.tenant_id),
            "tenant_handle": str(auth.tenant_handle) if auth.tenant_handle else "",
            "tenant_config": expected_tenant_config,
            "tenant_is_deleted": False,
            "identity_read_only": False,
            "identity_permissions": [],
            "is_sso_user": False,
            "organization_config": expected_org_config,
            "organization_permissions": [],
            "service_identity": "unspecified",
            "public_sharing_disabled": False,
            "sso_only": False,
        }

        # Only check fields we're expecting since we don't care about extra fields
        for k, v in expected_auth_dict.items():
            assert k in auth_dict
            assert auth_dict[k] == v

        auth_resp = await internal_platform_request(
            "GET",
            "/internal/org-auth",
            jwt_payload={"organization_id": str(auth.organization_id)},
        )
        auth_dict = orjson.loads(auth_resp.body)
        expected_auth_dict = {
            "organization_id": str(auth.organization_id),
            "organization_is_personal": auth.organization_is_personal,
            "organization_disabled": False,
            "identity_read_only": False,
            "organization_permissions": [],
            "is_sso_user": False,
            "organization_config": expected_org_config,
            "service_identity": "unspecified",
            "public_sharing_disabled": False,
            "sso_only": False,
        }
        for k, v in expected_auth_dict.items():
            assert k in auth_dict
            assert auth_dict[k] == v


@mock.patch("app.api.auth.verify.internal_platform_request")
async def test_missing_config_fields_internal(
    mock_internal_platform_request: mock.Mock,
) -> None:
    """Test that organization config / tenant config can have missing fields for internal auth."""
    # should not fail on valid field that is present with None
    tenant_id = uuid4()
    org_id = uuid4()
    max_identities = 10
    minimal_fields = {
        "tenant_id": str(tenant_id),
        "organization_id": str(org_id),
        "organization_is_personal": False,
        "organization_disabled": False,
        "organization_config": {
            "unknown_field": "blah",
            "max_identities": max_identities,
            "max_workspaces": 1,
            "can_use_saml_sso": None,
        },
        "tenant_config": {
            "unknown_field": "blah",
            "max_identities": max_identities,
            "max_workspaces": 1,
            "can_use_rbac": None,
        },
        "identity_read_only": False,
    }
    mock_resp = mock.Mock()
    mock_resp.body = orjson.dumps(minimal_fields)
    mock_internal_platform_request.return_value = mock_resp

    auth = await internal_auth_request(tenant_id)
    assert str(auth.tenant_id) == minimal_fields["tenant_id"]
    assert str(auth.organization_id) == minimal_fields["organization_id"]
    assert auth.identity_id is None
    expected_org_config = OrganizationConfig(
        max_identities=max_identities,
    )
    expected_tenant_config = TenantConfig(
        organization_config=expected_org_config, max_identities=max_identities
    )
    assert expected_tenant_config.model_dump() == auth.tenant_config.model_dump(), (
        "Tenant config should have defaults"
    )

    assert (
        expected_org_config.model_dump()
        == auth.tenant_config.organization_config.model_dump()
    ), "Org config should have defaults"

    mock_internal_platform_request.assert_called_once_with(
        "GET",
        "/internal/auth",
        jwt_payload={"tenant_id": minimal_fields["tenant_id"]},
    )


@mock.patch("app.api.auth.verify.internal_platform_request")
async def test_missing_org_config_fields_internal(
    mock_internal_platform_request: mock.Mock,
) -> None:
    """Test that organization config / tenant config can have missing fields for internal org auth."""
    # should not fail on valid field that is present with None
    org_id = uuid4()
    max_identities = 10
    minimal_fields = {
        "organization_id": str(org_id),
        "organization_is_personal": False,
        "organization_disabled": False,
        "organization_config": {
            "unknown_field": "blah",
            "max_identities": max_identities,
            "max_workspaces": 1,
            "can_use_saml_sso": None,
        },
        "identity_id": {"Valid": False, "String": ""},
        "identity_read_only": False,
    }
    mock_resp = mock.Mock()
    mock_resp.body = orjson.dumps(minimal_fields)
    mock_internal_platform_request.return_value = mock_resp

    auth = await internal_org_auth_request(org_id)
    assert str(auth.organization_id) == minimal_fields["organization_id"]
    assert auth.identity_id is None
    expected_org_config = OrganizationConfig(
        max_identities=max_identities,
    )
    assert expected_org_config.model_dump() == auth.org_config.model_dump(), (
        "Org config should have defaults"
    )

    mock_internal_platform_request.assert_called_once_with(
        "GET",
        "/internal/org-auth",
        jwt_payload={"organization_id": minimal_fields["organization_id"]},
    )
