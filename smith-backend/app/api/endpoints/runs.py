"""Endpoints for Runs."""

import asyncio
from datetime import datetime
from typing import Annotated, Literal, Optional
from uuid import UUID, uuid4

import orjson
import structlog
from fastapi import Body, Depends, Header, HTTPException, Query, Request, Response
from fastapi.responses import ORJ<PERSON>NResponse
from lc_config.settings import shared_settings as settings
from lc_database import api_router
from lc_database.curl import platform_request
from lc_database.queue.serializer import ORJSONSerializer
from lc_logging.audit_logs import audit_operation_name
from prometheus_client import Histogram
from sse_starlette import EventSourceResponse
from starlette_context import context

from app import config, crud, models, schemas
from app.api import deps
from app.api.auth.schemas import Permissions, ServiceIdentity
from app.models.runs.accept import parse_multipart_form
from app.models.runs.usage_limits import (
    has_tenant_exceeded_monthly_limits,
    has_tenant_exceeded_usage_limits,
)
from app.models.runs.validate import (
    convert_timestamp_to_isostring,
    parse_validate_batch,
    parse_validate_patch,
    parse_validate_post,
    validate_dotted_order,
)
from app.models.shared.utils import is_user_auth
from app.receive import consume_body
from app.stream import jsonpatch_sse_stream
from app.utils import arun_in_executor, gated_coro

router = api_router.TrailingSlashRouter()

logger = structlog.get_logger(__name__)

MAX_SIZE_BYTES = config.settings.MAX_SIZE_POST_BODY_FIELD_KB * 1024


@router.get("/rules", response_model=list[schemas.RunRulesSchema])
@audit_operation_name("read_rules")
async def list_rules(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_READ)),
    dataset_id: UUID | None = None,
    session_id: UUID | None = None,
    type: Literal["session", "dataset"] | None = None,
    name_contains: str | None = None,
    id: list[UUID] | None = Query(None),
) -> list[schemas.RunRulesSchema]:
    """List all run rules."""
    return await models.runs.rules.list_rules(
        auth, dataset_id, session_id, type, name_contains, id
    )


@router.post("/rules", response_model=schemas.RunRulesSchema)
@audit_operation_name("create_rule")
async def create_rule(
    rule: schemas.RunRulesCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_CREATE)),
) -> schemas.RunRulesSchema:
    """Create a new run rule."""
    return await models.runs.rules.create_rule(auth, rule)


@router.patch("/rules/{rule_id}", response_model=schemas.RunRulesSchema)
@audit_operation_name("update_rule")
async def update_rule(
    rule_id: UUID,
    rule: schemas.RunRulesCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_UPDATE)),
) -> schemas.RunRulesSchema:
    """Update a run rule."""
    return await models.runs.rules.update_rule(auth, rule_id, rule)


@router.get("/rules/{rule_id}/logs", response_model=list[schemas.RuleLogSchema])
@audit_operation_name("read_rule_logs")
async def list_rule_logs(
    response: Response,
    rule_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_READ)),
    limit: Annotated[int, Query(ge=100, le=1440)] = 720,
    offset: Annotated[int, Query(ge=0)] = 0,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
) -> list[schemas.RuleLogSchema]:
    """List logs for a particular rule"""
    if end_time and start_time and start_time >= end_time:
        raise HTTPException(
            status_code=422, detail="start_time must be before end_time"
        )

    retval, size = await models.runs.rules.list_rule_logs(
        auth=auth,
        rule_id=rule_id,
        limit=limit,
        offset=offset,
        start_time=start_time,
        end_time=end_time,
    )

    response.headers["X-Pagination-Total"] = str(size + offset)
    return retval


@router.get("/rules/{rule_id}/last_applied", response_model=schemas.RuleLogSchema)
async def get_last_applied_rule(
    rule_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_READ)),
) -> schemas.RuleLogSchema:
    """Get the last applied rule."""
    return await models.runs.rules.get_last_application_for_rule(auth, rule_id)


@router.post("/rules/{rule_id}/trigger")
@audit_operation_name("trigger_rule")
async def trigger_rule(
    rule_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_UPDATE)),
) -> schemas.RunRulesSchema:
    """Trigger a run rule manually."""
    return await models.runs.rules.trigger_rule(auth, rule_id)


@router.post("/rules/trigger")
@audit_operation_name("trigger_rules")
async def trigger_rules(
    payload: schemas.TriggerRulesRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_UPDATE)),
) -> None:
    """Trigger an array of run rules manually."""
    if payload.rule_ids:
        coroutines = [
            models.runs.rules.trigger_rule(auth, rule_id)
            for rule_id in payload.rule_ids
        ]
        semaphore = asyncio.Semaphore(settings.RULES_TRIGGER_SEMAPHORE)
        await asyncio.gather(*(gated_coro(coro, semaphore) for coro in coroutines))
    elif payload.dataset_id:
        await models.runs.rules.trigger_rules_by_dataset(auth, payload.dataset_id)


@router.delete("/rules/{rule_id}")
@audit_operation_name("delete_rule")
async def delete_rule(
    rule_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RULES_DELETE)),
) -> None:
    """Delete a run rule."""
    await models.runs.rules.delete_rule(auth, rule_id)
    return ORJSONResponse({"message": "Rule deleted"})


@router.get("/{run_id}", response_model=schemas.RunSchema)
@audit_operation_name("read_run")
async def read_run(
    run_id: UUID,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
    exclude_s3_stored_attributes: bool = False,
    exclude_serialized: bool = False,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
) -> schemas.RunSchema:
    """Get a specific run."""

    exclude_select = []

    if exclude_s3_stored_attributes:
        exclude_select.extend(
            [
                schemas.RunSelect.inputs,
                schemas.RunSelect.events,
                schemas.RunSelect.outputs,
                schemas.RunSelect.error,
                schemas.RunSelect.events,
                schemas.RunSelect.inputs_preview,
                schemas.RunSelect.outputs_preview,
                schemas.RunSelect.child_run_ids,
            ]
        )

    if exclude_serialized:
        exclude_select.extend([schemas.RunSelect.serialized])

    return await crud.get_run(
        auth,
        run_id,
        session_id=session_id,
        start_time=start_time,
        exclude_select=exclude_select,
        include_feedback=Permissions.FEEDBACK_READ.value in auth.identity_permissions,
    )


@router.get("/{run_id}/share", response_model=schemas.RunShareSchema | None)
async def read_run_share_state(
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
):
    """Get the state of sharing of a run."""
    return await crud.get_share_key_by_run_id(auth, run_id)


@router.put("/{run_id}/share", response_model=schemas.RunShareSchema)
@audit_operation_name("share_run")
async def share_run(
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_SHARE)),
):
    """Share a run."""
    return await crud.share_run(auth, run_id)


@router.delete("/{run_id}/share")
@audit_operation_name("unshare_run")
async def unshare_run(
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_SHARE)),
):
    """Unshare a run."""
    await crud.unshare_run(auth, run_id)
    return ORJSONResponse({"message": "Run unshared"})


@router.post("/query", response_model=schemas.ListRunsResponse)
@audit_operation_name("read_runs")
async def query_runs(
    body: schemas.BodyParamsForRunSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
):
    if body.session:
        await crud.ensure_tracer_sessions(auth, body.session)

    if body.use_experimental_search:
        if not config.settings.QUICKWIT_SEARCH_ENABLED:
            raise HTTPException(
                status_code=501,
                detail="Search filter is not enabled.",
            )

        return await models.runs.fetch_qw.fetch_runs(
            auth,
            body,
        )

    use_optimized_fetch = (
        auth.tenant_id is not None
        and str(auth.tenant_id) in settings.FETCH_RUNS_OPTIMIZED_TENANTS
    )

    """Get all runs by query in body payload."""
    return await models.runs.fetch_ch.fetch_runs(
        auth,
        body,
        include_feedback=Permissions.FEEDBACK_READ.value in auth.identity_permissions,
        use_optimized_fetch=use_optimized_fetch,
        max_threads=settings.FETCH_RUNS_API_MAX_THREADS
        if not is_user_auth(auth)
        else None,
    )


@router.post("/generate-query", response_model=schemas.ResponseBodyForRunsGenerateQuery)
async def generate_query_for_runs(
    body: schemas.RequestBodyForRunsGenerateQuery,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
):
    """Get runs filter expression query for a given natural language query."""
    return await models.runs.generate_filter.generate_runs_filter(auth, body)


@router.post(
    "/stats", response_model=schemas.RunStats | dict[str | datetime, schemas.RunStats]
)
@audit_operation_name("read_runs_stats")
async def stats_runs(
    body: schemas.RunStatsQueryParams,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
):
    """Get all runs by query in body payload."""
    return await models.runs.stats.run_stats(auth, body)


@router.post("/monitor", response_model=schemas.MonitorResponse)
@audit_operation_name("read_runs_monitors")
async def monitor_tracer_session(
    params: schemas.MonitorRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
) -> schemas.MonitorResponse:
    """Get monitoring data for a specific session."""
    return await crud.get_runs_monitoring(auth, params)


@router.post("", deprecated=True, include_in_schema=False)
@audit_operation_name("create_run")
async def create_run(
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_CREATE)),
):
    """Create a new run."""

    await has_tenant_exceeded_usage_limits(auth)
    raw = await consume_body(request)
    run = await arun_in_executor(parse_validate_post, raw)

    run_id, trace_id, dotted_order, parent_run_id = (
        run.get("id"),
        run.get("trace_id"),
        run.get("dotted_order"),
        run.get("parent_run_id"),
    )
    try:
        if run_id is None:
            run_id = uuid4()
            if context.exists():
                context["run_ids"] = [str(run_id)]
        else:
            if context.exists():
                context["run_ids"] = [run_id]
            run_id = UUID(run_id)
        if trace_id is not None:
            trace_id = UUID(trace_id)
        if parent_run_id is not None:
            parent_run_id = UUID(parent_run_id)
    except ValueError:
        raise HTTPException(
            status_code=422,
            detail=f"Invalid UUID received for run_id:{run_id} trace_id:{trace_id} dotted_order:{dotted_order} parent_run_id:{parent_run_id}",
        )
    validate_dotted_order(trace_id, dotted_order, parent_run_id, run_id)

    payloads = [
        crud.QueuePayload(
            run_id=run_id,
            parent_id=parent_run_id,
            trace_id=trace_id,
            value=raw,
            hash_key="post",
            process_inline=dotted_order is not None and trace_id is not None,
            session_id=run.get("session_id"),
            session_name=run.get("session_name"),
            start_time=convert_timestamp_to_isostring(run.get("start_time")),
            auto_upgrade=bool(run.get("reference_example_id")),
        )
    ]
    await crud.ensure_sessions_before_queue_run_payload(auth, payloads)
    await crud.queue_run_payload(auth, {trace_id} if trace_id else None, payloads)
    return ORJSONResponse({"message": "Run created"}, status_code=202)


@router.patch("/{run_id}")
@audit_operation_name("update_run")
async def update_run(
    run_id: UUID,
    request: Request,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_CREATE)),
):
    """Update a run."""
    if settings.LANGCHAIN_ENV == "local_test":
        await has_tenant_exceeded_usage_limits(auth)
        raw = await consume_body(request)
        run = await arun_in_executor(parse_validate_patch, raw)

        if context.exists():
            context["run_ids"] = [str(run_id)]
        trace_id, dotted_order, parent_run_id = (
            run.get("trace_id"),
            run.get("dotted_order"),
            run.get("parent_run_id"),
        )

        try:
            if trace_id is not None:
                trace_id = UUID(trace_id)
            if parent_run_id is not None:
                parent_run_id = UUID(parent_run_id)
        except ValueError:
            raise HTTPException(
                status_code=422,
                detail=f"Invalid UUID received for run_id:{run_id} trace_id:{trace_id} dotted_order:{dotted_order} parent_run_id:{parent_run_id}",
            )
        validate_dotted_order(trace_id, dotted_order, parent_run_id, run_id)

        payloads = [
            crud.QueuePayload(
                run_id=run_id,
                trace_id=trace_id,
                parent_id=parent_run_id,
                value=raw,
                hash_key="patch",
                process_inline=dotted_order is not None and trace_id is not None,
                auto_upgrade=bool(run.get("reference_example_id")),
            )
        ]
        await crud.ensure_sessions_before_queue_run_payload(auth, payloads)
        await crud.queue_run_payload(auth, {trace_id} if trace_id else None, payloads)
        return ORJSONResponse({"message": "Run updated"}, status_code=202)

    res = await platform_request(
        "PATCH",
        f"/runs/{run_id}",
        headers=dict(request.headers),
        body=await request.body(),
        raise_error=False,
    )
    headers = dict(res.headers)
    return Response(content=res.body, status_code=res.code, headers=headers)


RUNS_BATCH_COUNT = Histogram(
    "runs_batch_count",
    "Total count of runs processed in batch.",
    ["type"],
    namespace="langsmith",
    buckets=(1.0, 2.5, 5.0, 7.5, 10.0, 20.0, 40.0, 80.0, 120.0, 200.0, float("inf")),
)


@router.post("/batch", deprecated=True, include_in_schema=False)
@audit_operation_name("create_runs_batch")
async def batch_ingest_runs(
    request: Request,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            Permissions.RUNS_CREATE, allowed_services=[ServiceIdentity.UNSPECIFIED]
        )
    ),
):
    """Batch ingest runs."""

    await has_tenant_exceeded_usage_limits(auth)
    raw = await consume_body(request)
    batch = await arun_in_executor(parse_validate_batch, raw)

    post = batch.get("post", [])
    patch = batch.get("patch", [])
    runs = post + patch

    RUNS_BATCH_COUNT.labels(type="post").observe(len(post))
    RUNS_BATCH_COUNT.labels(type="patch").observe(len(patch))
    RUNS_BATCH_COUNT.labels(type="total").observe(len(runs))

    if not runs:
        raise HTTPException(status_code=422, detail="Empty batch")

    for run in runs:
        try:
            dotted_order = run["dotted_order"]
            trace_id, parent_run_id, run_id = (
                UUID(run["trace_id"]),
                UUID(run["parent_run_id"]) if run.get("parent_run_id") else None,
                UUID(run["id"]),
            )
        except (ValueError, KeyError):
            raise HTTPException(
                status_code=422,
                detail=f"Invalid identifiers received for run_id:{run.get('id')} trace_id:{run.get('trace_id')} dotted_order:{run.get('dotted_order')} parent_run_id:{run.get('parent_run_id')}",
            )
        validate_dotted_order(trace_id, dotted_order, parent_run_id, run_id)
    if context.exists():
        context["run_ids"] = [run["id"] for run in runs]

    runs_bytes = await arun_in_executor(_dumps_each, runs)

    payloads = [
        crud.QueuePayload(
            run_id=UUID(run["id"]),
            trace_id=UUID(run["trace_id"]),
            parent_id=UUID(run["parent_run_id"]) if run.get("parent_run_id") else None,
            value=raw,
            hash_key="post" if run in post else "patch",
            process_inline=True,
            session_id=run.get("session_id"),
            session_name=run.get("session_name"),
            start_time=convert_timestamp_to_isostring(run.get("start_time")),
            auto_upgrade=bool(run.get("reference_example_id")),
        )
        for run, raw in zip(runs, runs_bytes)
    ]
    await crud.ensure_sessions_before_queue_run_payload(auth, payloads)
    await crud.queue_run_payload(auth, None, payloads)

    return ORJSONResponse({"message": "Runs batch ingested"}, status_code=202)


@router.post("/multipart", deprecated=True, include_in_schema=False)
@audit_operation_name("create_runs_multipart")
async def multipart_ingest_runs(
    request: Request,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            Permissions.RUNS_CREATE, allowed_services=[ServiceIdentity.UNSPECIFIED]
        )
    ),
):
    """Batch ingest runs."""

    await has_tenant_exceeded_monthly_limits(auth)
    async with parse_multipart_form(request, auth) as (
        feedback,
        post,
        patch,
        extras,
    ):
        runs = post + patch

        RUNS_BATCH_COUNT.labels(type="post").observe(len(post))
        RUNS_BATCH_COUNT.labels(type="patch").observe(len(patch))
        RUNS_BATCH_COUNT.labels(type="total").observe(len(runs))

        if not runs and not feedback:
            raise HTTPException(status_code=422, detail="Empty batch")

        for run in runs:
            try:
                dotted_order = run["dotted_order"]
                trace_id, parent_run_id, run_id = (
                    UUID(run["trace_id"]),
                    UUID(run["parent_run_id"]) if run.get("parent_run_id") else None,
                    UUID(run["id"]),
                )
            except (ValueError, KeyError):
                raise HTTPException(
                    status_code=422,
                    detail=f"Invalid identifiers received for run_id:{run.get('id')} trace_id:{run.get('trace_id')} dotted_order:{run.get('dotted_order')} parent_run_id:{run.get('parent_run_id')}",
                )
            validate_dotted_order(trace_id, dotted_order, parent_run_id, run_id)
        if context.exists():
            context["run_ids"] = [run["id"] for run in runs]

        runs_bytes = await arun_in_executor(_dumps_each, runs)

        if feedback:
            feedback_configs = {
                c["feedback_key"]: c
                for c in await models.feedback_configs.fetch.fetch_feedback_configs_cached(
                    auth, sorted({f["key"] for f in feedback})
                )
            }
            for f in feedback:
                stored_config = feedback_configs.get(f["key"], {}).get(
                    "feedback_config"
                )
                default_config = models.feedback.ingest.get_default_feedback_config([f])
                feedback_config = models.feedback.ingest.resolve_feedback_config(
                    stored_config=stored_config,
                    payload_config=f["feedback_config"],
                    default_config=default_config,
                )
                models.feedback.ingest.verify_feedback_config(f, feedback_config)

        runs_bytes = await arun_in_executor(_dumps_each, runs)

        await crud.queue_run_payload(
            auth,
            None,
            [
                crud.QueuePayload(
                    run_id=UUID(run["id"]),
                    trace_id=UUID(run["trace_id"]),
                    parent_id=UUID(run["parent_run_id"])
                    if run.get("parent_run_id")
                    else None,
                    value=raw,
                    hash_key="post" if run in post else "patch",
                    process_inline=True,
                    session_id=run.get("session_id"),
                    session_name=run.get("session_name"),
                    start_time=convert_timestamp_to_isostring(run.get("start_time")),
                    extra=extras.get(run["id"]),
                    auto_upgrade=bool(run.get("reference_example_id")),
                )
                for run, raw in zip(runs, runs_bytes)
            ]
            + [
                crud.QueuePayload(
                    run_id=f["run_id"],
                    trace_id=f["trace_id"],
                    parent_id=None,
                    set_key="feedback",
                    value=ORJSONSerializer.dumps(f),
                    process_inline=True,
                )
                for f in feedback
            ],
            affects_shortterm_limits=False,
        )

    return ORJSONResponse({"message": "Runs batch ingested"}, status_code=202)


@router.post("/group")
@audit_operation_name("read_runs_grouped")
async def group_runs(
    body: schemas.RunGroupRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
    accept: Annotated[str | None, Header()] = None,
):
    """Get runs grouped by an expression"""

    if body.session_id:
        # verify tracer sessions
        await crud.ensure_tracer_sessions(auth, [body.session_id])

    if accept == "text/event-stream":
        return EventSourceResponse(
            jsonpatch_sse_stream(models.runs.group.stream_group_runs(auth, body))
        )

    return await models.runs.group.group_runs(auth, body)


@router.post("/group/stats", response_model=schemas.RunGroupStats)
@audit_operation_name("read_runs_grouped_stats")
async def stats_group_runs(
    body: schemas.RunGroupRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
):
    """Get stats for the grouped runs."""
    return await models.runs.group_stats.group_stats(auth, body)


def _dumps_each(runs: list[dict]) -> list[bytes]:
    """Dump runs to bytes."""
    return [orjson.dumps(run) for run in runs]


@router.post("/delete")
@audit_operation_name("delete_runs")
async def delete_runs(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_DELETE)),
    session_id: UUID = Body(...),
    trace_ids: list[UUID] = Body(...),
) -> None:
    """Delete specific runs."""
    await crud.delete_runs(auth, session_id, trace_ids)
    return ORJSONResponse({"message": "Runs deleted"}, status_code=202)
