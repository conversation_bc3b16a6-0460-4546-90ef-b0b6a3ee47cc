"""Endpoints for shared objects (such as Runs)."""

from typing import Annotated, List
from uuid import UUID

import or<PERSON><PERSON>
from fastapi import Depends, Header, HTTPException, Query, Response
from fastapi.responses import ORJSONResponse
from lc_config.settings import shared_settings as settings
from lc_database import api_router
from lc_logging.audit_logs import audit_operation_name
from sse_starlette import EventSourceResponse

from app import crud, models, schemas
from app.api import auth, deps
from app.models.examples.validate import get_message_format, get_tool_format
from app.models.feedback.fetch import fetch_feedbacks, fetch_feedbacks_postgres
from app.stream import jsonpatch_sse_stream

router = api_router.TrailingSlashRouter()


@router.get("/{share_token}/run", response_model=schemas.RunPublicSchema)
@audit_operation_name("read_shared_run")
async def get_shared_run(
    exclude_s3_stored_attributes: bool = False,
    share: auth.ShareRunInfo = Depends(deps.get_share_run_info),
):
    """Get the shared run."""
    return await crud.get_run(
        share,
        share.run_id,
        exclude_select=[schemas.RunSelect.inputs, schemas.RunSelect.outputs]
        if exclude_s3_stored_attributes
        else [],
    )


@router.get("/{share_token}/run/{id}", response_model=schemas.RunPublicSchema)
@audit_operation_name("read_shared_run")
async def get_shared_run_by_id(
    id: UUID,
    exclude_s3_stored_attributes: bool = False,
    share: auth.ShareRunInfo = Depends(deps.get_share_run_info),
):
    """Get the shared run."""
    if id not in share.allowed_run_ids:
        raise HTTPException(status_code=404, detail="Resource not found")
    return await crud.get_run(
        share,
        id,
        exclude_select=[schemas.RunSelect.inputs, schemas.RunSelect.outputs]
        if exclude_s3_stored_attributes
        else [],
    )


@router.post("/{share_token}/runs/query", response_model=schemas.ListPublicRunsResponse)
@audit_operation_name("read_shared_runs")
async def query_shared_runs(
    body: schemas.QueryParamsForPublicRunSchema,
    share: auth.ShareRunInfo = Depends(deps.get_share_run_info),
):
    """Get run by ids or the shared run if not specifed."""
    return await crud.get_public_runs(share, body)


@router.get("/{share_token}/feedbacks", response_model=List[schemas.FeedbackSchema])
@audit_operation_name("read_shared_feedbacks")
async def read_shared_feedbacks(
    response: Response,
    share: auth.ShareRunInfo = Depends(deps.get_share_run_info),
    run: Annotated[list[UUID] | None, Query()] = None,
    key: Annotated[list[str] | None, Query()] = None,
    session: Annotated[list[UUID] | None, Query()] = None,
    source: Annotated[list[schemas.SourceType] | None, Query()] = None,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    offset: Annotated[int, Query(ge=0)] = 0,
    user: Annotated[list[UUID] | None, Query()] = None,
    has_comment: bool | None = None,
    has_score: bool | None = None,
    level: schemas.FeedbackLevel | None = None,
):
    # We don't pass include_user_names for public feedbacks
    use_pg = settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL or (
        share.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    )
    if use_pg:
        rows, total = await fetch_feedbacks_postgres(
            share,
            schemas.QueryParamsForFeedbackSchema(
                run=run,
                key=key,
                session=session,
                source=source,
                limit=limit,
                offset=offset,
                user=user,
                has_comment=has_comment,
                has_score=has_score,
                level=level,
            ),
        )
        response.headers["X-Pagination-Total"] = str(total)
        return rows

    rows, total = await fetch_feedbacks(
        share,
        schemas.QueryParamsForFeedbackSchema(
            run=run,
            key=key,
            session=session,
            source=source,
            limit=limit,
            offset=offset,
            user=user,
            has_comment=has_comment,
            has_score=has_score,
            level=level,
        ),
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get("/{share_token}/datasets", response_model=schemas.DatasetPublicSchema)
@audit_operation_name("read_shared_dataset")
async def read_shared_dataset(
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    query_params: schemas.QueryParamsForPublicDatasetSchema = Depends(),
) -> schemas.DatasetPublicSchema:
    """Get dataset by ids or the shared dataset if not specifed."""
    return await crud.get_public_dataset(share, query_params)


@router.get("/{share_token}/examples/count", response_model=int)
async def count_shared_examples(
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    id: Annotated[list[UUID] | None, Query()] = None,
    as_of: schemas.AsOfType = "latest",
    metadata: str | None = None,
    filter: str | None = None,
) -> int:
    """Count all examples by query params"""
    count = await crud.count_examples_json(
        share,
        schemas.FilterQueryParamsForCountExampleSchema(
            id=id,
            as_of=as_of,
            metadata=metadata,
            filter=filter,
        ),
    )
    return Response(
        orjson.dumps(count),
        media_type="application/json",
    )


@router.get("/{share_token}/examples", response_model=List[schemas.Example])
@audit_operation_name("read_shared_examples")
async def read_shared_examples(
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    id: Annotated[list[UUID] | None, Query()] = None,
    as_of: schemas.AsOfType = "latest",
    metadata: str | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    select: Annotated[tuple[schemas.ExampleSelect, ...], Query()] = tuple(
        e for e in schemas.ExampleSelect if e != schemas.ExampleSelect.source_run_id
    ),
    filter: str | None = None,
):
    """Get example by ids or the shared example if not specifed."""
    rows, total = await crud.get_examples_json(
        share,
        schemas.FilterQueryParamsForExampleSchema.model_construct(
            id=id,
            as_of=as_of,
            metadata=metadata,
            offset=offset,
            limit=limit,
            select=select,
            filter=filter,
        ),
    )
    return Response(
        orjson.dumps([orjson.Fragment(row) for row in rows]),
        media_type="application/json",
        headers={"X-Pagination-Total": str(total)},
    )


@router.get(
    "/{share_token}/datasets/sessions", response_model=List[schemas.TracerSession]
)
@audit_operation_name("read_shared_dataset_tracing_projects")
async def read_shared_dataset_tracer_sessions(
    response: Response,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    accept: Annotated[str | None, Header()] = None,
    id: Annotated[list[UUID] | None, Query()] = None,
    name: str | None = None,
    name_contains: str | None = None,
    dataset_version: str | None = None,
    sort_by: schemas.SessionSortableColumns = schemas.SessionSortableColumns.START_TIME,
    sort_by_desc: bool = True,
    sort_by_feedback_key: str | None = None,
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    facets: bool = False,
):
    """Get projects run on a dataset that has been shared."""
    query_params = schemas.FilterQueryParamsForTracerSessionSchema(
        id=id,
        name=name,
        name_contains=name_contains,
        dataset_version=dataset_version,
        sort_by=sort_by,
        sort_by_desc=sort_by_desc,
        sort_by_feedback_key=sort_by_feedback_key,
        offset=offset,
        limit=limit,
        facets=facets,
        reference_dataset=[share.dataset_id],
    )

    if accept == "text/event-stream":
        return EventSourceResponse(
            jsonpatch_sse_stream(
                models.tracer_sessions.stream.stream_sessions(share, query_params)
            )
        )

    rows, total = await crud.get_tracer_sessions(share, query_params)
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get(
    "/datasets/sessions-bulk",
    response_model=List[schemas.TracerSession],
)
@audit_operation_name("read_shared_dataset_tracing_projects")
async def read_shared_dataset_tracer_sessions_bulk(
    share_tokens: Annotated[list[str], Query()],
):
    """Get sessions from multiple datasets using share tokens."""
    rows: list[schemas.TracerSession] = []
    for share_token in share_tokens:
        share = await deps.get_share_dataset_info(share_token)
        query_params = schemas.FilterQueryParamsForTracerSessionSchema()
        sessions, total_sessions = await crud.get_tracer_sessions(share, query_params)
        rows.extend(sessions)
    return rows


@router.post(
    "/{share_token}/examples/runs",
    response_model=List[schemas.PublicExampleWithRuns]
    | List[schemas.ExampleWithRunsCH],
)
@audit_operation_name("read_shared_dataset_examples_with_runs")
async def read_shared_dataset_examples_with_runs(
    response: Response,
    schema: schemas.QueryExampleSchemaWithRuns,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
):
    """Get examples with associated runs from sessions in a dataset that has been shared."""
    rows, total = await crud.get_example_runs_from_session_ch(
        share, share.dataset_id, schema
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post(
    "/{share_token}/datasets/runs/delta",
    response_model=schemas.SessionFeedbackDelta,
)
async def read_shared_delta(
    response: Response,
    schema: schemas.QueryFeedbackDelta,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
) -> schemas.SessionFeedbackDelta:
    """Fetch the number of regressions/improvements for each example in a dataset, between sessions[0] and sessions[1]."""
    rows, total = await models.runs.fetch_ch.fetch_feedback_delta(
        share, share.dataset_id, schema
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.post(
    "/{share_token}/datasets/runs/query",
    response_model=schemas.ListPublicDatasetRunsResponse,
)
@audit_operation_name("read_shared_dataset_runs")
async def query_shared_dataset_runs(
    body: schemas.BodyParamsForRunSchema,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
):
    """Get runs in projects run over a dataset that has been shared."""
    return await models.runs.fetch_ch.fetch_runs(share, body)


@router.post(
    "/{share_token}/datasets/runs/generate-query",
    response_model=schemas.ResponseBodyForRunsGenerateQuery,
)
async def generate_query_for_shared_dataset_runs(
    body: schemas.RequestBodyForRunsGenerateQuery,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
):
    """Get runs in projects run over a dataset that has been shared."""
    return await models.runs.generate_filter.generate_runs_filter(share, body)


@router.post(
    "/{share_token}/datasets/runs/stats",
    response_model=schemas.RunStats,
)
@audit_operation_name("read_shared_dataset_stats")
async def stats_shared_dataset_runs(
    body: schemas.RunStatsQueryParams,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
):
    """Get run stats in projects run over a dataset that has been shared."""
    return await models.runs.stats.run_stats(share, body)


@router.get(
    "/{share_token}/datasets/runs/{run_id}",
    response_model=schemas.RunPublicDatasetSchema,
)
@audit_operation_name("read_shared_dataset_run")
async def read_shared_dataset_run(
    run_id: UUID,
    exclude_s3_stored_attributes: bool = False,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
):
    """Get runs in projects run over a dataset that has been shared."""
    result = await crud.get_run(
        share,
        run_id,
        exclude_select=[schemas.RunSelect.inputs, schemas.RunSelect.outputs]
        if exclude_s3_stored_attributes
        else [],
    )
    return result


@router.get(
    "/{share_token}/datasets/feedback", response_model=List[schemas.FeedbackSchema]
)
@audit_operation_name("read_shared_dataset_feedback")
async def read_shared_dataset_feedback(
    response: Response,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    run: Annotated[list[UUID] | None, Query()] = None,
    key: Annotated[list[str] | None, Query()] = None,
    session: Annotated[list[UUID] | None, Query()] = None,
    source: Annotated[list[schemas.SourceType] | None, Query()] = None,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    offset: Annotated[int, Query(ge=0)] = 0,
    user: Annotated[list[UUID] | None, Query()] = None,
    has_comment: bool | None = None,
    has_score: bool | None = None,
    level: schemas.FeedbackLevel | None = None,
):
    """Get feedback for runs in projects run over a dataset that has been shared."""
    # Phew that's a mouthful
    use_pg = settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL or (
        share.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    )
    if use_pg:
        rows, total = await fetch_feedbacks_postgres(
            share,
            schemas.QueryParamsForFeedbackSchema(
                run=run,
                key=key,
                session=session,
                source=source,
                limit=limit,
                offset=offset,
                user=user,
                has_comment=has_comment,
                has_score=has_score,
                level=level,
            ),
        )
        response.headers["X-Pagination-Total"] = str(total)
        return rows

    rows, total = await fetch_feedbacks(
        share,
        schemas.QueryParamsForFeedbackSchema(
            run=run,
            key=key,
            session=session,
            source=source,
            limit=limit,
            offset=offset,
            user=user,
            has_comment=has_comment,
            has_score=has_score,
            level=level,
        ),
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get(
    "/{share_token}/datasets/comparative",
    response_model=List[schemas.PublicComparativeExperiment],
)
@audit_operation_name("read_shared_comparative_experiments")
async def read_shared_comparative_experiments(
    response: Response,
    share: auth.ShareDatasetInfo = Depends(deps.get_share_dataset_info),
    name: str | None = None,
    name_contains: str
    | None = None,  # search by comparative experiment or tracer session name
    offset: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    sort_by: schemas.SortByComparativeExperimentColumn = schemas.SortByComparativeExperimentColumn.created_at,
    sort_by_desc: bool = True,
) -> List[schemas.PublicComparativeExperiment]:
    """Get all comparative experiments for a given dataset."""
    query_params = schemas.QueryParamsForComparativeExperimentsSchema(
        limit=limit,
        offset=offset,
        name=name,
        name_contains=name_contains,
        sort_by=sort_by,
        sort_by_desc=sort_by_desc,
    )

    rows, total = await models.datasets.comparative.get_comparative_experiments(
        share, query_params
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


@router.get("/schemas/{version}/message.json")
async def get_message_json_schema(version: str) -> ORJSONResponse:
    message_fmt = get_message_format(version)
    return ORJSONResponse(message_fmt)


@router.get("/schemas/{version}/tooldef.json")
async def get_tool_def_json_schema(version: str) -> ORJSONResponse:
    tool_def_fmt = get_tool_format(version)
    return ORJSONResponse(tool_def_fmt)
