"""Endpoints for Feedback."""

import datetime
from typing import Annotated, List, Sequence
from uuid import UUID

from fastapi import Depends, Query, Response
from lc_database import api_router, redis
from lc_logging.audit_logs import audit_operation_name

from app import crud, models, schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.models.feedback.fetch import (
    fetch_feedback,
    fetch_feedbacks,
)

router = api_router.TrailingSlashRouter()


async def get_use_select_sequential_consistency(
    tenant_id: UUID, run: list[UUID] | None
):
    if run is None or len(run) == 0:
        return True
    use_select_sequential_consistency = False
    async with redis.aredis_caching_pool() as aredis, aredis.pipeline() as pipe:
        # unsharded on purpose
        for r in run:
            res = pipe.get(f"smith:feedback_update:{tenant_id}:{r}")
        results = await pipe.execute()
        for res in results:
            if res is not None and res.decode("utf-8") == "true":
                use_select_sequential_consistency = True
                break
    return use_select_sequential_consistency


@router.get("/{feedback_id}", response_model=schemas.FeedbackSchema)
@audit_operation_name("read_feedback")
async def read_feedback(
    feedback_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_READ)),
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema:
    """Get a specific feedback."""
    return await fetch_feedback(
        feedback_id, auth, include_user_names=include_user_names
    )


@router.get("", response_model=List[schemas.FeedbackSchema])
@audit_operation_name("read_feedbacks")
async def read_feedbacks(
    response: Response,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_READ)),
    run: Annotated[list[UUID] | None, Query()] = None,
    key: Annotated[list[str] | None, Query()] = None,
    session: Annotated[list[UUID] | None, Query()] = None,
    source: Annotated[list[schemas.SourceType] | None, Query()] = None,
    limit: Annotated[int, Query(ge=1, le=100)] = 100,
    offset: Annotated[int, Query(ge=0)] = 0,
    user: Annotated[list[UUID] | None, Query()] = None,
    has_comment: bool | None = None,
    has_score: bool | None = None,
    level: schemas.FeedbackLevel | None = None,
    max_created_at: datetime.datetime | None = None,
    min_created_at: datetime.datetime | None = None,
    include_user_names: bool | None = None,
) -> Sequence[schemas.FeedbackSchema]:
    """List all Feedback by query params."""
    use_select_sequential_consistency = await get_use_select_sequential_consistency(
        auth.tenant_id, run
    )
    rows, total = await fetch_feedbacks(
        auth,
        schemas.QueryParamsForFeedbackSchema(
            run=run,
            key=key,
            session=session,
            source=source,
            limit=limit,
            offset=offset,
            user=user,
            has_comment=has_comment,
            has_score=has_score,
            level=level,
            max_created_at=max_created_at,
            min_created_at=min_created_at,
            include_user_names=include_user_names,
            use_select_sequential_consistency=use_select_sequential_consistency,
        ),
    )
    response.headers["X-Pagination-Total"] = str(total)
    return rows


# TODO remove response payload once SDK is updated
@router.post("", response_model=schemas.FeedbackSchema)
@audit_operation_name("create_feedback")
async def create_feedback(
    response: Response,
    feedback: schemas.FeedbackCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_CREATE)),
):
    """Create a new feedback."""
    # Returns 202 if the feedback is queued, 200 if created synchronously
    status = await crud.create_feedback(
        auth,
        schemas.FeedbackCreateSchemaInternal(
            **feedback.model_dump(),
            extra={"error": feedback.error if feedback.error is not None else False},
        ),
    )
    response.status_code = status
    return feedback


# TODO remove this endpoint once SDK is updated
@router.post("/eager", response_model=schemas.FeedbackSchema)
@audit_operation_name("create_feedback")
async def eagerly_create_feedback(
    response: Response,
    feedback: schemas.FeedbackCreateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_CREATE)),
):
    """Create a new feedback.

    This method is invoked under the assumption that the run
    is already visible in the app, thus already present in DB
    """
    status = await crud.create_feedback(
        auth, schemas.FeedbackCreateSchemaInternal(**feedback.model_dump())
    )
    response.status_code = status
    return feedback


@router.patch("/{feedback_id}", response_model=schemas.FeedbackSchema)
@audit_operation_name("update_feedback")
async def update_feedback(
    feedback_id: UUID,
    feedback: schemas.FeedbackUpdateSchema,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_UPDATE)),
):
    """Replace an existing feedback entry with a new, modified entry."""
    return await crud.update_feedback_ch(auth, feedback_id, feedback)


@router.delete("/{feedback_id}")
@audit_operation_name("delete_feedback")
async def delete_feedback(
    feedback_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_DELETE)),
):
    """Delete a feedback."""
    await models.feedback.delete.delete_feedback([feedback_id], auth)
    return {"message": "Feedback deleted"}


@router.post(
    "/tokens",
    response_model=schemas.FeedbackIngestTokenSchema
    | list[schemas.FeedbackIngestTokenSchema],
)
@audit_operation_name("create_feedback_ingest_token")
async def create_feedback_ingest_token(
    feedback_ingest_token: schemas.FeedbackIngestTokenCreateSchema
    | list[schemas.FeedbackIngestTokenCreateSchema],
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_CREATE)),
):
    """Create a new feedback ingest token."""
    if isinstance(feedback_ingest_token, list):
        return await models.feedback.tokens.create_ingest_tokens(
            auth, feedback_ingest_token
        )
    else:
        return await models.feedback.tokens.create_ingest_token(
            auth, feedback_ingest_token
        )


@router.get("/tokens", response_model=List[schemas.FeedbackIngestTokenSchema])
@audit_operation_name("read_feedback_ingest_tokens")
async def list_feedback_ingest_tokens(
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.FEEDBACK_CREATE)),
):
    """List all feedback ingest tokens for a run."""
    return await models.feedback.tokens.list_ingest_tokens(auth, run_id)


@router.get("/tokens/{token}")
@audit_operation_name("create_feedback_with_token")
async def create_feedback_with_token_get(
    response: Response,
    auth: deps.FeedbackTokenInfo = Depends(deps.get_feedback_token_info),
    score: float | int | bool | None = None,
    value: float | int | bool | str | None = None,
    comment: str | None = None,
    correction: str | None = None,
):
    """Create a new feedback with a token."""
    status = await crud.create_feedback(
        auth,
        schemas.FeedbackCreateSchemaInternal(
            run_id=auth.run_id,
            key=auth.feedback_key,
            score=score,
            comment=comment,
            value=value,
            correction=correction,
            feedback_source=schemas.APIFeedbackSource(),
        ),
    )
    response.status_code = status


@router.post("/tokens/{token}")
@audit_operation_name("create_feedback_with_token")
async def create_feedback_with_token_post(
    response: Response,
    payload: schemas.FeedbackCreateWithTokenExtendedSchema,
    auth: deps.FeedbackTokenInfo = Depends(deps.get_feedback_token_info),
):
    """Create a new feedback with a token."""
    status = await crud.create_feedback(
        auth,
        schemas.FeedbackCreateSchemaInternal(
            run_id=auth.run_id,
            key=auth.feedback_key,
            score=payload.score,
            comment=payload.comment,
            value=payload.value,
            correction=payload.correction,
            feedback_source=schemas.APIFeedbackSource(metadata=payload.metadata),
        ),
    )
    response.status_code = status
