from uuid import UUID

import orjson
from lc_config.settings import shared_settings as settings
from lc_database.database import asyncpg_conn
from lc_database.queue.serializer import ORJSONSerializer

from app.api.auth.schemas import AuthInfo
from app.models.feedback.fetch import fetch_feedback, fetch_feedback_postgres
from app.models.feedback.ingest import FeedbackInsert, upsert_feedback


async def delete_feedback(
    ids: list[UUID],
    auth: AuthInfo,
) -> None:
    use_pg = settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL or (
        auth.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    )
    if use_pg:
        existing = await fetch_feedback_postgres(ids, auth)
    else:
        existing = await fetch_feedback(ids, auth)

    await upsert_feedback(
        [
            FeedbackInsert(
                payload=orjson.loads(ORJSONSerializer.dumps(row)),
                trace_id=row.trace_id,  # type: ignore
                session_id=row.session_id,
                start_time=row.start_time,  # type: ignore
                redis=None,
                delete=True,
            )
            for row in existing
        ],
        auth,
    )

    if use_pg:
        async with asyncpg_conn() as conn:
            await conn.execute(
                """
                DELETE FROM feedbacks
                WHERE id = ANY($1::uuid[]) AND tenant_id = $2
                """,
                ids,
                auth.tenant_id,
            )
