import uuid
from collections import defaultdict
from datetime import datetime, timezone
from typing import Any, Sequence, overload
from uuid import UUID

import orjson
from aiochclient import Record
from asyncpg.pgproto.pgproto import UUID as PGUUID
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database import clickhouse
from lc_database.clickhouse import ClickhouseClient
from lc_database.database import asyncpg_conn, asyncpg_pool, kwargs_to_pgpos

from app import schemas
from app.api.auth.schemas import AuthInfo, ShareDatasetInfo, ShareRunInfo
from app.models.identities.users import get_linked_login_methods_in_txn
from app.retry import retry_asyncpg, retry_clickhouse_read
from app.utils import load_json, try_load_json

NULL_UUID = UUID("00000000-0000-0000-0000-000000000000")

ch_fetch_cols_list = [
    "id",
    "session_id",
    "created_at",
    "modified_at",
    "start_time",
    "is_root",
    "run_id",
    "key",
    "score",
    "value",
    "comment",
    "correction",
    "trace_id",
    "feedback_source",
    "comparative_experiment_id",
    "feedback_group_id",
    "extra",
]

all_cols_list = ch_fetch_cols_list + [
    "tenant_id",
    "is_deleted",
]


def should_use_pg_for_feedback_fetch(auth: AuthInfo) -> bool:
    return (
        settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL
        or auth.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    )


@overload
async def fetch_feedback_postgres(
    id: UUID,
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema: ...


@overload
async def fetch_feedback_postgres(
    id: list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> list[schemas.FeedbackSchema]: ...


@retry_asyncpg
async def fetch_feedback_postgres(
    id: UUID | list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema | list[schemas.FeedbackSchema]:
    ids = id if isinstance(id, list) else [id]
    async with asyncpg_pool() as pg:
        rows = await pg.fetch(
            """
            SELECT * FROM feedbacks
            WHERE tenant_id = $1 AND id = ANY($2)
            """,
            auth.tenant_id,
            ids,
        )
        if len(rows) != len(ids):
            raise HTTPException(status_code=404, detail="Feedback not found")
        else:
            feedbacks = [map_feedback(row) for row in rows]
            if include_user_names:
                feedbacks = await enrich_feedbacks_with_user_names(feedbacks)
            feedback_schemas = [schemas.FeedbackSchema(**f) for f in feedbacks]
            return feedback_schemas if isinstance(id, list) else feedback_schemas[0]


@overload
async def fetch_feedback_clickhouse(
    id: UUID,
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema: ...


@overload
async def fetch_feedback_clickhouse(
    id: list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> list[schemas.FeedbackSchema]: ...


@retry_clickhouse_read
async def fetch_feedback_clickhouse(
    id: UUID | list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema | list[schemas.FeedbackSchema]:
    ids = id if isinstance(id, list) else [id]
    async with clickhouse.clickhouse_client(
        clickhouse.ClickhouseClient.USER_QUERIES
    ) as ch:
        cols = get_argmax_feedback_columns(ch_fetch_cols_list)
        rows = await ch.fetch(
            "fetch_feedbacks",
            f"SELECT {cols} FROM feedbacks_rmt_id "
            + """
            WHERE tenant_id = {tenant_id} AND id IN {ids}
            GROUP BY id
            HAVING argMax(is_deleted, modified_at) = 0
            """,
            params={"tenant_id": auth.tenant_id, "ids": ids},
        )
        if len(rows) != len(ids):
            raise HTTPException(status_code=404, detail="Feedback not found")
        else:
            rows = [
                {
                    **row,
                    "value": orjson.loads(row["value"]),
                    "correction": orjson.loads(row["correction"]),
                    "feedback_source": orjson.loads(row["feedback_source"]),
                    "extra": orjson.loads(row["extra"]) if row.get("extra") else {},
                }
                for row in rows
            ]
            if include_user_names:
                rows = await enrich_feedbacks_with_user_names(rows)
            feedback_schemas = [schemas.FeedbackSchema(**f) for f in rows]
            return feedback_schemas if isinstance(id, list) else feedback_schemas[0]


@overload
async def fetch_feedback(
    id: UUID,
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema: ...


@overload
async def fetch_feedback(
    id: list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> list[schemas.FeedbackSchema]: ...


async def fetch_feedback(
    id: UUID | list[UUID],
    auth: AuthInfo,
    include_user_names: bool | None = None,
) -> schemas.FeedbackSchema | list[schemas.FeedbackSchema]:
    """Parent function that routes to PostgreSQL or ClickHouse based on feature flag."""
    if should_use_pg_for_feedback_fetch(auth):
        return await fetch_feedback_postgres(id, auth, include_user_names)
    else:
        return await fetch_feedback_clickhouse(id, auth, include_user_names)


@retry_asyncpg
async def fetch_feedbacks_postgres(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForFeedbackSchema,
) -> tuple[Sequence[schemas.FeedbackSchema], int]:
    filters, params = await _build_postgres_feedback_filters(auth, query_params)

    if query_params.limit:
        sql = f"""
        SELECT *
        FROM feedbacks
        WHERE {" AND ".join(filters)}
        ORDER BY created_at DESC
        LIMIT $limit
        OFFSET $offset
        """
        params.update(limit=query_params.limit + 1, offset=query_params.offset)
    else:
        # This code path is only called from non-API callers
        sql = f"""
        SELECT *
        FROM feedbacks
        WHERE {" AND ".join(filters)}
        ORDER BY created_at DESC
        """

    sql_query_pos = kwargs_to_pgpos(sql, params)
    async with asyncpg_conn() as pg:
        feedback_rows = await pg.fetch(sql_query_pos.sql, *sql_query_pos.args)

    raw_count = len(feedback_rows)

    if query_params.limit:
        feedback_rows = feedback_rows[: query_params.limit]

    feedbacks = [map_feedback(f) for f in feedback_rows]

    if isinstance(auth, (ShareRunInfo, ShareDatasetInfo)):
        for feedback in feedbacks:
            if (
                feedback.get("feedback_source") is not None
                and "metadata" in feedback["feedback_source"]
            ):
                del feedback["feedback_source"]["metadata"]
    elif query_params.include_user_names:
        # Enrich with user_name if requested, but not for public feedbacks
        feedbacks = await enrich_feedbacks_with_user_names(feedbacks)

    return [schemas.FeedbackSchema(**f) for f in feedbacks], raw_count


@retry_clickhouse_read
async def fetch_feedbacks_clickhouse(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForFeedbackSchema,
) -> tuple[Sequence[schemas.FeedbackSchema], int]:
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        filters, params = await _build_feedback_filters(auth, query_params)

        if query_params.limit:
            query_name = "fetch_feedbacks_rmt"
            feedbacks_query = f"""
            SELECT *
            FROM feedbacks_rmt FINAL
            WHERE {" AND ".join(filters)}
            ORDER BY created_at DESC
            LIMIT {{limit}}
            OFFSET {{offset}}
            {"SETTINGS select_sequential_consistency = 1" if query_params.use_select_sequential_consistency else ""}
            """
        else:
            query_name = "fetch_feedbacks"
            feedbacks_query = f"""
            WITH ordered as (
                SELECT *
                FROM feedbacks
                WHERE {" AND ".join(filters)}
                ORDER BY id DESC, modified_at DESC
                LIMIT 1 BY id
                {"SETTINGS select_sequential_consistency = 1" if query_params.use_select_sequential_consistency else ""}
            )

            SELECT *
            FROM ordered
            WHERE is_deleted = 0
            ORDER BY created_at DESC
            """

        params["offset"] = query_params.offset
        params["limit"] = query_params.limit + 1

        feedback_records = await ch.fetch(query_name, feedbacks_query, params=params)
        raw_count = len(feedback_records)

    if query_params.limit:
        feedback_records = feedback_records[: query_params.limit]

    feedbacks = [map_feedback(f) for f in feedback_records]

    if isinstance(auth, (ShareRunInfo, ShareDatasetInfo)):
        for feedback in feedbacks:
            if (
                feedback.get("feedback_source") is not None
                and "metadata" in feedback["feedback_source"]
            ):
                del feedback["feedback_source"]["metadata"]
    elif query_params.include_user_names:
        # Enrich with user_name if requested, but not for public feedbacks
        feedbacks = await enrich_feedbacks_with_user_names(feedbacks)

    return [schemas.FeedbackSchema(**f) for f in feedbacks], raw_count


async def fetch_feedbacks(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForFeedbackSchema,
) -> tuple[Sequence[schemas.FeedbackSchema], int]:
    """Parent function that routes to PostgreSQL or ClickHouse based on feature flag."""
    if should_use_pg_for_feedback_fetch(auth):
        return await fetch_feedbacks_postgres(auth, query_params)
    else:
        return await fetch_feedbacks_clickhouse(auth, query_params)


def get_argmax_feedback_columns(ch_fetch_cols_list):
    return ", ".join(
        [
            col
            if col in ("id",)
            else "max(modified_at)"
            if col == "modified_at"
            else f"argMax({col}, modified_at) as {col}"
            for col in ch_fetch_cols_list
        ]
    )


def _to_utc_naive(dt: datetime | None) -> datetime | None:
    if dt is None or dt.tzinfo is None:
        return dt
    return dt.astimezone(timezone.utc).replace(tzinfo=None)


def map_feedback(feedback: Record) -> dict:
    # convert pg UUID to stdlib
    def _cast_uuid(v):
        return uuid.UUID(str(v)) if isinstance(v, PGUUID) else v

    base = {k: _cast_uuid(v) for k, v in feedback.items()}

    return {
        **base,
        "run_id": None if base["run_id"] == NULL_UUID else base["run_id"],
        "trace_id": None if base["trace_id"] == NULL_UUID else base["trace_id"],
        "start_time": _to_utc_naive(base.get("start_time")),
        "created_at": _to_utc_naive(base["created_at"]),
        "modified_at": _to_utc_naive(base["modified_at"]),
        "value": try_load_json(base["value"]),
        "correction": try_load_json(base["correction"])
        if base.get("correction")
        else None,
        "feedback_source": load_json(base["feedback_source"])
        if base.get("feedback_source")
        else None,
        "extra": load_json(base["extra"]) if base.get("extra") else None,
    }


def _opt_user_id(feedback: dict) -> UUID | None:
    # handles the case where feedback_source is present and None
    return feedback.get("user_id") or (feedback.get("feedback_source") or {}).get(
        "user_id"
    )


async def enrich_feedbacks_with_user_names(feedbacks: list[dict]) -> list[dict]:
    user_ids = [_opt_user_id(f) for f in feedbacks]
    user_ids_query: list[UUID] = [uid for uid in user_ids if uid is not None]

    if not user_ids_query:
        return feedbacks

    async with asyncpg_conn() as db:
        provider_users = await get_linked_login_methods_in_txn(
            db,
            user_ids_query,
        )
        # Basic auth and none auth don't have provider_user_id, so do not enrich
        pus_safe = [p for p in provider_users if p.provider_user_id is not None]
        if not pus_safe:
            return feedbacks

        # To enable searching linked login methods by user_id
        ls_user_id_by_user_id = {
            str(pu.provider_user_id): pu.ls_user_id for pu in pus_safe
        }
        pu_by_id: dict[UUID, list[schemas.ProviderUserSlim]] = defaultdict(list)
        for provider_user in pus_safe:
            pu_by_id[provider_user.ls_user_id].append(provider_user)

        for feedback in feedbacks:
            if user_id := _opt_user_id(feedback):
                user_id_str = str(user_id)
                ls_user_id = ls_user_id_by_user_id.get(user_id_str)
                if ls_user_id is None:
                    continue
                matching_login_methods = pu_by_id.get(ls_user_id)
                if matching_login_methods is None:
                    continue

                # First try to find login method with matching provider_user_id
                matching_pu = next(
                    (
                        pu
                        for pu in matching_login_methods
                        if str(pu.provider_user_id) == user_id_str
                    ),
                    None,
                )
                # Fall back to any linked login method if no direct match
                user_name = (
                    matching_pu.full_name
                    if matching_pu
                    else next(
                        (pu.full_name for pu in matching_login_methods if pu.full_name),
                        None,
                    )
                )
                feedback["feedback_source"]["user_name"] = user_name
    return feedbacks


async def _build_feedback_filters(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForFeedbackSchema,
) -> tuple[list[Any], dict[str, Any]]:
    filters: list[Any] = []
    params: dict[str, Any] = {}
    if isinstance(auth, AuthInfo):
        filters.append("tenant_id = {tenant_id}")
        params["tenant_id"] = auth.tenant_id
    elif isinstance(auth, ShareRunInfo):
        filters.append(
            "tenant_id = {tenant_id} AND session_id = {session_id} AND run_id IN({allowed_run_ids}) "
        )
        params["allowed_run_ids"] = auth.allowed_run_ids
        params["tenant_id"] = auth.tenant_id
        params["session_id"] = auth.session_id
    elif isinstance(auth, ShareDatasetInfo):
        # TODO: Only permit sharing of certain projects run over a dataset + other restrictions
        # Or would be cheaper if we included in the joins table
        async with asyncpg_pool() as pool:
            rows = await pool.fetch(
                "select id from tracer_session where reference_dataset_id = $1",
                auth.dataset_id,
            )
        tracer_session_ids = [str(row.id) for row in rows]
        filters.append("tenant_id={tenant_id} and session_id IN({tracer_session_ids})")
        params["tracer_session_ids"] = tracer_session_ids
        params["tenant_id"] = auth.tenant_id
    else:
        raise ValueError(f"Invalid auth type: {type(auth)}")

    str_uids = [str(uid) for uid in query_params.user] if query_params.user else []
    if query_params.run:
        filters.append("run_id IN {run}")
        params["run"] = query_params.run
    if query_params.session:
        filters.append("session_id IN {session}")
        params["session"] = query_params.session
    if query_params.key:
        filters.append("key IN {key}")
        params["key"] = query_params.key
    if query_params.source:
        filters.append("JSONExtractString(feedback_source, 'type') IN ({source})")
        params["source"] = [s.value for s in query_params.source]
    if query_params.user:
        filters.append("JSONExtractString(feedback_source, 'user_id') IN ({str_uids})")
        params["str_uids"] = str_uids
    if query_params.has_comment is not None:
        if query_params.has_comment:
            filters.append("NOT empty(comment)")
        else:
            filters.append("empty(comment)")
    if query_params.has_score is not None:
        if query_params.has_score:
            filters.append("score IS NOT NULL")
        else:
            filters.append("score IS NULL")
    if query_params.level == schemas.FeedbackLevel.session:
        if not query_params.session:
            raise HTTPException(
                status_code=400, detail="session_id is required for session level"
            )
        filters.append("is_root = 1")
        filters.append("run_id = defaultValueOfArgumentType(run_id)")
        filters.append("trace_id = defaultValueOfArgumentType(trace_id)")
        filters.append("start_time = defaultValueOfArgumentType(start_time)")
    elif query_params.level == schemas.FeedbackLevel.run:
        filters.append("run_id != defaultValueOfArgumentType(run_id)")
    if query_params.max_created_at:
        filters.append("created_at <= {max_created_at}")
        params["max_created_at"] = query_params.max_created_at.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        )
    if query_params.min_created_at:
        filters.append("created_at >= {min_created_at}")
        params["min_created_at"] = query_params.min_created_at.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        )
    return (filters, params)


async def _build_postgres_feedback_filters(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo,
    query_params: schemas.QueryParamsForFeedbackSchema,
) -> tuple[list[str], dict[str, object]]:
    """
    Return:
        filters : list[str]   – ready to be joined with ' AND '
        params  : dict[str, Any] – bind-values keyed by the same names
    """
    filters: list[str] = []
    params: dict[str, object] = {}

    if isinstance(auth, AuthInfo):
        filters.append("tenant_id = $tenant_id")
        params["tenant_id"] = auth.tenant_id
    elif isinstance(auth, ShareRunInfo):
        filters.append(
            "tenant_id = $tenant_id "
            "AND session_id = $session_id "
            "AND run_id  = ANY($allowed_run_ids)"
        )
        params.update(
            tenant_id=auth.tenant_id,
            session_id=auth.session_id,
            allowed_run_ids=list(auth.allowed_run_ids),
        )
    elif isinstance(auth, ShareDatasetInfo):
        async with asyncpg_pool() as pool:
            rows = await pool.fetch(
                "select id from tracer_session where reference_dataset_id = $1",
                auth.dataset_id,
            )
        tracer_session_ids = [str(row.id) for row in rows]

        filters.append(
            "tenant_id = $tenant_id AND session_id = ANY($tracer_session_ids)"
        )
        params.update(
            tenant_id=auth.tenant_id,
            tracer_session_ids=tracer_session_ids,
        )
    else:
        raise ValueError(f"Invalid auth type: {type(auth)}")

    if query_params.run:
        filters.append("run_id = ANY($run_ids)")
        params["run_ids"] = query_params.run

    if query_params.session:
        filters.append("session_id = ANY($session_ids)")
        params["session_ids"] = query_params.session

    if query_params.key:
        filters.append('"key" = ANY($keys)')
        params["keys"] = query_params.key

    if query_params.source:
        filters.append("(feedback_source->>'type') = ANY($sources)")
        params["sources"] = [s.value for s in query_params.source]

    if query_params.user:
        filters.append("user_id = ANY($user_ids)")
        params["user_ids"] = query_params.user

    if query_params.has_comment is not None:
        if query_params.has_comment:
            filters.append("(comment IS NOT NULL AND comment <> '')")
        else:
            filters.append("(comment IS NULL OR comment = '')")

    if query_params.has_score is not None:
        filters.append(
            "score IS NOT NULL" if query_params.has_score else "score IS NULL"
        )

    if query_params.level == schemas.FeedbackLevel.session:
        if not query_params.session:
            raise HTTPException(
                status_code=400, detail="session_id is required for session level"
            )
        filters.extend(
            [
                "run_id IS NULL",
                "trace_id IS NULL",
                "start_time IS NULL",
            ]
        )

    elif query_params.level == schemas.FeedbackLevel.run:
        filters.append("run_id IS NOT NULL")

    if query_params.max_created_at:
        filters.append("created_at <= $max_created_at")
        params["max_created_at"] = query_params.max_created_at

    if query_params.min_created_at:
        filters.append("created_at >= $min_created_at")
        params["min_created_at"] = query_params.min_created_at

    return filters, params
