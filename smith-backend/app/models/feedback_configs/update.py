from datetime import datetime, timezone
from uuid import UUID

import or<PERSON><PERSON>
from fastapi import HTTP<PERSON>x<PERSON>
from lc_database import clickhouse
from lc_database.clickhouse import ClickhouseClient
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth import AuthInfo
from app.models.feedback.utils import normalize_feedback_key
from app.models.feedback_configs.ingest import (
    is_feedback_configs_produce_enabled,
    is_feedback_configs_write_and_fetch_enabled,
    is_postgres_only_ingest_skip_ch_enabled,
)
from app.retry import retry_clickhouse


@retry_clickhouse
async def update_feedback_config(
    auth: AuthInfo, payload: schemas.UpdateFeedbackConfigSchema
) -> schemas.FeedbackConfigSchema:
    async with clickhouse.clickhouse_client(ClickhouseClient.INGESTION) as ch:
        existing_config = await ch.fetchrow(
            "fetch_feedback_config",
            """
            SELECT * FROM feedback_configs FINAL
            WHERE tenant_id = {tenant_id} AND feedback_key = {feedback_key} AND is_deleted = 0
            """,
            params={
                "tenant_id": UUID(auth.tenant_id.hex),
                "feedback_key": normalize_feedback_key(payload.feedback_key),
            },
        )

        if not existing_config:
            raise HTTPException(status_code=404, detail="Feedback config not found")

    feedback_config_str = (
        orjson.dumps(payload.feedback_config.model_dump()).decode("utf-8")
        if payload.feedback_config
        else existing_config["feedback_config"]
    )

    modified_at = datetime.now(timezone.utc)
    params = {
        "tenant_id": UUID(auth.tenant_id.hex),
        "feedback_key": normalize_feedback_key(payload.feedback_key),
        "is_lower_score_better": payload.is_lower_score_better
        if payload.is_lower_score_better is not None
        else existing_config["is_lower_score_better"],
        "feedback_config": feedback_config_str,
        "modified_at": modified_at.strftime("%Y-%m-%d %H:%M:%S.%f"),
    }

    insert_query = """
    INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, modified_at, is_deleted, is_lower_score_better)
    SETTINGS async_insert = 0
    SELECT tenant_id, feedback_key, {feedback_config}, {modified_at}, is_deleted, {is_lower_score_better}
    FROM feedback_configs
    WHERE tenant_id = {tenant_id} AND feedback_key = {feedback_key} AND is_deleted = 0
    """

    if is_feedback_configs_write_and_fetch_enabled(auth):
        insert_query = """
        INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, modified_at, is_deleted, is_lower_score_better)
        SETTINGS async_insert=1
        SELECT tenant_id, feedback_key, {feedback_config}, {modified_at}, is_deleted, {is_lower_score_better}
        FROM feedback_configs
        WHERE tenant_id = {tenant_id} AND feedback_key = {feedback_key} AND is_deleted = 0
        """

    skip_ch_write = is_postgres_only_ingest_skip_ch_enabled(auth)

    if not skip_ch_write:
        await clickhouse.multi_execute_single(
            clickhouse.ExecuteRequest(
                "upsert_feedback_config", insert_query, params=params
            ),
        )

    if is_feedback_configs_produce_enabled(auth):
        await _upsert_feedback_config_to_postgres(auth, params)

    return schemas.FeedbackConfigSchema(
        feedback_key=normalize_feedback_key(payload.feedback_key),
        feedback_config=schemas.FeedbackConfig.model_validate(
            orjson.loads(feedback_config_str)
        ),
        is_lower_score_better=payload.is_lower_score_better,
        tenant_id=auth.tenant_id,
        modified_at=modified_at,
    )


async def _upsert_feedback_config_to_postgres(auth: AuthInfo, params: dict):
    async with asyncpg_conn() as conn, conn.transaction():
        await conn.execute(
            """
            INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, is_lower_score_better)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (tenant_id, feedback_key) DO UPDATE
            SET feedback_config = EXCLUDED.feedback_config,
                is_lower_score_better = EXCLUDED.is_lower_score_better,
                modified_at = NOW();
            """,
            params["tenant_id"],
            params["feedback_key"],
            params["feedback_config"],
            params["is_lower_score_better"],
        )
