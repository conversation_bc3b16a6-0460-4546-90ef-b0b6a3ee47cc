import uuid
from collections import defaultdict
from typing import Any

import orjson
import structlog
from aiocache import Cache, cached
from aiocache.serializers import JsonSerializer
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import AuthInfo
from app.models.datasets import fetch as datasets_fetch
from app.models.examples import create as examples_create
from app.models.examples import validate as examples_validate
from app.models.feedback.fetch import fetch_feedbacks
from app.models.runs.rule_application.apply import (
    NULL_UUID,
    RuleApplier,
)

logger = structlog.get_logger(__name__)


class AddToDatasetRuleApplier(RuleApplier):
    # Cache for long enough that it stays consistent throughout the rule
    CACHE_CORRECTIONS_LEN_SEC = 300

    @classmethod
    def _should_use_corrections(cls, rule: schemas.RunRulesSchema) -> bool:
        return (
            rule.add_to_dataset_id is not None
        ) and rule.add_to_dataset_prefer_correction

    @classmethod
    @cached(
        ttl=CACHE_CORRECTIONS_LEN_SEC, cache=Cache.MEMORY, serializer=JsonSerializer()
    )
    async def _get_corrections(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled_run_ids: list[str],
        # times are included to make sure the cache can stay around for the full rule
        start_time: str,
        end_time: str,
    ) -> dict[str, Any]:
        if cls._should_use_corrections(rule) and sampled_run_ids:
            feedbacks, _ = await fetch_feedbacks(
                auth,
                schemas.QueryParamsForFeedbackSchema(
                    run=sampled_run_ids,
                    level=schemas.FeedbackLevel.run,
                ),
            )
            corrections_dict = {
                str(feedback["run_id"]): {"output": feedback["correction"]}
                if isinstance(feedback["correction"], str)
                else feedback["correction"]
                for feedback in feedbacks
                if feedback["correction"]
            }
            return corrections_dict
        else:
            return {}

    @classmethod
    async def _apply(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> dict[str, dict[str, schemas.RuleLogActionResponse]]:
        response: dict[str, dict[str, schemas.RuleLogActionResponse]] = defaultdict(
            dict
        )
        if not rule.add_to_dataset_id:
            raise ValueError("Rule must have add_to_dataset_id set to apply")

        async with asyncpg_conn() as db:
            # Note that the corrections should be pre-sampled
            corrections_dict = await cls._get_corrections(
                rule, auth, [run["id"] for run in sampled], start_time, end_time
            )
            examples: list[dict] = []
            example_run_pairs: list[tuple[dict, dict]] = []
            example_id_to_run_id: dict[uuid.UUID, uuid.UUID] = {}
            for run in sampled:
                examples += [
                    {
                        "id": uuid.uuid4(),
                        "source_run_id": run["id"],
                        "dataset_id": rule.add_to_dataset_id,
                        "use_source_run_io": True,
                    }
                ]
                example_run_pairs += [(examples[-1], run)]
                example_id_to_run_id[examples[-1]["id"]] = run["id"]

            dataset = await datasets_fetch.fetch_dataset(
                db,
                auth,
                rule.add_to_dataset_id,
            )

            # TODO (agola): support blob copying in run rules
            # TODO (jakerachleff): do not fail adding all examples if some fail validation
            updated = []
            failed_validation: dict[
                uuid.UUID,
                examples_validate.ExampleValidationError
                | examples_validate.CombinedExampleValidationError,
            ] = {}
            try:
                insert_response = await examples_create.insert_and_validate_examples(
                    db,
                    auth,
                    examples,
                    example_run_pairs,
                    dataset,
                    ignore_conflicts=True,
                    return_ids=True,
                    corrections_dict=corrections_dict,
                    fail_on_validation_error=False,
                    create_new_transaction_per_batch=True,
                )
                examples_json = insert_response.examples_json
                failed_validation = insert_response.failed_validation_examples
                updated = [
                    uuid.UUID(orjson.loads(example)["id"]) for example in examples_json
                ]
                await logger.ainfo(
                    "Finished dataset insert from rules",
                    dataset_id=rule.add_to_dataset_id,
                    num_inserted=len(updated),
                    num_failed_validation=len(failed_validation),
                )

            except Exception as e:
                await logger.aerror("Failed to insert examples from rules", error=e)

            dataset_presence: dict[uuid.UUID, uuid.UUID] = {
                example_id_to_run_id[example_id]: example_id for example_id in updated
            }
            failed_validation_presence: dict[uuid.UUID, uuid.UUID] = {
                example_id_to_run_id[example_id]: example_id
                for example_id in failed_validation
            }

            response[str(NULL_UUID)] |= {
                "add_to_dataset": schemas.RuleLogActionResponse(
                    outcome=schemas.RuleLogActionOutcome.success
                    if len(dataset_presence) == len(sampled)
                    else schemas.RuleLogActionOutcome.error
                    if len(failed_validation_presence) > 0
                    else schemas.RuleLogActionOutcome.skipped
                ),
            }
            for run in sampled:
                if run["id"] in dataset_presence:
                    run_response = schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.success,
                        payload={
                            "dataset_id": str(rule.add_to_dataset_id),
                            "example_id": dataset_presence[run["id"]],
                        },
                    )
                elif run["id"] in failed_validation_presence:
                    example_id = failed_validation_presence[run["id"]]
                    run_response = schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.error,
                        payload={
                            "error": "Failed schema validation: "
                            + str(failed_validation[example_id]),
                        },
                    )
                else:
                    run_response = schemas.RuleLogActionResponse(
                        outcome=schemas.RuleLogActionOutcome.skipped,
                    )

                response[str(run["id"])] |= {"add_to_dataset": run_response}

        return response

    @classmethod
    async def _filter_samples(
        cls,
        rule: schemas.RunRulesSchema,
        auth: AuthInfo,
        sampled: list[dict[str, Any]],
        start_time: str,
        end_time: str,
    ) -> list[dict[str, Any]]:
        if cls._should_use_corrections(rule):
            corrections_dict = await cls._get_corrections(
                rule, auth, [run["id"] for run in sampled], start_time, end_time
            )
            return [run for run in sampled if str(run["id"]) in corrections_dict]
        else:
            return sampled
