import random
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any
from uuid import UUID

import or<PERSON>son
import structlog
from lc_config.settings import shared_settings as settings
from lc_config.utils import arun_in_executor
from lc_database import clickhouse, redis
from pydantic import BaseModel

from app import config, schemas
from app.api.auth.schemas import BaseAuthInfo
from app.retry import retry_clickhouse

logger = structlog.getLogger(__name__)


class TraceTierUpgradeReason(str, Enum):
    feedback = "feedback"
    run_rule = "run_rule"
    annotation_queue = "annotation_queue"
    run_view = "run_view"


class TraceIngestionMethod(str, Enum):
    redis = "redis"
    clickhouse = "clickhouse"


class TraceUpgradeInsert(BaseModel):
    trace_id: UUID
    session_id: UUID
    reason: TraceTierUpgradeReason
    target_tier: schemas.TraceTier
    ingestion_method: TraceIngestionMethod


async def upgrade_trace_tier(
    auth: BaseAuthInfo,
    session_id: UUID,
    trace_ids: list[UUID],
    reason: TraceTierUpgradeReason = TraceTierUpgradeReason.feedback,
    target_tier: schemas.TraceTier = schemas.TraceTier.longlived,
) -> None:
    if (
        not settings.FF_TRACE_TIERS_ENABLED
        or not settings.FF_UPGRADE_TRACE_TIER_ENABLED
    ):
        return

    await logger.ainfo(
        "Upgrading traces",
        session_id=session_id,
        trace_ids=trace_ids,
        tenant_id=auth.tenant_id,
    )

    trace_ids_to_upgrade = []
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )

    # First check if the trace has already been marked for upgrade
    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.READ
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        for trace_id in trace_ids:
            trace_pending_key = f"smith:runs:pending:{str(auth.tenant_id)}:{trace_id}"
            pipe.hget(trace_pending_key, "upgrade_trace_tier")
            pipe.hget(trace_pending_key, "trace_tier")

        results = await pipe.execute()

        for trace_id, upgrade_trace_tier, trace_tier in zip(
            trace_ids, results[::2], results[1::2]
        ):
            if (
                upgrade_trace_tier is not None
                and upgrade_trace_tier.decode().strip('"') == target_tier.value
            ):
                continue
            if (
                trace_tier is not None
                and trace_tier.decode().strip('"') == target_tier.value
            ):
                continue
            trace_ids_to_upgrade.append(trace_id)

    # If not, we need to mark it for upgrade
    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.WRITE
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        # dual-write: read/write
        auth_id = auth.tenant_id

        for trace_id in trace_ids_to_upgrade:
            # get runs registered so far the trace_id
            trace_runs_key = f"smith:runs:trace_runs:{auth_id}:{trace_id}"
            pipe.smembers(trace_runs_key)

            # set target tier for the root trace to apply to ingested records going forward
            trace_pending_key = f"smith:runs:pending:{auth_id}:{trace_id}"
            pipe.hset(
                trace_pending_key,
                "upgrade_trace_tier",
                orjson.dumps(target_tier),
            )
            pipe.expire(trace_pending_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)

        results = await redis.execute_write_pipeline(
            str(auth.tenant_id), pipe, redis.RedisOperationType.UPGRADES
        )
        all_seen_runs_per_trace = results[::3][: settings.TRACE_RUN_UPGRADE_LIMIT]

    ch_traces = []
    ingest_traces = []
    upgrade_inserts = []
    async with (
        redis.aredis_routed_pool(str(auth_id), redis.RedisOperation.ENQUEUE) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        async with redis.async_routed_queue(
            settings.UPGRADES_QUEUE, str(auth_id)
        ) as queue:
            # Step through traces and enqueue background job for upgrades
            # For traces that are within ingestion, use ingestion path
            # Otherwise upgrade by fetching and upserting from Clickhouse.
            # NOTE: There may be a condition for runs past the Redis retention where this may
            # split across Redis and Clickhouse, we can monitor with our upgrades table.
            for trace_id, all_runs in zip(
                trace_ids_to_upgrade,
                all_seen_runs_per_trace[0 : len(trace_ids_to_upgrade)],
            ):
                if all_runs:
                    # sort for deterministic order
                    all_runs = sorted(all_runs)
                    ingest_traces.append(str(trace_id))
                    trace_and_run_ids = [
                        [trace_id, orjson.loads(run_id_bytes)]
                        for run_id_bytes in all_runs
                    ]
                    logger.info(
                        "Enqueueing ingestion trace for upgrade",
                        session_id=str(session_id),
                        trace_id=str(trace_id),
                        run_ids=[str(run_info[1]) for run_info in trace_and_run_ids],
                    )
                    # Chunk the upgrades into batches if too much
                    for i in range(
                        0, len(trace_and_run_ids), settings.TRACE_UPGRADE_CHUNK_SIZE
                    ):
                        batch_trace_and_run_ids = trace_and_run_ids[
                            i : i + settings.TRACE_UPGRADE_CHUNK_SIZE
                        ]
                        await queue.enqueue(
                            "upgrade_batched_runs",
                            pipeline=pipe
                            if not redis.is_redis_cluster_ingestion_enabled(
                                str(auth.tenant_id)
                            )
                            else None,
                            run_ids=batch_trace_and_run_ids,
                            tenant_id=str(auth.tenant_id),
                            lb_trace_context=structlog.contextvars.get_contextvars().get(
                                "lb_trace_context"
                            ),
                            scheduled=(
                                datetime.now(timezone.utc)
                                + timedelta(
                                    seconds=random.randint(
                                        0, settings.TRACE_UPGRADE_MAX_SPREAD_SEC
                                    )
                                )
                            ).timestamp(),
                            **redis.INGEST_JOB_KWARGS,
                        )
                    upgrade_inserts.append(
                        TraceUpgradeInsert(
                            trace_id=trace_id,
                            session_id=session_id,
                            reason=reason,
                            target_tier=target_tier,
                            ingestion_method=TraceIngestionMethod.redis,
                        )
                    )
                else:
                    ch_traces.append(str(trace_id))
                    upgrade_inserts.append(
                        TraceUpgradeInsert(
                            trace_id=trace_id,
                            session_id=session_id,
                            reason=reason,
                            target_tier=target_tier,
                            ingestion_method=TraceIngestionMethod.clickhouse,
                        )
                    )

            # no need to dual-write here since it's only enqueueing to appropriate shard with no other operations
            await pipe.execute()

    await upsert_trace_upgrade(auth, upgrade_inserts)

    async with redis.async_routed_queue(settings.UPGRADES_QUEUE, str(auth_id)) as queue:
        if ch_traces:
            logger.info("Enqueueing ch trace for upgrade", tenant_id=auth.tenant_id)
            # we only need to enqueue the tenant_id and the rest will be fetched from Clickhouse
            job = await queue.enqueue(
                "upgrade_batched_ch_traces",
                auth_dict=auth.model_dump(),
                key=f"upgrade_batched_ch_traces:{auth.tenant_id}",
                scheduled=(
                    datetime.now(timezone.utc)
                    + timedelta(seconds=settings.CH_UPGRADE_QUEUE_DELAY_SEC)
                ).timestamp(),
                lb_trace_context=structlog.contextvars.get_contextvars().get(
                    "lb_trace_context"
                ),
                **redis.CH_UPGRADE_KWARGS,
            )
            if not job:
                async with redis.aredis_pool() as aredis:
                    # locks will remain on node-0 rather than sharding
                    inflight_key = f"lock_upgrade_ch_traces:{auth.tenant_id}"
                    lock = aredis.lock(inflight_key)
                    # In this case if the job is in progress, we need to ensure it still runs in the future.
                    if await lock.locked():
                        # re-enqueue the job in the future since it's in progress
                        await queue.enqueue(
                            "schedule_upgrade_batched_ch_traces",
                            auth_dict=auth.model_dump(),
                            scheduled=(
                                datetime.now(timezone.utc)
                                + timedelta(seconds=settings.CH_UPGRADE_QUEUE_DELAY_SEC)
                            ).timestamp(),
                            **redis.CH_UPGRADE_KWARGS,
                        )
                        await logger.ainfo(
                            "Ensured upgrade_ch job is scheduled in the future for tenant",
                            tenant_id=auth.tenant_id,
                        )
                    else:
                        await logger.aerror(
                            "Skipped upgrade_ch job for tenant since lock is not busy",
                            tenant_id=auth.tenant_id,
                        )

    logger.info(
        "Enqueued traces for upgrade",
        ch_traces=ch_traces,
        ingest_traces=ingest_traces,
        session_id=str(session_id),
    )


@retry_clickhouse
async def upsert_trace_upgrade(
    auth: BaseAuthInfo,
    upgrades: list[TraceUpgradeInsert],
) -> None:
    if not upgrades:
        return

    data = await arun_in_executor(_prepare_upsert_payload, auth, upgrades)

    request = clickhouse.ExecuteRequest(
        "upsert_trace_upgrade",
        """INSERT INTO trace_upgrades (trace_id, tenant_id, session_id, reason, target_tier, ingestion_method) SETTINGS async_insert=1 VALUES""",
        data,
    )

    await clickhouse.multi_execute_single(request, use_slow_client=True)

    logger.info(
        "Upserted trace upgrades into ClickHouse",
        upgrade_count=len(data),
        upgrade_ids=[d[0] for d in data],
    )


def _prepare_upsert_payload(
    auth: BaseAuthInfo, upgrades: list[TraceUpgradeInsert]
) -> list[list[Any]]:
    return [
        [
            UUID(upgrade.trace_id.hex),
            UUID(auth.tenant_id.hex),
            UUID(upgrade.session_id.hex),
            upgrade.reason.value,
            upgrade.target_tier.value,
            upgrade.ingestion_method.value,
        ]
        for upgrade in upgrades
    ]
