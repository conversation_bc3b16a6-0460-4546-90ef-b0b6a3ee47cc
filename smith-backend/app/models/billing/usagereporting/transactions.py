import abc
import datetime
import uuid
from enum import Enum
from typing import Generic, List, TypeVar

import httpx
import structlog
from lc_config.service_communication_settings import ServiceName
from lc_config.settings import shared_settings  # noqa: E402
from lc_database import metronome
from lc_database.database import asyncpg_conn
from lc_database.service_client import get_service_client

logger = structlog.getLogger(__name__)


class UsageReportingStatus(Enum):
    TODO = "todo"
    PENDING = "pending"
    SENT = "sent"
    SHOULD_NOT_REPORT = "should_not_report"
    SHOULD_RETRY = "should_retry"
    FAILED = "failed"
    SKIPPED = "skipped"


class LangSmithUsageTransaction(abc.ABC):
    @abc.abstractmethod
    def get_org_id(self) -> uuid.UUID | None:
        pass

    @abc.abstractmethod
    def get_transaction_id(self) -> uuid.UUID:
        pass

    @abc.abstractmethod
    def get_status(self) -> UsageReportingStatus:
        pass

    @abc.abstractmethod
    def get_num_failed_send_attempts(self) -> int:
        pass

    @classmethod
    @abc.abstractmethod
    def get_status_colname(cls) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def get_failed_attempts_colname(cls) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def get_table_name(cls) -> str:
        pass

    @classmethod
    @abc.abstractmethod
    def get_transaction_id_colname(cls) -> str:
        pass

    @abc.abstractmethod
    def get_event_timestamp(self) -> datetime.datetime:
        pass


class MetronomeTransaction(LangSmithUsageTransaction):
    @abc.abstractmethod
    def get_properties(self) -> dict:
        pass

    @abc.abstractmethod
    def get_event_type(self) -> str:
        pass

    @abc.abstractmethod
    def get_self_hosted_customer_id(self) -> uuid.UUID | None:
        pass

    def to_metronome_ingest_body(self) -> dict:
        # For self-hosted customers we ingest using the self-hosted customer ID
        customer_id = self.get_self_hosted_customer_id() or self.get_org_id()
        return {
            "transaction_id": str(self.get_transaction_id()),
            "customer_id": str(customer_id),
            "event_type": self.get_event_type(),
            "timestamp": self.get_event_timestamp().isoformat(),
            "properties": self.get_properties(),
        }

    @abc.abstractmethod
    def to_json(self) -> dict:
        pass


TxnType = TypeVar("TxnType", bound=LangSmithUsageTransaction)
MetronomeTxnType = TypeVar("MetronomeTxnType", bound=MetronomeTransaction)


class LangSmithUsageTransactionReporter(Generic[TxnType], abc.ABC):
    @classmethod
    def _logstr(cls, msg: str) -> str:
        return f"{cls.__name__}: {msg}"

    @classmethod
    @abc.abstractmethod
    def _get_initial_status(cls) -> UsageReportingStatus:
        # TODO: this should be unified, but currently isn't.
        pass

    @classmethod
    @abc.abstractmethod
    async def _fetch_reportable_transactions(
        cls,
        status: UsageReportingStatus,
        before_time: datetime.datetime | None = None,
    ) -> tuple[List[TxnType], bool]:  # True if we need to fetch another batch
        pass

    @classmethod
    @abc.abstractmethod
    async def _send_transaction_batch(cls, txns: List[TxnType]) -> bool:
        pass

    @classmethod
    async def report_usage(cls) -> None:
        """
        Report usage to an upstream usage processor. This will report based on the usage's
        start interval rather than the first usage that occurred within reporting
        interval. We do this on purpose to provide nicer timestamps for our systems.
        """
        await logger.ainfo(cls._logstr("Reporting usage"))
        should_fetch_another_batch = True
        current_time = datetime.datetime.now(datetime.timezone.utc)

        while should_fetch_another_batch:
            (
                transactions_to_send,
                should_fetch_another_batch,
            ) = await cls._fetch_reportable_transactions(
                cls._get_initial_status(), current_time
            )

            if len(transactions_to_send) == 0:
                await logger.ainfo(cls._logstr("No transactions to report"))
                return

            await logger.ainfo(
                cls._logstr("Found transactions. Attempting to report them."),
                metadata={
                    "transaction_count": len(transactions_to_send),
                },
            )
            successfully_reported = 0
            transactions_to_report = len(transactions_to_send)
            for idx in range(
                0,
                transactions_to_report,
                shared_settings.METRONOME_REPORTING_BATCH_SIZE,
            ):
                batch = transactions_to_send[
                    idx : min(
                        idx + shared_settings.METRONOME_REPORTING_BATCH_SIZE,
                        transactions_to_report,
                    )
                ]

                table_name = batch[0].get_table_name()
                status_col = batch[0].get_status_colname()
                failed_attempts_col = batch[0].get_failed_attempts_colname()
                txn_id_col = batch[0].get_transaction_id_colname()

                if await cls._send_transaction_batch(batch):
                    successfully_reported += len(batch)

                    async with asyncpg_conn() as db, db.transaction():
                        await db.executemany(
                            f"""
                            UPDATE
                                {table_name}
                            SET
                                {status_col} = '{UsageReportingStatus.SENT.value}'
                            WHERE
                                {txn_id_col} = $1
                            """,
                            [(txn.get_transaction_id(),) for txn in batch],
                        )
                else:
                    async with asyncpg_conn() as db, db.transaction():
                        await db.executemany(
                            f"""
                            UPDATE
                                {table_name}
                            SET
                                {status_col} = '{UsageReportingStatus.SHOULD_RETRY.value}',
                                {failed_attempts_col} = 1
                            WHERE
                                {txn_id_col} = $1
                                -- if another process already updated the status to be
                                -- successful, then we shouldn't retry. Only change the
                                -- status if it's still pending
                                AND {status_col} = '{cls._get_initial_status().value}'
                            """,
                            [(txn.get_transaction_id(),) for txn in batch],
                        )

            await logger.ainfo(
                cls._logstr("Reported transactions."),
                metadata={
                    "sent_transactions": successfully_reported,
                    "failed_transactions": transactions_to_report
                    - successfully_reported,
                },
            )

    @classmethod
    async def retry_report_failed_transactions(cls) -> None:
        """
        Retry reporting transactions that previously failed to report to metronome.
        """
        await logger.ainfo(cls._logstr("Retrying failed transactions"))
        should_fetch_another_batch = True
        current_time = datetime.datetime.now(datetime.timezone.utc)

        while should_fetch_another_batch:
            (
                transactions_to_retry,
                should_fetch_another_batch,
            ) = await cls._fetch_reportable_transactions(
                UsageReportingStatus.SHOULD_RETRY, current_time
            )

            if len(transactions_to_retry) == 0:
                await logger.ainfo(cls._logstr("No transactions to retry"))
                return

            await logger.ainfo(
                cls._logstr("Attempting to retry transactions"),
                metadata={
                    "transactions": [
                        str(txn.get_transaction_id()) for txn in transactions_to_retry
                    ],
                    "orgs": [str(txn.get_org_id()) for txn in transactions_to_retry],
                    "event_timestamps": [
                        txn.get_event_timestamp().isoformat()
                        for txn in transactions_to_retry
                    ],
                },
            )

            successful_txns: List[TxnType] = []
            failed_txns: List[TxnType] = []
            needs_retry_txns: List[TxnType] = []

            for txn in transactions_to_retry:
                success = await cls._send_transaction_batch([txn])
                async with asyncpg_conn() as db, db.transaction():
                    table_name = txn.get_table_name()
                    status_col = txn.get_status_colname()
                    failed_attempts_col = txn.get_failed_attempts_colname()
                    txn_id_col = txn.get_transaction_id_colname()

                    if success:
                        await db.execute(
                            f"""
                            UPDATE
                                {table_name}
                            SET
                                {status_col} = '{UsageReportingStatus.SENT.value}'
                            WHERE
                                {txn_id_col} = $1
                            """,
                            txn.get_transaction_id(),
                        )
                        successful_txns.append(txn)
                    elif (
                        txn.get_num_failed_send_attempts() + 1
                    ) < shared_settings.MAX_TRANSACTION_SEND_ATTEMPTS:
                        await db.execute(
                            f"""
                            UPDATE
                                {table_name}
                            SET
                                {failed_attempts_col} = {txn.get_num_failed_send_attempts() + 1}
                            WHERE
                                {txn_id_col} = $1
                                -- if the status has changed, then we shouldn't retry, since this
                                -- means another process has already retried this transaction and
                                -- it was successful or it hit its last retry attempt
                                AND {status_col} = '{UsageReportingStatus.SHOULD_RETRY.value}'
                            """,
                            txn.get_transaction_id(),
                        )
                        needs_retry_txns.append(txn)
                    else:
                        await db.execute(
                            f"""
                            UPDATE
                                {table_name}
                            SET
                                {status_col} = '{UsageReportingStatus.FAILED.value}',
                                {failed_attempts_col} = {txn.get_num_failed_send_attempts() + 1}
                            WHERE
                                {txn_id_col} = $1
                                -- if the status has changed, then we shouldn't attempt to
                                -- change it again, since this means another process has already
                                -- retried this transaction and it was successful or failed
                                AND {status_col} = '{UsageReportingStatus.SHOULD_RETRY.value}'
                            """,
                            txn.get_transaction_id(),
                        )
                        failed_txns.append(txn)

            await logger.ainfo(
                cls._logstr("Completed retry iteration of failed transactions"),
                metadata={
                    "successful_transactions": [
                        str(txn.get_transaction_id()) for txn in successful_txns
                    ],
                    "failed_transactions": [
                        str(txn.get_transaction_id()) for txn in failed_txns
                    ],
                    "needs_retry_transactions": [
                        str(txn.get_transaction_id()) for txn in needs_retry_txns
                    ],
                },
            )


class MetronomeTransactionReporter(LangSmithUsageTransactionReporter[MetronomeTxnType]):
    @classmethod
    async def _send_transaction_batch(cls, txns: List[MetronomeTxnType]) -> bool:
        usage_events = [txn.to_metronome_ingest_body() for txn in txns]

        async with metronome.metronome_client() as httpx_session:
            try:
                await logger.ainfo(
                    cls._logstr("Sending usage events to metronome"),
                    metadata={"usage_events": usage_events},
                )
                await httpx_session.post(
                    "https://api.metronome.com/v1/ingest",
                    data=usage_events,
                )
                return True
            except httpx.HTTPStatusError as e:
                await logger.aerror(
                    cls._logstr("Failed to report trace count to metronome."),
                    metadata={
                        "failed_transactions": [
                            str(txn.get_transaction_id()) for txn in txns
                        ],
                        "affected_orgs": [str(txn.get_org_id()) for txn in txns],
                        "affected_properties": [txn.get_properties() for txn in txns],
                        "usage_events": usage_events,
                    },
                    exc_info=e,
                )
                return False


class SelfHostedTransactionReporter(
    LangSmithUsageTransactionReporter[MetronomeTxnType]
):
    @classmethod
    async def _send_transaction_batch(cls, txns: List[MetronomeTxnType]) -> bool:
        async with get_service_client(ServiceName.BEACON) as beacon_client:
            try:
                body = {
                    "license": shared_settings.LANGSMITH_LICENSE_KEY,
                    # Just serialize the txn to forward to beacon
                    "trace_transactions": [txn.to_json() for txn in txns],
                }
                response = await beacon_client.post(
                    "v1/beacon/ingest-traces",
                    json=body,
                )
                response.raise_for_status()
                logger.info(
                    cls._logstr("Successfully reported trace count to beacon."),
                    metadata={
                        "successful_transactions": [
                            str(txn.get_transaction_id()) for txn in txns
                        ],
                        "affected_orgs": [str(txn.get_org_id()) for txn in txns],
                    },
                )
                return True
            except httpx.HTTPStatusError as e:
                await logger.aerror(
                    cls._logstr("Failed to report trace count to beacon."),
                    metadata={
                        "failed_transactions": [
                            str(txn.get_transaction_id()) for txn in txns
                        ],
                        "affected_orgs": [str(txn.get_org_id()) for txn in txns],
                    },
                    exc_info=e,
                )
                return False
