"""CRUD operations."""

import asyncio
import csv
import functools
import io
import json
import re
import tempfile
from contextlib import AsyncExitStack
from dataclasses import dataclass
from dataclasses import field as dataclass_field
from datetime import datetime, timedelta, timezone
from typing import (
    Any,
    Callable,
    List,
    Mapping,
    NamedTuple,
    Optional,
    Sequence,
    Tuple,
    Union,
    cast,
    overload,
)
from uuid import UUID, uuid4, uuid5

import asyncpg
import jsonpatch
import orjson
import structlog  # noqa: E402
from anyio import TASK_STATUS_IGNORED
from anyio.abc import TaskStatus
from anyio.streams.memory import MemoryObjectSendStream
from ddtrace import tracer
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database import clickhouse, database, redis
from lc_database.queue.serializer import ORJSONSerializer
from lc_database.s3_client import copy_object
from redis.asyncio import Redis as AsyncRedis
from rq.queue import EnqueueData

from app import config, models, schemas
from app.api.auth import AuthInfo, ShareRunInfo, TenantlessAuthInfo
from app.api.auth.schemas import (
    FeedbackTokenInfo,
    OrgAuthInfo,
    ShareDatasetInfo,
    parse_full_auth_info,
)
from app.memoize import clear_redis_cache, redis_cache
from app.models.datasets.indexed.index import (
    get_indexed_dataset_opt_within_txn,
    remove_index_for_dataset,
)
from app.models.examples import create as example_create
from app.models.examples.fetch import populate_attachment_urls_correct_format
from app.models.examples.transform import validate_transformations_format
from app.models.examples.validate import (
    CombinedExampleValidationError,
    ExampleValidationError,
    validate_dataset_schema_and_transformations,
    validate_example,
)
from app.models.feedback.fetch import (
    fetch_feedbacks,
)
from app.models.identities.crud import get_tenant_members
from app.models.query_lang.parse import parse_as_filter_directive
from app.models.runs.compression import (
    compress_based_on_size,
    compress_based_on_size_many,
)
from app.models.runs.fetch_ch import FetchRunsResult, fetch_runs
from app.models.runs.ingest import (
    persist_batched_runs_ch,
    persist_run_and_children_ch,
    queued_runs_key,
)
from app.models.runs.upgrade import (
    TraceTierUpgradeReason,
    upgrade_trace_tier,
)
from app.models.runs.usage_limits import (
    check_longlived_usage_limits,
    usage_limit_events_ingested_per_hour_counter_key,
    usage_limit_events_ingested_per_minute_counter_key,
    usage_limit_payload_size_per_hour_counter_key,
)
from app.models.shared.utils import is_user_auth
from app.models.tracer_sessions.start import start_or_fetch_tracer_session
from app.models.usage_limits import user_defined_limits
from app.retry import retry_asyncpg
from app.utils import (
    arun_in_executor,
    completed_future,
    gated_coro,
    start_of_next_month_timestamp,
)
from app.workers.utils import set_auth_metadata

logger = structlog.getLogger(__name__)

QUEUE_LOCK_TIMEOUT = 2
TASK_IN_QUEUED_SET = 1
QUEUED_RUN_SET_EXPIRATION = 60 * 60 * 24 + 300

DELETE_RETRY_DELAY = 60
DELETE_RETRIES = 2
DELETE_TIMEOUT = 1200
DELETE_ASSETS_RETRIES = 5
DELETE_ASSETS_TIMEOUT = 3600

ANNOTATION_QUEUE_SYNC_RUN_FETCH_LIMIT = 99


@dataclass
class SeenTraces:
    traceUpgradeSet: set[UUID] = dataclass_field(default_factory=set)


@retry_asyncpg
async def get_tenant_stats(
    auth: AuthInfo, tag_value_ids: List[UUID] | None = None
) -> schemas.TenantStats:
    sql = (
        models.tenants.sql.tenant_stats_sql
        if tag_value_ids is None
        else models.tenants.sql.tenant_stats_sql_with_value_ids
    )
    args: List[Any] = (
        [auth.tenant_id] if tag_value_ids is None else [auth.tenant_id, tag_value_ids]
    )

    async with database.asyncpg_pool() as db:
        row = await db.fetchrow(
            sql,
            *args,
        )
    return schemas.TenantStats.model_validate(row)


@retry_asyncpg
async def get_tenant(auth: AuthInfo) -> schemas.Tenant:
    """Get a tenant by id."""
    async with database.asyncpg_conn() as db:
        tenant = await db.fetchrow(
            """select
                tenants.*,
                is_personal
            from tenants
            inner join organizations on tenants.organization_id = organizations.id
            where tenants.id = $1""",
            auth.tenant_id,
        )
    assert auth.exists(tenant)
    return schemas.Tenant.model_validate(tenant)


@retry_asyncpg
async def create_tenant(
    auth: TenantlessAuthInfo, tenant: schemas.TenantCreate
) -> schemas.Tenant:
    """Create a tenant."""
    async with database.asyncpg_conn() as db:
        return await models.tenants.create.create_tenant(db, auth, tenant)


@retry_asyncpg
async def get_runs_monitoring(
    auth: AuthInfo,
    query_params: schemas.MonitorRequest,
) -> schemas.MonitorResponse:
    return await models.runs.monitor.get_monitor_blocks(auth, query_params)


@retry_asyncpg
async def get_tracer_session_asyncpg(
    db: asyncpg.Connection,
    auth: AuthInfo,
    tracer_session_id: UUID,
    query_params: schemas.QueryParamsForSingleTracerSessionSchema,
) -> schemas.TracerSession:
    if query_params.include_stats:
        return schemas.TracerSession(
            **jsonpatch.apply_patch(
                {},
                [
                    op
                    async for ops in models.tracer_sessions.stream.stream_session(
                        auth, tracer_session_id, query_params
                    )
                    for op in ops
                ],
            )
        )
    else:
        row = await db.fetchrow(
            """
select *
from tracer_session
where id = $1 and tenant_id = $2""",
            tracer_session_id,
            auth.tenant_id,
        )
        assert auth.exists(row)
        return schemas.TracerSession(**row)


async def ensure_tracer_sessions_from_cache(
    auth_type: str,
    auth_id: str,
    sessions: set[str],
) -> bool:
    async with redis.aredis_caching_pool() as aredis, aredis.pipeline() as pipe:
        if len(sessions) == 0:
            return False
        if settings.DATADOG_ENABLED and (span := tracer.current_span()):
            span.set_tag("num_sessions", len(sessions))
        if settings.REDIS_USE_SMISMEMBER:
            # can use a set (not list) of sessions because order of results doesn't matter
            pipe.smismember(
                f"smith:cache:auth_tracer_sessions:{auth_type}:{auth_id}", sessions
            )
        else:
            for session in sessions:
                pipe.sismember(
                    f"smith:cache:auth_tracer_sessions:{auth_type}:{auth_id}", session
                )
        results = await pipe.execute()

        # flatten list to handle smismember result
        if settings.REDIS_USE_SMISMEMBER:
            return all(x for xs in results for x in xs)
        return all(results)


async def send_tracer_sessions_to_cache(
    auth_type: str,  # can be tenant or organization
    auth_id: str,
    sessions: set[str],
) -> None:
    if settings.DATADOG_ENABLED and (span := tracer.current_span()):
        span.set_tag("num_sessions", len(sessions))
    async with redis.aredis_caching_pool() as aredis:
        ttl = await aredis.ttl(
            f"smith:cache:auth_tracer_sessions:{auth_type}:{auth_id}"
        )
        await aredis.sadd(
            f"smith:cache:auth_tracer_sessions:{auth_type}:{auth_id}", *sessions
        )
        if ttl < 0:
            await aredis.expire(
                f"smith:cache:auth_tracer_sessions:{auth_type}:{auth_id}",
                settings.TENANT_TRACER_SESSIONS_CACHE_TTL,
            )


@retry_asyncpg
async def ensure_tracer_sessions(
    auth: AuthInfo | OrgAuthInfo,
    sessions_set: set[UUID],
) -> None:
    """Ensure that all provided session IDs exist and belong to the tenant/org."""

    auth_type = "tenant" if isinstance(auth, AuthInfo) else "organization"
    auth_id = auth.tenant_id if auth_type == "tenant" else auth.organization_id
    # check if session_id is in redis
    if await ensure_tracer_sessions_from_cache(
        auth_type,
        str(auth_id),
        set(map(str, sessions_set)),
    ):
        return

    query = """SELECT id FROM tracer_session WHERE id = ANY($1)""" + (
        """ AND tenant_id = $2"""
        if auth_type == "tenant"
        else """ AND tenant_id = ANY(SELECT id FROM tenants WHERE organization_id = $2)"""
    )
    # failed to validate cache, fetch from db
    async with database.asyncpg_conn() as db:
        fetched_sessions = await db.fetch(
            query,
            list(sessions_set),
            auth_id,
        )

        if len(fetched_sessions) > 0:
            # send to redis cache
            await send_tracer_sessions_to_cache(
                auth_type, str(auth_id), {str(row["id"]) for row in fetched_sessions}
            )

        if len(fetched_sessions) != len(sessions_set):
            raise HTTPException(
                status_code=404,
                detail="One or more sessions not found",
            )


@redis_cache(ttl=30, exclude_kwargs=["auth", "query_params"])
async def get_cached_top_tracer_sessions(
    _tenant_id: UUID,
    k: int,
    *,
    auth: AuthInfo,
    query_params: schemas.FilterQueryParamsForTracerSessionSchema,
) -> tuple[list[schemas.TracerSession], int]:
    query_params.limit = k
    rows, total = await get_tracer_sessions(auth, query_params)
    return rows, total


@retry_asyncpg
async def get_tracer_session_by_name_asyncpg(
    db: asyncpg.Connection, auth: AuthInfo, session_name: str
) -> schemas.TracerSession:
    """Get a tracer session by id."""
    row = await db.fetchrow(
        """
select *
from tracer_session
where name = $1 and tenant_id = $2""",
        session_name,
        auth.tenant_id,
    )
    assert auth.exists(row)
    return schemas.TracerSession(**row)


@retry_asyncpg
async def get_workspace_sessions(
    db: asyncpg.Connection, auth: AuthInfo
) -> list[schemas.TracerSession]:
    """Get a tracer session by id."""
    result = await db.fetch(
        """
select *
from tracer_session
where tenant_id = $1""",
        auth.tenant_id,
    )
    sessions = [schemas.TracerSession(**row) for row in result]
    return sessions


@retry_asyncpg
async def get_tracer_sessions(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForTracerSessionSchema,
) -> tuple[list[schemas.TracerSession], int]:
    """Get all tracer sessions that belong to the owner."""
    result = jsonpatch.apply_patch(
        {},
        [
            op
            async for ops in models.tracer_sessions.stream.stream_sessions(
                auth, query_params
            )
            for op in ops
        ],
    )
    sessions = [schemas.TracerSession(**row) for row in result["rows"]]

    return sessions, result["total"]


@retry_asyncpg
async def delete_tracer_sessions_within_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    tracer_session_ids: List[UUID],
) -> None:
    """Delete tracer sessions."""

    if not tracer_session_ids:
        raise HTTPException(
            status_code=400,
            detail="Must provide at least one tracer session id.",
        )

    if settings.BLOB_STORAGE_ENGINE == "Azure":
        raise HTTPException(
            status_code=501,
            detail="Deleting tracer sessions is not supported with Azure Blob Storage.",
        )

    try:
        rows = await db.fetch(
            """
            DELETE FROM tracer_session
            WHERE id = ANY($1) AND tenant_id = $2
            RETURNING id, name
            """,
            tracer_session_ids,
            auth.tenant_id,
        )
        if len(rows) != len(tracer_session_ids):
            raise HTTPException(
                status_code=404,
                detail="One or more tracer sessions not found.",
            )

        for row in rows:
            # Clear redis cache so that tracer session is not used for further ingestion
            # Handle different param combination (session_id or session_name combinations)
            exclude_kwargs = ["start_time_str"]
            for session_id, session_name in (
                (row[0], row[1]),
                (row[0], None),
                (None, row[1]),
            ):
                await clear_redis_cache(
                    start_or_fetch_tracer_session,
                    auth,
                    session_id=session_id,
                    session_name=session_name,
                    exclude_kwargs=exclude_kwargs,
                )

    except asyncpg.exceptions.ForeignKeyViolationError:
        raise HTTPException(
            status_code=409,
            detail="Cannot delete a project that is associated with a "
            + "LangServe deployment. Please delete the deployment first, then try again.",
        )

    delete_ch_key = uuid5(
        auth.tenant_id,
        orjson.dumps(sorted(list(map(str, tracer_session_ids)))).decode("utf-8"),
    )
    async with redis.async_queue(settings.ADHOC_QUEUE) as queue:
        # Schedule delete tasks. Use a delay so any pending ingestion tasks can complete.
        await queue.enqueue(
            "background_delete_tracer_sessions",
            auth_dict=auth.model_dump(),
            tracer_session_ids=list(map(str, tracer_session_ids)),
            key=f"delete_job:{delete_ch_key}",  # job key for job deduplication
            scheduled=(
                datetime.now(timezone.utc)
                + timedelta(seconds=settings.DELETE_QUEUE_DELAY_SEC)
            ).timestamp(),
            # custom retry settings
            retry_delay=DELETE_RETRY_DELAY,
            timeout=DELETE_TIMEOUT,
            retries=DELETE_RETRIES,
            retry_backoff=True,
        )
        if config.settings.FF_BLOB_STORAGE_ENABLED:
            for session_id in tracer_session_ids:
                delete_assets_key = uuid5(
                    auth.tenant_id,
                    orjson.dumps(str(session_id)).decode("utf-8"),
                )
                await queue.enqueue(
                    "background_delete_stored_assets",
                    auth_dict=auth.model_dump(),
                    session_id=str(session_id),
                    key=f"delete_ch_static_assets:{delete_assets_key}",  # job key for job deduplication
                    scheduled=(
                        datetime.now(timezone.utc)
                        + timedelta(seconds=settings.DELETE_QUEUE_DELAY_SEC)
                    ).timestamp(),
                    # custom retry settings
                    retry_delay=DELETE_RETRY_DELAY,
                    timeout=DELETE_ASSETS_TIMEOUT,
                    retries=DELETE_ASSETS_RETRIES,
                    retry_backoff=True,
                )


async def evict_tenant_from_tracer_sessions_cache(tenant_id: UUID) -> None:
    async with redis.aredis_caching_pool() as aredis:
        await aredis.delete(f"smith:cache:auth_tracer_sessions:tenant:{str(tenant_id)}")


@retry_asyncpg
async def delete_tracer_sessions(
    auth: AuthInfo,
    tracer_session_ids: List[UUID],
) -> None:
    """Delete tracer sessions."""

    await evict_tenant_from_tracer_sessions_cache(auth.tenant_id)

    async with database.asyncpg_conn() as db, db.transaction():
        await delete_tracer_sessions_within_transaction(
            db,
            auth,
            tracer_session_ids,
        )


async def delete_tracer_sessions_ch(
    tenant_ids: List[UUID], tracer_session_ids: List[UUID]
) -> None:
    params = {
        "tenant_ids": tenant_ids,
        "session_ids": tracer_session_ids,
    }
    for tenant_id in tenant_ids:
        await evict_tenant_from_tracer_sessions_cache(tenant_id)

    logger.info("Deleting sessions", session_ids=tracer_session_ids)
    await clickhouse.multi_execute_single(
        clickhouse.ExecuteRequest(
            "delete_tracer_runs",
            """DELETE FROM runs WHERE tenant_id IN {tenant_ids} AND session_id IN {session_ids}""",
            params=params,
        ),
        clickhouse.ExecuteRequest(
            "delete_tracer_feedbacks",
            """DELETE FROM feedbacks WHERE tenant_id IN {tenant_ids} AND session_id IN {session_ids}""",
            params=params,
        ),
        *clickhouse.delete_materialized_view_queries(
            source_table="feedbacks",
            query="DELETE FROM {table} WHERE tenant_id IN {tenant_ids} AND session_id IN {session_ids}",
            params=params,
        ),
        *clickhouse.delete_materialized_view_queries(
            source_table="runs",
            query="DELETE FROM {table} WHERE tenant_id IN {tenant_ids} AND session_id IN {session_ids}",
            params=params,
        ),
        use_slow_client=True,
        max_attempts=2,
    )


@retry_asyncpg
async def create_tracer_session(
    auth: AuthInfo,
    tracer_session: schemas.TracerSessionCreate,
    upsert: bool = False,
) -> schemas.TracerSessionWithoutVirtualFields:
    """Create a tracer session."""

    async with database.asyncpg_conn() as db:
        return await models.tracer_sessions.create.create_tracer_session(
            db, auth, tracer_session, upsert=upsert
        )


@retry_asyncpg
async def update_tracer_session(
    auth: AuthInfo,
    session_id: UUID,
    tracer_session: schemas.TracerSessionUpdate,
) -> schemas.TracerSessionWithoutVirtualFields:
    """Update a tracer session."""
    return await models.tracer_sessions.update.update_tracer_session(
        auth, session_id, tracer_session
    )


async def get_examples(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> Tuple[list[schemas.Example], int]:
    examples, total = await get_examples_json(auth, query_params)
    exampls_objs = [schemas.Example.model_validate_json(row) for row in examples]
    for example in exampls_objs:
        if example.attachment_urls:
            example.attachment_urls = populate_attachment_urls_correct_format(
                example.attachment_urls
            )
    return exampls_objs, total


@retry_asyncpg
async def get_examples_json(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> Tuple[list[str], int]:
    """Get all examples depending on the owner and dataset."""
    async with database.asyncpg_conn() as db:
        examples = await models.examples.fetch.fetch_examples_json(
            db, auth, query_params
        )
        if query_params.limit > 0:
            return (
                examples[: query_params.limit],
                query_params.offset + len(examples),
            )
        else:
            return examples, query_params.offset + len(examples)


@retry_asyncpg
async def count_examples_json(
    auth: AuthInfo | ShareDatasetInfo,
    query_params: schemas.FilterQueryParamsForExampleSchema,
) -> int:
    """Count all examples depending on the owner and dataset."""
    async with database.asyncpg_conn() as db:
        return await models.examples.fetch.count_examples_json(db, auth, query_params)


async def get_example_runs_from_session_ch(
    auth: AuthInfo | ShareDatasetInfo,
    dataset_id: UUID,
    query_params: schemas.QueryExampleSchemaWithRuns,
) -> Tuple[List[schemas.ExampleWithRunsCH] | List[schemas.PublicExampleWithRuns], int]:
    """Get all examples depending on the owner and dataset, using ClickHouse."""

    examples_with_runs, total = await models.runs.fetch_ch.fetch_runs_comparison_view(
        auth, dataset_id, query_params
    )
    return examples_with_runs, total


@retry_asyncpg
async def get_example(
    auth: AuthInfo, example_id: UUID, as_of: datetime | str = "latest"
) -> schemas.Example:
    """Get an example by id."""
    async with database.asyncpg_conn() as db:
        return await models.examples.fetch.fetch_example(
            db, auth, example_id, as_of=as_of
        )


@retry_asyncpg
async def create_examples(
    auth: AuthInfo, examples: List[dict], ignore_conflicts=True
) -> List[str]:
    async with database.asyncpg_conn() as db, db.transaction():
        (
            ret_examples,
            blobs_to_copy,
            example_ids_to_ignore,
        ) = await create_examples_with_existing_transaction(
            db, auth, examples, ignore_conflicts=ignore_conflicts
        )

    try:
        # copy attachments
        # for now, do this after postgres to prioritize getting examples in the db
        # ignore examples in examples_to_ignore, flat map
        await _copy_example_blobs(blobs_to_copy, example_ids_to_ignore)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail="Error copying attachments from source run to example.",
        ) from e

    return ret_examples


@retry_asyncpg
async def create_examples_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    examples: list[dict],
    ignore_conflicts: bool = True,
) -> Tuple[list[str], dict[str, List[Tuple[str, str]]], set[str]]:
    """Create multiple examples."""
    if not examples:
        raise HTTPException(
            status_code=400,
            detail="Must provide at least one example.",
        )
    dataset_id = examples[0]["dataset_id"]
    for example in examples:
        if example["dataset_id"] != dataset_id:
            raise HTTPException(
                status_code=400,
                detail="All examples must belong to the same dataset",
            )

    dataset, example_run_pairs = await asyncio.gather(
        models.datasets.fetch.fetch_dataset(db, auth, dataset_id),
        models.examples.sourcerun.resolve_runs_for_examples(auth, examples),
    )
    if not dataset:
        raise HTTPException(
            status_code=404,
            detail="Dataset not found.",
        )

    insert_response = await example_create.insert_and_validate_examples(
        db, auth, examples, example_run_pairs, dataset, ignore_conflicts
    )
    return (
        insert_response.examples_json,
        insert_response.blobs_to_copy,
        insert_response.example_ids_to_ignore,
    )


async def _copy_example_blobs(
    blobs_to_copy: dict[str, List[Tuple[str, str]]], example_ids_to_ignore: set[str]
):
    blobs_to_copy_fm: list[Tuple[str, str]] = [
        (src, dest)
        for k, v in blobs_to_copy.items()
        if k not in example_ids_to_ignore
        for src, dest in v
    ]
    if blobs_to_copy_fm and settings.FF_BLOB_STORAGE_ENABLED:
        semaphore = asyncio.Semaphore(settings.BLOB_STORAGE_FETCH_SEMAPHORE)
        await asyncio.gather(
            *[
                gated_coro(copy_object(src, dest), semaphore)
                for src, dest in blobs_to_copy_fm
            ]
        )


@retry_asyncpg
async def update_example(
    auth: AuthInfo,
    example_id: UUID,
    example_update: schemas.ExampleUpdate,
) -> schemas.Example:
    """Update an example."""
    async with database.asyncpg_conn() as db:
        return await models.examples.create.update_example(
            db, auth, example_id, example_update
        )


@retry_asyncpg
async def update_examples(
    auth: AuthInfo,
    example_updates: list[schemas.ExampleUpdateWithID],
) -> List[schemas.Example]:
    """Update an example."""
    async with database.asyncpg_conn() as db:
        modified_at = datetime.now(tz=timezone.utc)
        return await models.examples.create.update_examples(
            db, auth, example_updates, coerce_modified_at=modified_at
        )


@retry_asyncpg
async def delete_example(auth: AuthInfo, example_id: UUID) -> None:
    """Delete an example."""
    async with database.asyncpg_conn() as db:
        await models.examples.delete.delete_example(db, auth, example_id)


@retry_asyncpg
async def delete_examples(auth: AuthInfo, example_ids: List[UUID]) -> None:
    """Delete multiple examples."""
    async with database.asyncpg_conn() as db, db.transaction():
        await models.examples.delete.delete_examples(db, auth, example_ids)


@retry_asyncpg
async def get_datasets(
    auth: AuthInfo,
    query_params: schemas.FilterQueryParamsForDatasetSchema,
) -> tuple[list[schemas.Dataset], int]:
    """Get all datasets depending on the owner."""
    async with database.asyncpg_conn() as db:
        return await models.datasets.fetch.fetch_datasets(db, auth, query_params)


@retry_asyncpg
async def get_public_dataset(
    auth: ShareDatasetInfo,
    query_params: schemas.QueryParamsForPublicDatasetSchema | None = None,
) -> schemas.Dataset:
    """Get all datasets depending on the owner."""
    query_params = query_params or schemas.QueryParamsForPublicDatasetSchema()
    async with database.asyncpg_conn() as db:
        rows, _ = await models.datasets.fetch.fetch_datasets(db, auth, query_params)
    if not rows:
        raise HTTPException(
            status_code=404,
            detail="Dataset not found.",
        )
    return rows[0]


@retry_asyncpg
async def get_dataset(auth: AuthInfo, dataset_id: UUID) -> schemas.Dataset:
    """Get a dataset by id."""
    async with database.asyncpg_conn() as db:
        return await models.datasets.fetch.fetch_dataset(db, auth, dataset_id)


def is_valid_key(s: str) -> bool:
    return bool(re.match(r"^[a-zA-Z0-9\-_]+$", s))


def _convert_ls_message_to_openai(message: dict) -> dict:
    """Convert a LS message to OpenAI format."""
    role = message["type"]
    if role == "human":
        role = "user"
    elif role == "ai":
        role = "assistant"
    data = message["data"]
    result = {"role": role, "content": data["content"]}
    if (data.get("additional_kwargs") or {}).get("function_call"):
        result["function_call"] = data["additional_kwargs"]["function_call"]
        if not result["content"]:
            del result["content"]
    return result


def _load_byte_rows_as_json(row: bytearray, fieldnames: Sequence[str]) -> List[dict]:
    # CSV to jsonl
    stringio = io.StringIO(row.decode("utf-8"))
    reader = csv.DictReader(stringio, fieldnames=fieldnames)
    return list(reader)


async def get_dataset_as_jsonl(
    auth: AuthInfo,
    dataset_id: UUID,
    write_stream: MemoryObjectSendStream[bytes],
    as_of: datetime | None,
    task_status: TaskStatus[None] = TASK_STATUS_IGNORED,
) -> None:
    """Download a dataset as a JSONL file."""
    # NOTE THIS DOESN'T WORK WITH ATTACHMENTS

    offset = 0
    batch_size = 100
    try:
        while True:
            examples, _ = await get_examples_json(
                auth,
                schemas.FilterQueryParamsForExampleSchema(
                    dataset=dataset_id,
                    limit=batch_size,
                    as_of=as_of or "latest",
                    offset=offset,
                    select=[
                        schemas.ExampleSelect.inputs,
                        schemas.ExampleSelect.outputs,
                        schemas.ExampleSelect.metadata,
                    ],
                    ignore_unselected=True,
                ),
            )
            if offset == 0:
                task_status.started()

            for example in examples:
                try:
                    await write_stream.send(example + "\n")
                except Exception as e:
                    await logger.aerror("Error writing example to stream", error=e)

            if not examples:
                break

            offset += batch_size
    finally:
        await write_stream.aclose()


async def get_dataset_as_openai_evals(
    auth: AuthInfo,
    dataset_id: UUID,
    write_stream: MemoryObjectSendStream[bytes],
    as_of: datetime | None,
    task_status: TaskStatus[None] = TASK_STATUS_IGNORED,
):
    """Download a dataset as OpenAI evals Jsonl format."""

    def transform_llm(example: dict) -> bytes:
        out_row = {
            "input": example["input_input"],
        }
        if example.get("output_output"):
            out_row["ideal"] = example["output_output"]
        else:
            out_row["ideal"] = None
        return orjson.dumps(out_row)

    def transform_chat(example: dict) -> bytes:
        inputs = orjson.loads(example["input_input"])
        outputs = (
            None
            if not example.get("output_output")
            else orjson.loads(example["output_output"])
        )
        # TODO: Not sure how to include function defs
        out_row = {
            "input": [_convert_ls_message_to_openai(message) for message in inputs],
            "ideal": (outputs["data"]["content"] if outputs else None),
        }
        return orjson.dumps(out_row)

    def transform_kv(example: dict) -> bytes:
        inputs = {}
        outputs = {}
        for key in example:
            if key.startswith("input_"):
                inputs[key[6:]] = example[key]
            elif key.startswith("output_"):
                outputs[key[7:]] = example[key]

        if len(inputs) != 1:
            raise HTTPException(
                status_code=400,
                detail="Invalid input format for example. "
                f"Expected 1 input key, got {len(inputs)}.",
            )
        if outputs and len(outputs) != 1:
            raise HTTPException(
                status_code=400,
                detail="Invalid output format for example "
                f"Expected 1 output key, got {len(outputs)}.",
            )
        out_row = {
            "input": next(iter(inputs.values())),
            "ideal": next(iter(outputs.values())) if outputs else None,
        }
        return orjson.dumps(out_row)

    has_written = False

    def transform(
        data_type, row: bytearray | List[dict], fieldnames: Sequence[str]
    ) -> bytes:
        nonlocal has_written
        examples = (
            _load_byte_rows_as_json(row, fieldnames=fieldnames)
            if isinstance(row, bytearray)
            else row
        )
        if data_type == "llm":
            fn = transform_llm
        elif data_type == "chat":
            fn = transform_chat
        else:
            fn = transform_kv
        response = b"\n".join([fn(example) for example in examples])
        if has_written:
            return b"\n" + response
        # Avoid the leading newline
        has_written = True
        return response

    return await get_dataset_as_csv(
        auth, dataset_id, write_stream, as_of, task_status, transform
    )


async def get_dataset_as_openai_ft(
    auth: AuthInfo,
    dataset_id: UUID,
    write_stream: MemoryObjectSendStream[bytes],
    as_of: datetime | None,
    task_status: TaskStatus[None] = TASK_STATUS_IGNORED,
):
    def transform_llm(example: dict) -> bytes:
        out_row = {
            "messages": [
                {"role": "user", "content": example["input_input"]},
            ]
        }
        if "output_output" in example:
            out_row["messages"].append(
                {"role": "assistant", "content": example["output_output"]}
            )
        return orjson.dumps(out_row)

    def transform_chat(example: dict) -> bytes:
        inputs = orjson.loads(example["input_input"])
        messages = inputs
        if "output_output" in example:
            outputs = orjson.loads(example["output_output"])
            if outputs:
                messages.append(outputs)
        out_row = {
            "messages": [_convert_ls_message_to_openai(message) for message in messages]
        }
        if "input_functions" in inputs:
            functions = orjson.loads(inputs["input_functions"])
            if functions:
                out_row["functions"] = functions
        return orjson.dumps(out_row)

    has_written = False

    def transform(
        data_type, row: bytearray | List[dict], fieldnames: Sequence[str]
    ) -> bytes:
        nonlocal has_written
        examples = (
            _load_byte_rows_as_json(row, fieldnames)
            if isinstance(row, bytearray)
            else row
        )
        if data_type == "llm":
            fn = transform_llm
        elif data_type == "chat":
            fn = transform_chat
        else:
            raise HTTPException(
                status_code=400,
                detail="KV datasets do not support export to OpenAI's fine-tuning format. "
                "Please use a chat or LLM dataset instead.",
            )
        response = b"\n".join([fn(example) for example in examples])
        if has_written:
            return b"\n" + response
        # Avoid the leading newline
        has_written = True
        return response

    return await get_dataset_as_csv(
        auth, dataset_id, write_stream, as_of, task_status, transform
    )


# This one cannot be retried as it would potentially send repeated chunks to the client
async def get_dataset_as_csv(
    auth: AuthInfo,
    dataset_id: UUID,
    write_stream: MemoryObjectSendStream[bytes],
    as_of: datetime | None,
    task_status: TaskStatus[None] = TASK_STATUS_IGNORED,
    format_response: (
        Callable[[str, Union[bytearray, List[dict]], Sequence[str]], bytes] | None
    ) = None,
) -> None:
    """Get the dataset as a CSV file."""
    modified_at = as_of or datetime.now(timezone.utc)
    async with database.asyncpg_conn() as db:
        key_set_query = """
        WITH latest_examples AS (
            SELECT
                id,
                max(modified_at) FILTER (WHERE modified_at <= $3) AS latest_modified_at
            FROM examples_log
            WHERE dataset_id = $1
            GROUP BY id
        ),
        recent_examples AS (
            SELECT
                examples_log.id,
                examples_log.inputs,
                examples_log.outputs
            FROM examples_log
            INNER JOIN latest_examples
            ON examples_log.id = latest_examples.id
            AND examples_log.modified_at = latest_examples.latest_modified_at
            WHERE examples_log.dataset_id = $1
            AND examples_log.inputs IS NOT NULL
        )

        SELECT
            ARRAY(
                SELECT DISTINCT jsonb_object_keys(inputs)
                FROM recent_examples
                ORDER BY jsonb_object_keys(inputs)
            ) AS input_key_set,
            ARRAY(
                SELECT DISTINCT jsonb_object_keys(outputs)
                FROM recent_examples
                ORDER BY jsonb_object_keys(outputs)
            ) AS output_key_set,
            data_type
        FROM dataset
        WHERE id = $1 AND tenant_id = $2
        LIMIT 1;
        """

        key_set_results = await db.fetchrow(
            key_set_query, dataset_id, auth.tenant_id, modified_at
        )
        if not key_set_results:
            raise HTTPException(
                status_code=404,
                detail="Dataset not found.",
            )
        input_keys = key_set_results["input_key_set"]
        output_keys = key_set_results["output_key_set"]
        data_type = key_set_results["data_type"]

        for key in input_keys + output_keys:
            if not is_valid_key(key):
                raise HTTPException(
                    status_code=400,
                    detail=f"Dataset has invalid key for exporting: {key}",
                )

        input_selectors = [
            f"examples_log.inputs->>'{key}' AS input_{key}" for key in input_keys
        ]
        output_selectors = [
            f"examples_log.outputs->>'{key}' AS output_{key}" for key in output_keys
        ]

        all_selectors = ", ".join(input_selectors + output_selectors)
        query = f"""
        SELECT {all_selectors}
        FROM (
            SELECT
                id,
                max(modified_at) FILTER (WHERE modified_at <= $3) as modified_at
                from examples_log
                where dataset_id = $1
            group by 1
            {{limit_stmt}}
        ) as latest
        INNER JOIN examples_log ON examples_log.id = latest.id AND examples_log.modified_at = latest.modified_at AND examples_log.inputs is not null
        INNER JOIN dataset ON examples_log.dataset_id = dataset.id
        WHERE dataset.id = $1
        AND dataset.tenant_id = $2
        """

        all_fieldnames = [f"input_{key}" for key in input_keys] + [
            f"output_{key}" for key in output_keys
        ]

        if format_response is not None:
            # Check that the first row conforms to the expected format
            records = await db.fetch(
                query.format(limit_stmt="LIMIT 1"),
                dataset_id,
                auth.tenant_id,
                modified_at,
            )
            records = [dict(record) for record in records]
            format_response(
                data_type,
                records,
                all_fieldnames,
            )
        # All errors henceforth will be treated as 200's with truncated data
        task_status.started()

        format_response_ = cast(
            Callable[[str, Union[bytearray, List[dict]]], bytes],
            (
                functools.partial(format_response, fieldnames=all_fieldnames)  # type: ignore
                if format_response is not None
                else (lambda _, ba: bytes(cast(bytearray, ba)))
            ),
        )

        async def write(ba: bytearray):
            try:
                write_bytes = format_response_(data_type, ba)
                await write_stream.send(write_bytes)
            except (Exception, asyncio.CancelledError):
                await write_stream.aclose()
                raise

        headers = format_response is None
        await db.copy_from_query(
            query.format(limit_stmt=""),
            dataset_id,
            auth.tenant_id,
            modified_at,
            output=write,
            format="csv",
            header=headers,
        )
    await write_stream.aclose()


@retry_asyncpg
async def create_dataset(
    auth: AuthInfo, dataset: schemas.DatasetCreate
) -> schemas.Dataset:
    """Create a dataset depending on the owner."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await create_dataset_with_existing_transaction(db, auth, dataset)


async def create_dataset_with_existing_transaction(
    db: asyncpg.Connection,
    auth: AuthInfo,
    dataset: schemas.DatasetCreate,
) -> schemas.Dataset:
    """Create a dataset depending on the owner."""
    dataset.name = dataset.name.strip()
    if dataset.data_type != schemas.DataType.kv:
        if (
            dataset.inputs_schema_definition is not None
            or dataset.outputs_schema_definition is not None
        ):
            raise HTTPException(
                status_code=400,
                detail="Schema definition is only supported for KV datasets.",
            )

    validate_transformations_format(dataset.transformations or [])
    for field, schema in [
        ("inputs", dataset.inputs_schema_definition),
        ("outputs", dataset.outputs_schema_definition),
    ]:
        tforms_for_field = [
            t for t in dataset.transformations or [] if t.path[0] == field
        ]
        if schema is not None:
            validate_dataset_schema_and_transformations(field, schema, tforms_for_field)
        elif tforms_for_field:
            raise HTTPException(
                status_code=400,
                detail="Transformations are only supported on defined schemas.",
            )

    try:
        raw_query = """
            INSERT INTO dataset (id, name, description, created_at, modified_at, data_type, tenant_id, extra, inputs_schema_definition, outputs_schema_definition, externally_managed)
            VALUES ($id, $name, $description, $created_at, $created_at, $data_type, $tenant_id, $extra, $inputs_schema_definition, $outputs_schema_definition, $externally_managed)
            RETURNING *;
        """
        params = {**dataset.model_dump(), "tenant_id": auth.tenant_id}
        if params.get("id") is None:
            params["id"] = uuid4()
        params["created_at"] = params["created_at"] or datetime.now(timezone.utc)
        if params["created_at"].tzinfo is None:
            params["created_at"] = params["created_at"].replace(tzinfo=timezone.utc)
        sql_query = database.kwargs_to_pgpos(raw_query, params)
        db_dataset = await db.fetchrow(sql_query.sql, *sql_query.args)
    except asyncpg.UniqueViolationError:
        raise HTTPException(
            status_code=409,
            detail="Dataset with this name already exists.",
        )

    transformations = []
    if dataset.transformations:
        transformations_raw = await db.fetchmany(
            """
            INSERT INTO dataset_transformations (dataset_id, path, transformation_type)
            VALUES ($1, $2::jsonb, $3)
            RETURNING path, transformation_type;
            """,
            [
                (
                    db_dataset["id"],
                    transformation.path,
                    transformation.transformation_type.value,
                )
                for transformation in dataset.transformations
            ],
        )
        transformations = [
            schemas.DatasetTransformation(**transformation)
            for transformation in transformations_raw
        ]

    return schemas.Dataset(
        **{
            **db_dataset,
            "transformations": transformations if dataset.transformations else None,
        },
        example_count=0,
        session_count=0,
    )


@retry_asyncpg
async def update_dataset(
    auth: AuthInfo,
    dataset_id: UUID,
    dataset_update: schemas.DatasetUpdate,
) -> Tuple[schemas.Dataset, int]:
    """Update a dataset."""
    try:
        set_clauses = []
        param_dict = {}
        fields = [
            "name",
            "description",
            "inputs_schema_definition",
            "outputs_schema_definition",
        ]

        for field in fields:
            value = getattr(dataset_update, field)
            if not isinstance(value, schemas.Missing):
                param_dict[field] = value
                set_clauses.append(f"{field} = ${field}")

        set_clauses.append("modified_at = now()")

        query = (
            f"UPDATE dataset SET {', '.join(set_clauses)} "
            f"WHERE id = $dataset_id AND tenant_id = $tenant_id RETURNING *;"
        )

        param_dict["dataset_id"] = dataset_id
        param_dict["tenant_id"] = auth.tenant_id

        sql_query = database.kwargs_to_pgpos(query, param_dict)

        async with database.asyncpg_conn() as db, db.transaction():
            existing_dataset = await models.datasets.fetch.fetch_dataset(
                db, auth, dataset_id
            )

            if existing_dataset is None:
                raise HTTPException(status_code=404, detail="Dataset not found")

            served_dataset_info = await get_indexed_dataset_opt_within_txn(
                db, dataset_id, auth.tenant_id
            )
            if served_dataset_info is not None and not isinstance(
                getattr(dataset_update, "inputs_schema_definition", None),
                schemas.Missing,
            ):
                raise HTTPException(
                    status_code=400,
                    detail="Cannot update input schema definition of a dataset that is indexed for few shot examples. Please stop indexing the dataset before changing the input schema.",
                )

            if not isinstance(dataset_update.transformations, schemas.Missing):
                # Delete all existing transformations
                await db.execute(
                    "DELETE FROM dataset_transformations WHERE dataset_id = $1",
                    dataset_id,
                )

                # Insert new transformations if any exist
                if dataset_update.transformations is not None:
                    await db.executemany(
                        """
                        INSERT INTO dataset_transformations (dataset_id, path, transformation_type)
                        VALUES ($1, $2::jsonb, $3)
                        """,
                        [
                            (
                                dataset_id,
                                transformation.path,
                                transformation.transformation_type.value,
                            )
                            for transformation in dataset_update.transformations
                        ],
                    )

            updated_examples = []
            if (
                not isinstance(dataset_update.inputs_schema_definition, schemas.Missing)
                or not isinstance(
                    dataset_update.outputs_schema_definition, schemas.Missing
                )
                or not isinstance(dataset_update.transformations, schemas.Missing)
            ):
                input_schema = (
                    existing_dataset.inputs_schema_definition
                    if isinstance(
                        dataset_update.inputs_schema_definition, schemas.Missing
                    )
                    else dataset_update.inputs_schema_definition
                )
                output_schema = (
                    existing_dataset.outputs_schema_definition
                    if isinstance(
                        dataset_update.outputs_schema_definition, schemas.Missing
                    )
                    else dataset_update.outputs_schema_definition
                )
                transformations = (
                    existing_dataset.transformations
                    if isinstance(dataset_update.transformations, schemas.Missing)
                    else dataset_update.transformations
                )
                updated_examples = await verify_existing_examples_and_maybe_update_with_existing_transaction(
                    auth,
                    db,
                    existing_dataset,
                    input_schema,
                    output_schema,
                    dataset_update.patch_examples,
                    transformations,
                )
            updated_dataset_row = await db.fetchrow(sql_query.sql, *sql_query.args)
            if updated_dataset_row is None:
                raise HTTPException(status_code=404, detail="Dataset not found")
            return await models.datasets.fetch.fetch_dataset(db, auth, dataset_id), len(
                updated_examples
            )
    except asyncpg.UniqueViolationError:
        raise HTTPException(
            status_code=409, detail="Dataset with this name already exists."
        )


EXAMPLE_BATCH_SIZE = 100


async def verify_existing_examples_and_maybe_update_with_existing_transaction(
    auth: AuthInfo,
    db: asyncpg.Connection,
    dataset: schemas.Dataset,
    inputs_schema_definition: dict | None,
    outputs_schema_definition: dict | None,
    patch_examples: dict[UUID, schemas.ExampleUpdate] | None,
    transformations: List[schemas.DatasetTransformation] | None,
) -> list[dict]:
    if inputs_schema_definition is None and outputs_schema_definition is None:
        if transformations is not None and len(transformations) > 0:
            raise HTTPException(
                status_code=400,
                detail="Transformations are only supported on defined schemas.",
            )

        return []
    if dataset.data_type != schemas.DataType.kv:
        raise HTTPException(
            status_code=400,
            detail="Schema definition is only supported for KV datasets.",
        )

    validate_transformations_format(transformations or [])
    for field, schema in [
        ("inputs", inputs_schema_definition),
        ("outputs", outputs_schema_definition),
    ]:
        tforms_for_field = [t for t in transformations or [] if t.path[0] == field]
        if schema is not None:
            validate_dataset_schema_and_transformations(field, schema, tforms_for_field)
        elif tforms_for_field:
            raise HTTPException(
                status_code=400,
                detail="Transformations are only supported on defined schemas.",
            )

    # Handle transformations update
    await db.execute(
        """
        DELETE FROM dataset_transformations
        WHERE dataset_id = $1
        """,
        dataset.id,
    )
    if transformations:
        await db.executemany(
            """
            INSERT INTO dataset_transformations (dataset_id, path, transformation_type)
            VALUES ($1, $2::jsonb, $3)
            """,
            [
                (
                    dataset.id,
                    transformation.path,
                    transformation.transformation_type.value,
                )
                for transformation in transformations
            ],
        )

    non_conforming_examples = []
    offset = 0
    # Check that all existing examples conform to the new schema definition
    while True:
        examples, _ = await get_examples(
            auth,
            schemas.FilterQueryParamsForExampleSchema(
                dataset=dataset.id, offset=offset, limit=EXAMPLE_BATCH_SIZE
            ),
        )
        for example in examples:
            coalesced_example = example.model_dump()
            if patch_examples is not None and example.id in patch_examples:
                for key, value in patch_examples[example.id].model_dump().items():
                    if value is not None:
                        coalesced_example[key] = value
            try:
                validate_example(
                    dataset.data_type,
                    schemas.ExampleUpdate(**coalesced_example),
                    inputs_schema_definition,
                    outputs_schema_definition,
                    # Note: we purposefully do not apply transformations on dataset update
                    #       because transformations should only apply to _new_ examples in
                    #       the dataset. Customers can copy all examples to apply transformations
                    #       to a new dataset with transformations enabled if needed.
                    transformations=[],
                )
            except ExampleValidationError:
                non_conforming_examples.append(str(example.id))
            except CombinedExampleValidationError:
                non_conforming_examples.append(str(example.id))

        if len(examples) < EXAMPLE_BATCH_SIZE:
            break
        offset += EXAMPLE_BATCH_SIZE

    if non_conforming_examples:
        raise HTTPException(
            status_code=400,
            detail={
                "message": f"{len(non_conforming_examples)} examples do not conform to the new schema definition.",
                "non_conforming_examples": non_conforming_examples,
            },
        )

    if patch_examples is None:
        return []

    # Update all examples in patch_examples now that we know they all conform
    updated_examples_ret = []
    for example_id, updated_example in patch_examples.items():
        updated = await models.examples.create.update_example_raw(
            db, auth, example_id, updated_example, full_overwrite=False
        )
        updated_examples_ret.append(updated)
    return updated_examples_ret


@retry_asyncpg
async def create_dataset_from_csv(
    auth: AuthInfo,
    file: tempfile.SpooledTemporaryFile,
    name: str,
    input_keys: List[str],
    output_keys: List[str],
    description: Optional[str],
    data_type: schemas.DataType,
) -> schemas.Dataset:
    """Create a dataset from the given csv file by creating examples."""
    async with database.asyncpg_conn() as db, db.transaction():
        dataset_id = uuid4()
        dataset_row = await db.fetchrow(
            """
            INSERT INTO dataset (id, name, tenant_id, description, data_type, created_at, modified_at)
            VALUES ($1, $2, $3, $4, $5, $6, $6)
            RETURNING *;
            """,
            dataset_id,
            name,
            auth.tenant_id,
            description,
            data_type,
            datetime.now(timezone.utc),
        )
        await models.examples.create.create_examples_from_csv(
            db, auth, dataset_id, file, input_keys, output_keys
        )
        return schemas.Dataset(
            **dataset_row,
            example_count=0,
            session_count=0,
            last_session_start_time=None,
        )


@retry_asyncpg
async def create_examples_from_csv(
    auth: AuthInfo,
    dataset_id: UUID,
    file: tempfile.SpooledTemporaryFile,
    input_keys: List[str],
    output_keys: List[str],
) -> list[dict[str, Any]]:
    """Create a dataset from the given csv file by creating examples."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await models.examples.create.create_examples_from_csv(
            db, auth, dataset_id, file, input_keys, output_keys
        )


@retry_asyncpg
async def delete_dataset(auth: AuthInfo, dataset_id: UUID) -> None:
    """Delete a dataset."""

    # Remove dataset indexes outside of the delete dataset transaction
    async with database.asyncpg_conn() as db:
        served_dataset_for_dataset_opt = await get_indexed_dataset_opt_within_txn(
            db,
            dataset_id,
            auth.tenant_id,
        )

    if served_dataset_for_dataset_opt is not None:
        await remove_index_for_dataset(auth, dataset_id)

    # perform dataset specific info within the transaction
    async with database.asyncpg_conn() as db, db.transaction():
        db_dataset = await models.datasets.fetch.fetch_dataset(db, auth, dataset_id)
        if db_dataset is None:
            raise HTTPException(status_code=404, detail="Dataset not found")

        delete_stmt = "DELETE FROM dataset WHERE id = $1 AND tenant_id = $2"
        await db.execute(delete_stmt, dataset_id, auth.tenant_id)

        # deleting projects is not supported yet with Azure
        if settings.BLOB_STORAGE_ENGINE != "Azure":
            session_ids_to_delete_stmt = "SELECT id FROM tracer_session WHERE reference_dataset_id = $1 AND tenant_id = $2"
            rows = await db.fetch(
                session_ids_to_delete_stmt, dataset_id, auth.tenant_id
            )
            if rows:
                await delete_tracer_sessions_within_transaction(
                    db, auth, [UUID(str(r.id)) for r in rows]
                )


@overload
async def get_run(
    auth: AuthInfo,
    run_id: UUID,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
    exclude_select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
) -> schemas.RunSchema: ...


@overload
async def get_run(
    auth: ShareDatasetInfo,
    run_id: UUID,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
    exclude_select: list[schemas.RunSelect] | None = None,
) -> schemas.RunPublicDatasetSchema: ...


@overload
async def get_run(
    auth: ShareRunInfo,
    run_id: UUID,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
    exclude_select: list[schemas.RunSelect] | None = None,
) -> schemas.RunPublicSchema: ...


async def get_run(
    auth: AuthInfo | ShareDatasetInfo | ShareRunInfo,
    run_id: UUID,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
    exclude_select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
) -> schemas.RunSchema | schemas.RunPublicSchema | schemas.RunPublicDatasetSchema:
    if exclude_select is None:
        exclude_select = []
    run = await models.runs.fetch_ch.fetch_and_assert_run(
        auth,
        run_id,
        [e for e in schemas.RunSelect if e not in exclude_select],
        include_feedback=include_feedback,
        session_id=session_id,
        start_time=start_time,
        max_threads=settings.FETCH_RUNS_API_MAX_THREADS
        if not is_user_auth(auth)
        else None,
    )
    if isinstance(auth, AuthInfo):
        return schemas.RunSchema.model_validate(run)
    elif isinstance(auth, ShareDatasetInfo):
        return schemas.RunPublicDatasetSchema.model_validate(run)
    else:
        return schemas.RunPublicSchema.model_validate(run)


def write_queue_reject_key():
    today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    return f"smith:runs:reject:{today}"


class QueuePayload(NamedTuple):
    run_id: UUID
    parent_id: UUID | None
    trace_id: UUID | None
    value: bytes
    content_type: str = "application/json"
    hash_key: str | None = None
    set_key: str | None = None
    process_inline: bool = False
    session_id: UUID | None = None
    session_name: str | None = None
    start_time: str | None = None
    extra: Mapping[str, str | tuple[str, str, bytes]] | None = None
    auto_upgrade: bool = False
    """
    extra holds attachments for `value`:
    - key is the name of the attachment (eg. inputs, outputs)
    - value is either
        - string s3 url where object is/will be stored
        - tuple of content type, attachment payload
    """

    def session_hash_str(self) -> str:
        return orjson.dumps(
            {"session_id": self.session_id, "session_name": self.session_name}
        ).decode("utf-8")


async def ensure_sessions_before_queue_run_payload(
    auth: AuthInfo | FeedbackTokenInfo,
    payloads: list[QueuePayload],
    received_at: str | None = None,
) -> None:
    received_at = received_at or datetime.now(timezone.utc).isoformat()
    sessions_semaphore = asyncio.Semaphore(settings.INGEST_PROJECT_CREATION_SEMAPHORE)
    session_to_earliest_payload_map: dict[str, QueuePayload] = {}

    def _get_start_time(payload: QueuePayload) -> str:
        return payload.start_time or received_at

    payloads_with_sessions = [
        payload for payload in payloads if payload.hash_key == "post"
    ]
    reverse_sorted_payloads = sorted(
        payloads_with_sessions,
        key=lambda x: _get_start_time(x),
        reverse=True,
    )
    for payload in reverse_sorted_payloads:
        session_hash = payload.session_hash_str()
        session_to_earliest_payload_map[session_hash] = payload

    # Create the sessions. Note we don't turn on return_exceptions here
    # because we ultimately want to throw RBAC errors here if they occur
    #
    # TODO: do not create tracer sessions if the run will be rejected
    tracer_sessions = await asyncio.gather(
        *(
            gated_coro(
                start_or_fetch_tracer_session(
                    auth,
                    session_id=payload.session_id,
                    session_name=payload.session_name,
                    start_time_str=_get_start_time(payload),
                ),
                sessions_semaphore,
            )
            for payload in session_to_earliest_payload_map.values()
        )
    )

    if settings.FF_TRACE_TIERS_ENABLED and (
        any(
            session.trace_tier == schemas.TraceTier.longlived
            for session in tracer_sessions
        )
        or any(payloads.auto_upgrade for payloads in payloads)
    ):
        await check_longlived_usage_limits(auth)


async def process_payloads(
    auth: AuthInfo | FeedbackTokenInfo,
    trace_ids: set[UUID] | None,
    payloads: list[QueuePayload],
    aredis: AsyncRedis,
    auth_id: UUID,
    received_at: bytes,
    should_check_limits: bool,
    use_inline_processing: bool,
    queued_run_set_for_auth: str,
    affects_shortterm_limits: bool,
    processed_traces: SeenTraces = SeenTraces(),
) -> None:
    redis_cluster_ingestion_enabled = redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )
    use_redis_transaction = not redis_cluster_ingestion_enabled

    async with (
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
        AsyncExitStack() as exit,
    ):
        # read/write
        compressed_payloads_and_methods = await asyncio.gather(
            *(
                (
                    compress_based_on_size("", payload.value, exit)
                    if payload.hash_key is not None
                    else completed_future((payload.value, None))
                )
                for payload in payloads
            )
        )
        compressed_extras = await asyncio.gather(
            *(
                compress_based_on_size_many(
                    {k: v[1:] for k, v in payload.extra.items() if isinstance(v, tuple)}
                    if payload.extra
                    else {},
                    exit,
                )
                for payload in payloads
            )
        )

        if not use_inline_processing and trace_ids:
            if settings.REDIS_USE_SMISMEMBER:
                # can use a set (not list) of trace IDs because order of results doesn't matter
                pipe.smismember(
                    queued_run_set_for_auth, {str(trace_id) for trace_id in trace_ids}
                )
            else:
                # check the queued runs set
                for trace_id in trace_ids:
                    pipe.sismember(queued_run_set_for_auth, str(trace_id))

        # track total compressed size including extras
        compressed_payload_size = 0
        for payload, (value, compression_method), extra in zip(
            payloads, compressed_payloads_and_methods, compressed_extras
        ):
            compressed_payload_size += len(value)

            # store the payload
            pending_key = f"smith:runs:pending:{auth_id}:{payload.run_id}"
            if payload.hash_key is not None:
                pipe.hsetnx(pending_key, payload.hash_key, value)
                pipe.hsetnx(pending_key, payload.hash_key + "_received_at", received_at)
                pipe.hsetnx(
                    pending_key,
                    payload.hash_key + "_content_type",
                    payload.content_type,
                )
                pipe.hsetnx(
                    pending_key,
                    payload.hash_key + "_compression_method",
                    orjson.dumps(compression_method),
                )
                pipe.expire(pending_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
            if payload.extra:
                extra_key = f"{pending_key}:extra"
                for key in payload.extra:
                    if key in extra:
                        val, compression_method = extra[key]
                        compressed_payload_size += len(val)
                        mapping: Mapping[str | bytes, bytes | float | int | str] = {
                            key: val,
                            key + "_content_type": payload.extra[key][0],
                            key + "_compression_method": compression_method,
                        }
                        pipe.hset(extra_key, mapping=mapping)
                    else:
                        pipe.hset(
                            extra_key,
                            key + "_s3_url",
                            cast(str, payload.extra[key]),
                        )
                pipe.expire(extra_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
            if payload.set_key is not None:
                full_set_key = (
                    f"smith:runs:{payload.set_key}:{auth_id}:{payload.run_id}"
                )
                pipe.sadd(full_set_key, value)
                pipe.expire(full_set_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
            # store the parent-child relationship
            if payload.parent_id is not None:
                pipe.hsetnx(pending_key, "parent", orjson.dumps(payload.parent_id))
                children_key = f"smith:runs:children:{auth_id}:{payload.parent_id}"
                pipe.sadd(children_key, orjson.dumps(payload.run_id))
                pipe.expire(children_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
            if payload.trace_id is not None:
                pipe.hsetnx(pending_key, "trace_id", orjson.dumps(payload.trace_id))
            if settings.FF_TRACE_TIERS_ENABLED and (
                payload.trace_id or len(payloads) == 1 and trace_ids
            ):
                # save trace_ids and first_received_at for the trace
                # we may not have the trace on the non batch payload but it would be in the trace_ids
                trace_id = payload.trace_id or list(cast(set[UUID], trace_ids))[0]
                trace_pending_key = f"smith:runs:pending:{auth_id}:{trace_id}"
                trace_runs_key = f"smith:runs:trace_runs:{auth_id}:{trace_id}"
                pipe.hsetnx(trace_pending_key, "trace_first_received_at", received_at)
                pipe.expire(
                    trace_pending_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS
                )
                # track list of runs for trace
                pipe.sadd(trace_runs_key, orjson.dumps(payload.run_id))
                pipe.expire(trace_runs_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
                if (
                    payload.auto_upgrade
                    and trace_id not in processed_traces.traceUpgradeSet
                ):
                    logger.info(
                        "Auto upgrading trace tier",
                        trace_id=trace_id,
                        run_id=payload.run_id,
                    )
                    # auto upgrade trace tier
                    pipe.hset(
                        trace_pending_key,
                        "upgrade_trace_tier",
                        orjson.dumps("longlived"),
                    )
                    processed_traces.traceUpgradeSet.add(trace_id)

        # set payload size on parent Datadog span
        if settings.DATADOG_ENABLED and (span := tracer.current_span()):
            span.set_tag_str("compressed_payload_size", str(compressed_payload_size))

        if should_check_limits and affects_shortterm_limits:
            # increment the total payload size per hour counter
            payload_size_incr = sum(
                len(p.value)
                + (
                    sum(len(v[1]) for v in p.extra.values() if isinstance(v, tuple))
                    if p.extra
                    else 0
                )
                for p in payloads
            )
            payload_size_key = usage_limit_payload_size_per_hour_counter_key(auth_id)
            pipe.incrby(payload_size_key, payload_size_incr)
            pipe.expire(payload_size_key, config.settings.REDIS_RUNS_EXPIRY_SECONDS)
            # increment the total requests per hour counter
            total_requests_per_hour_key = (
                usage_limit_events_ingested_per_hour_counter_key(auth_id)
            )
            pipe.incrby(total_requests_per_hour_key, len(payloads))
            pipe.expire(
                total_requests_per_hour_key,
                config.settings.REDIS_RUNS_EXPIRY_SECONDS,
            )
            # increment the total requests per minute counter
            total_requests_per_minute_key = (
                usage_limit_events_ingested_per_minute_counter_key(auth_id)
            )
            pipe.incrby(total_requests_per_minute_key, len(payloads))
            pipe.expire(total_requests_per_minute_key, 60 * 5)

        if should_check_limits and trace_ids:
            # add trace_ids to hll
            hll_key = user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                auth_id
            )
            pipe.pfadd(hll_key, *(orjson.dumps(trace_id) for trace_id in trace_ids))
            pipe.expireat(hll_key, int(start_of_next_month_timestamp()))

            for limit in user_defined_limits.UsageLimitUnit.TRACES.get_all_limits():
                limit.mark_seen_events_in_pipe(trace_ids, pipe, auth_id)

        if use_inline_processing:
            async with (
                redis.async_routed_queue(
                    settings.INGESTION_QUEUE, str(auth_id)
                ) as queue,
                queue.redis.pipeline(
                    transaction=use_redis_transaction
                ) as queue_pipeline,
            ):
                await queue.enqueue(
                    "persist_batched_runs",
                    pipeline=pipe
                    if not redis_cluster_ingestion_enabled
                    else queue_pipeline,
                    run_ids=[[payload.trace_id, payload.run_id] for payload in payloads]
                    if settings.FF_TRACE_TIERS_ENABLED
                    else [payload.run_id for payload in payloads],  # type: ignore
                    auth_dict=auth.model_dump(),
                    api_key_short=structlog.contextvars.get_contextvars().get(
                        "api_key_short", ""
                    ),
                    lb_trace_context=structlog.contextvars.get_contextvars().get(
                        "lb_trace_context"
                    ),
                    **redis.INGEST_JOB_KWARGS,
                )
                # execute pipeline
                # must be in async with context, otherwise sharded_pipe is reset
                results = [
                    await redis.execute_write_pipeline(
                        auth_id, pipe, redis.RedisOperationType.INGESTION
                    )
                ]
                # In cluster mode use a separate pipeline for enqueueing since SAQ doesn't support Redis Cluster.
                # Make sure to do the enqueue after payloads are written to Redis.
                if redis_cluster_ingestion_enabled:
                    await queue_pipeline.execute()
        else:
            results = [
                await redis.execute_write_pipeline(
                    auth_id, pipe, redis.RedisOperationType.INGESTION
                )
            ]

        # original pipe remains correct, and is the result we want to read, because of dual-writing.
        # this is only true becuase we aren't reading the enqueue results, which may be on the sharded pipe.
        pipeline_result = results[0] if results else None

    # if the trace id is not present, we wait for the parent to be queued
    if not trace_ids:
        return

    if not use_inline_processing:
        # if inline not schedule them here, otherwise inline processing jobs already queued in Redis pipeline
        jobs: list[EnqueueData] = []
        # if using smismember, first element is a list of the statuses.
        #   if not, first len(trace_ids) elements are the statuses.
        statuses = (
            pipeline_result[0]
            if pipeline_result and settings.REDIS_USE_SMISMEMBER
            else pipeline_result[: len(trace_ids)]
            if pipeline_result and not settings.REDIS_USE_SMISMEMBER
            else []
        )
        if all(status == TASK_IN_QUEUED_SET for status in statuses):
            # tasks currently in the queue, no need to schedule again
            await logger.ainfo(
                "All traces %s already in queue, skipping", str(trace_ids)
            )
            return

        # Figure out the tasks to schedule, only schedule if they are not already in the queued runs set.
        job_ids_to_queue = []
        for task_run_id, status in zip(trace_ids, statuses):
            if status != TASK_IN_QUEUED_SET:
                jobs.append(
                    redis.JobEnqueue(
                        func="persist_run_or_parent",
                        kwargs={
                            "auth_id": auth_id,
                            "run_id": task_run_id,
                            "auth_dict": auth.model_dump(),
                            "api_key_short": structlog.contextvars.get_contextvars().get(
                                "api_key_short", ""
                            ),
                            "lb_trace_context": structlog.contextvars.get_contextvars().get(
                                "lb_trace_context"
                            ),
                        },
                    )
                )
                job_ids_to_queue.append(str(task_run_id))
            else:
                await logger.ainfo(
                    "Task runs %s already in queue, skipping", str(task_run_id)
                )

        # Enqueue the tasks, and add trace_ids to the queued set for tracking.
        # This is done in a transactional pipeline to make sure it's atomic.
        # This will help track what is pending execution and prevent duplicate work.
        # We enqueue to a single shard to avoid dual-processing.
        async with (
            redis.aredis_routed_pool(
                str(auth_id), redis.RedisOperation.ENQUEUE
            ) as aredis_shard,
            aredis_shard.pipeline(transaction=use_redis_transaction) as enqueue_pipe,
        ):
            async with (
                redis.async_routed_queue(
                    settings.INGESTION_QUEUE, str(auth_id)
                ) as queue,
                queue.redis.pipeline(
                    transaction=use_redis_transaction
                ) as queue_pipeline,
            ):
                for job in jobs:
                    await queue.enqueue(
                        job.func,
                        pipeline=enqueue_pipe
                        if not redis_cluster_ingestion_enabled
                        else queue_pipeline,
                        **job.kwargs,
                        **redis.INGEST_JOB_KWARGS,
                    )

                enqueue_pipe.sadd(queued_run_set_for_auth, *job_ids_to_queue)
                enqueue_pipe.expire(queued_run_set_for_auth, QUEUED_RUN_SET_EXPIRATION)
                await enqueue_pipe.execute()

                # In cluster mode use a separate pipeline for enqueueing since SAQ doesn't support Redis Cluster.
                # Make sure to do the enqueue after payloads are written to Redis.
                if redis_cluster_ingestion_enabled:
                    await queue_pipeline.execute()


async def queue_run_payload(
    auth: AuthInfo | FeedbackTokenInfo,
    # trace_ids is None for batch/multipart and computed downstream
    trace_ids: set[UUID] | None,
    payloads: list[QueuePayload],
    affects_shortterm_limits: bool = True,
) -> None:
    """Queue a run to be persisted."""
    for payload in payloads:
        if payload.hash_key is not None:
            if payload.hash_key not in ("post", "patch"):
                raise ValueError(f"Invalid value: {payload.hash_key}")
        elif payload.set_key is not None:
            if payload.set_key not in ("feedback",):
                raise ValueError(f"Invalid value: {payload.set_key}")
            if payload.extra is not None:
                raise ValueError("extra is not allowed for set_key")
            if payload.content_type != "application/json":
                raise ValueError("content_type must be application/json for set_key")
        else:
            raise ValueError("One of hash_key or set_key should be set")

        if payload.parent_id == payload.run_id:
            raise HTTPException(
                status_code=400, detail="parent_id and run_id cannot be the same"
            )

    received_at = orjson.dumps(datetime.now(timezone.utc))

    # Authentication:
    # We want to prevent two kinds of attacks:
    #   1. A tenant can't write runs for another tenant
    #   2. A tenant can't prevent another tenant from writing runs (DoS)
    # To prevent 1, we verify auth before persisting any run in the tree.
    # To prevent 2, we namespace all redis data by a stable hash of the auth.
    # That prevents malicious attempts to write runs for another tenant from
    # overwriting valid payloads.
    # This enforces that all payloads received for any run in the tree must
    # have the same auth, and that auth must be valid.
    auth_id = auth.tenant_id
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(str(auth_id))
    async with redis.aredis_routed_pool(
        str(auth_id), redis.RedisOperation.READ
    ) as aredis:
        # short circuit if payload already received
        if hash_payloads := [p for p in payloads if p.hash_key is not None]:
            async with aredis.pipeline(transaction=use_redis_transaction) as pipe:
                # readonly
                for payload in hash_payloads:
                    pending_key = f"smith:runs:pending:{auth_id}:{payload.run_id}"
                    pipe.hget(pending_key, cast(str, f"{payload.hash_key}_received_at"))
                existing = await pipe.execute()

            if len(existing) and any(existing):
                for hash_payload, exists in zip(hash_payloads, existing):
                    if exists:
                        payloads.remove(hash_payload)
                if not payloads:
                    # all payloads already received
                    raise HTTPException(
                        status_code=409,
                        detail="Payloads already received",
                    )

        unique_trace_ids = {p.trace_id for p in payloads if p.trace_id is not None}
        if unique_trace_ids:
            async with aredis.pipeline(transaction=use_redis_transaction) as pipe:
                for tid in unique_trace_ids:
                    trace_key = f"smith:runs:trace_runs:{auth_id}:{tid}"
                    pipe.scard(trace_key)
                trace_counts = await pipe.execute()
            unique_trace_ids_list = list(unique_trace_ids)
            for tid, count in zip(unique_trace_ids_list, trace_counts):
                if count >= settings.MAX_TRACE_LIMIT:
                    logger.info(
                        "Trace count exceeds MAX_TRACE_SIZE",
                        trace_count=count,
                        max_runs=settings.MAX_TRACE_LIMIT,
                        trace_id=tid,
                    )
                    raise HTTPException(
                        status_code=422,
                        detail=(
                            f"Trace count {count} exceeds max trace limit of "
                            f"{settings.MAX_TRACE_LIMIT} for trace {tid}"
                        ),
                    )

        # find the root run id a.k.a. trace ID
        if len(payloads) == 1:
            if trace_ids is not None:
                pass
            elif payloads[0].parent_id is None and payloads[0].hash_key == "post":
                trace_ids = {payloads[0].run_id}
            else:
                if payloads[0].parent_id is not None:
                    current_id = payloads[0].parent_id
                    seen = {payloads[0].run_id}
                else:
                    current_id = payloads[0].run_id
                    seen = set[UUID]()
                for _ in range(1000):
                    # detect cycles and break
                    if current_id in seen:
                        raise HTTPException(
                            status_code=400,
                            detail="Cycle detected in run tree",
                        )
                    else:
                        seen.add(current_id)
                    # fetch next parent
                    async with aredis.pipeline(
                        transaction=use_redis_transaction
                    ) as pipe:
                        # readonly
                        hash_key_current = f"smith:runs:pending:{auth_id}:{current_id}"
                        pipe.exists(hash_key_current)
                        pipe.hmget(hash_key_current, "parent", "trace_id")
                        (
                            exists,
                            (parent_of_current_id, trace_of_current_id),
                        ) = await pipe.execute()

                    if not exists:
                        # parent hasn't been queued yet
                        # so we save the payloads but don't schedule the task
                        # when the parent is queued, it will pick these up
                        break
                    elif trace_of_current_id is not None:
                        # if it exists and has a trace id, we've found the root
                        trace_ids = {UUID(orjson.loads(trace_of_current_id))}
                        break
                    elif parent_of_current_id is None:
                        # if it exists but has no parent, we've found the root
                        trace_ids = {current_id}
                        break
                    else:
                        current_id = UUID(orjson.loads(parent_of_current_id))
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="Run tree too deep",
                    )
        # Check limits only if not in a docker environment.
        should_check_limits = not config.settings.IS_SELF_HOSTED

        # check inline processing, meaning it will be processed non recursively
        resolved_trace_ids = trace_ids or {
            p.trace_id for p in payloads if p.trace_id is not None
        }
        if len(payloads) > 1 and not resolved_trace_ids:
            raise HTTPException(
                status_code=400,
                detail="trace_ids must be specified for batch requests",
            )

        use_inline_processing = bool(
            resolved_trace_ids
            and (
                # explicitly set by the caller
                all(payload.process_inline for payload in payloads)
                # feedback for root run
                or all(
                    payload.set_key == "feedback"
                    and payload.run_id in resolved_trace_ids
                    for payload in payloads
                )
            )
        )
        queued_run_set_for_auth = queued_runs_key(str(auth_id))

    async with redis.aredis_routed_pool(
        str(auth_id), redis.RedisOperation.WRITE
    ) as aredis:
        # Chunk Redis transactions to avoid too-large pipelines
        chunked_payloads = [
            payloads[i : i + settings.REDIS_TRANSACTION_RUNS_CHUNK_SIZE]
            for i in range(0, len(payloads), settings.REDIS_TRANSACTION_RUNS_CHUNK_SIZE)
        ]

        processed_traces = SeenTraces()
        for chunk in chunked_payloads:
            chunked_trace_ids = trace_ids or {
                p.trace_id for p in chunk if p.trace_id is not None
            }
            if settings.DATADOG_ENABLED:
                with tracer.trace("redis.multipart"):
                    await process_payloads(
                        auth,
                        chunked_trace_ids,
                        chunk,
                        aredis,
                        auth_id,
                        received_at,
                        should_check_limits,
                        use_inline_processing,
                        queued_run_set_for_auth,
                        affects_shortterm_limits,
                        processed_traces=processed_traces,
                    )
            else:
                await process_payloads(
                    auth,
                    chunked_trace_ids,
                    chunk,
                    aredis,
                    auth_id,
                    received_at,
                    should_check_limits,
                    use_inline_processing,
                    queued_run_set_for_auth,
                    affects_shortterm_limits,
                    processed_traces=processed_traces,
                )


async def persist_run_or_parent(
    run_id: str,
    auth_dict: dict[str, Any] | None = None,
    seen: set[str] | None = None,
    semaphore: Optional[asyncio.Semaphore] = None,
    auth_id: str | None = None,  # TODO: Deprecate this
    # include kwargs so adding fields is backwards compatible
    **kwargs,
) -> None:
    """Move a pending run from Redis queue to Postgres, starting with the root."""
    if seen is None:
        await logger.ainfo(
            "persist_run_or_parent info",
            metadata={
                "auth_id": auth_id,
                "run_id": run_id,
            },
        )
        seen = set()

    # if this run tree has a cycle, abort
    if run_id in seen:
        await logger.awarn(
            "Skipping run with cycle: auth_id:%s run_id:%s seen:%s",
            auth_id,
            run_id,
            seen,
        )
        return
    else:
        seen.add(run_id)

    # 1. verify auth
    if auth_dict is None:
        try:
            auth = await get_auth_for_tenant_cached(auth_id)
        except Exception as e:
            await logger.aerror(
                "Failed to get tenant auth for run %s",
                run_id,
                exc_info=e,
                auth_id=auth_id,
            )
            raise
    else:
        auth = await arun_in_executor(parse_full_auth_info, auth_dict)

    set_auth_metadata(auth)

    async with redis.aredis_routed_pool(
        str(auth.tenant_id), redis.RedisOperation.READ
    ) as aredis:
        parent, trace = await aredis.hmget(
            f"smith:runs:pending:{auth.tenant_id}:{run_id}", "parent", "trace_id"
        )
    if trace is not None:
        trace_id = orjson.loads(trace)
        if trace_id != run_id:
            logger.warning(
                "Recursing for parent for seen set empty trace {}".format(seen)
            )
            return await persist_run_or_parent(
                auth_id=auth_id,
                run_id=orjson.loads(trace),
                auth_dict=auth_dict,
                seen=seen,
            )
    elif parent is not None:
        logger.warning("Recursing for parent for seen set no parent {}".format(seen))
        # navigate up the run tree until the root run is found
        return await persist_run_or_parent(
            auth_id=auth_id, run_id=orjson.loads(parent), auth_dict=auth_dict, seen=seen
        )
    # we've reached the root of the tree, so we can start persisting
    await logger.ainfo("Reached root run %s %s %s", auth_id, run_id, seen)

    # 2. acquire lock to prevent multiple workers from persisting the same tree
    # lock is short-lived, so if a worker dies while holding the lock,
    # it will be released automatically 10s after it was acquired
    inflight_key = f"smith:runs:inflight:{auth.tenant_id}:{run_id}"
    async with redis.renewable_lock(inflight_key, QUEUE_LOCK_TIMEOUT) as lock:
        # if lock not acquired, requeue
        if not lock:
            # re-queue the task if another worker is acquiring the lock
            # we only need to re-enqueue if it is marked as queued.
            # if is it not, this means our data has been read and we don't need to re-process it
            async with aredis.pipeline() as pipe:
                # check the queued runs set
                if settings.REDIS_USE_SMISMEMBER:
                    # can use a set (not list) of run IDs because order of results doesn't matter
                    pipe.smismember(
                        queued_runs_key(str(auth.tenant_id)), {str(run) for run in seen}
                    )
                else:
                    for run in seen:
                        pipe.sismember(queued_runs_key(str(auth.tenant_id)), str(run))

                runs_queued = await pipe.execute()

            if any(runs_queued):
                # If a run is queued, this means that some of our Redis updates were not yet read, so requeue it.
                # If they had been read, the queued status would be cleared since it's the same Redis transaction.
                await logger.ainfo(
                    "Lock not acquired, rescheduling  %s %s %s", auth_id, run_id, seen
                )
                async with redis.async_routed_queue(
                    settings.INGESTION_QUEUE, str(auth_id)
                ) as queue:
                    await queue.enqueue(
                        "persist_run_or_parent",
                        auth_id=auth_id,
                        run_id=run_id,
                        auth_dict=auth_dict,
                        lb_trace_context=structlog.contextvars.get_contextvars().get(
                            "lb_trace_context"
                        ),
                        scheduled=(
                            datetime.now(timezone.utc)
                            + timedelta(seconds=QUEUE_LOCK_TIMEOUT * 1.1)
                        ).timestamp(),
                        **redis.INGEST_JOB_KWARGS,
                    )
            else:
                # If there are no queued runs and the lock is active, this means our Redis updates have been read
                # by another job and no more work is to be done.
                # If it hadn't been read, the task would still be marked as queued as Redis read and queue clear is a Redis transaction.
                await logger.ainfo(
                    "Lock not acquired, queued is cleared, exiting %s %s %s",
                    auth_id,
                    run_id,
                    seen,
                )
            return
        else:
            await logger.ainfo("Acquired lock %s %s %s", auth_id, run_id, seen)

            # 3. persist run or if needed, recurse back up the tree, persisting each run
            try:
                await persist_run_and_children_ch(
                    auth,
                    run_id,
                    semaphore
                    or asyncio.Semaphore(config.settings.WRITE_QUEUE_PARALLEL_OPS),
                )
            except HTTPException:
                await _cleanup_queued_runs(str(auth.tenant_id), [run_id])
            except Exception as e:
                await logger.aexception(e)
                await _cleanup_queued_runs(str(auth.tenant_id), [run_id])
                raise


async def persist_batched_runs(
    run_ids: list[list[str]] | list[str],
    auth_dict: dict[str, Any] | None = None,
    semaphore: Optional[asyncio.Semaphore] = None,
    tenant_id: str | None = None,
    # include kwargs so adding fields is backwards compatible
    **kwargs,
) -> None:
    """
    Store a list of batched runs that have trace_id and dotted order.
    """

    # 1. verify auth
    if auth_dict is None:
        try:
            auth = await get_auth_for_tenant_cached(tenant_id)
        except Exception as e:
            await logger.aerror(
                "Failed to get tenant auth for run %s",
                run_ids=run_ids,
                exc_info=e,
                tenant_id=tenant_id,
            )
            raise

    else:
        auth = await arun_in_executor(parse_full_auth_info, auth_dict)

    set_auth_metadata(auth)

    # 2. persist batch of runs into ch
    try:
        await persist_batched_runs_ch(
            auth,
            run_ids,
            semaphore or asyncio.Semaphore(config.settings.WRITE_QUEUE_PARALLEL_OPS),
        )
    except Exception as e:
        await logger.aexception(e)
        raise


async def get_public_runs(
    auth: ShareRunInfo,
    query_params: schemas.QueryParamsForPublicRunSchema,
) -> Sequence[dict]:
    """Get all public runs depending on the query params and owner."""
    if query_params.id is None or len(query_params.id) == 0:
        query_params.id = [auth.run_id]

    if len(set(query_params.id) - set(auth.allowed_run_ids)) > 0:
        raise HTTPException(
            status_code=404,
            detail="Runs not found",
        )

    return await models.runs.fetch_ch.fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            id=query_params.id,
            session=[auth.session_id] if auth.session_id else None,
        ),
    )


@retry_asyncpg
async def get_share_key_by_run_id(
    auth: AuthInfo,
    run_id: UUID,
) -> dict | None:
    """Get a share key."""
    await models.runs.fetch_ch.fetch_and_assert_run(auth, run_id)
    async with database.asyncpg_pool() as db:
        return await db.fetchrow("select * from share_keys where run_id = $1", run_id)


@retry_asyncpg
async def share_run(
    auth: AuthInfo,
    run_id: UUID,
) -> Any:
    """Share a run with a share key."""
    if auth.public_sharing_disabled:
        raise HTTPException(
            status_code=403,
            detail="Sharing resources is disabled for this organization.",
        )
    await models.runs.fetch_ch.fetch_and_assert_run(auth, run_id)
    try:
        async with database.asyncpg_conn() as db:
            insert_stmt = """
                    INSERT INTO share_keys (id, run_id, share_token, created_at, tenant_id)
                    SELECT $id, $run_id, $share_token, $created_at, $tenant_id
                    FROM organizations
                    WHERE id = $organization_id AND NOT organizations.public_sharing_disabled
                    RETURNING *;
                """

            sql_query = database.kwargs_to_pgpos(
                insert_stmt,
                {
                    "id": uuid4(),
                    "run_id": run_id,
                    "share_token": uuid4(),
                    "created_at": datetime.utcnow(),
                    "tenant_id": auth.tenant_id,
                    "organization_id": auth.organization_id,
                },
            )
            result = await db.fetchrow(sql_query.sql, *sql_query.args)
            if not result:
                raise HTTPException(
                    status_code=403,
                    detail="Sharing resources is disabled for this organization.",
                )
            return result
    except asyncpg.UniqueViolationError:
        raise HTTPException(status_code=409, detail="Run already shared")


@retry_asyncpg
async def unshare_run(
    auth: AuthInfo,
    run_id: UUID,
) -> None:
    """Unshare a run with a share key."""
    await models.runs.fetch_ch.fetch_and_assert_run(auth, run_id)
    async with database.asyncpg_conn() as db:
        params = {"run_id": run_id}
        delete_stmt = "DELETE FROM share_keys WHERE run_id = $run_id"
        sql_query = database.kwargs_to_pgpos(delete_stmt, params)
        await db.execute(sql_query.sql, *sql_query.args)


@retry_asyncpg
async def share_dataset(
    auth: AuthInfo,
    dataset_id: UUID,
    params: schemas.QueryParamsForShareDatasetSchema,
) -> Any:
    """Share a dataset with a share key."""
    if auth.public_sharing_disabled:
        raise HTTPException(
            status_code=403,
            detail="Sharing resources is disabled for this organization.",
        )
    try:
        async with database.asyncpg_conn() as db, db.transaction():
            await models.datasets.fetch.fetch_dataset(db, auth, dataset_id)
            # TODO: Flag whether projects can be shared. Could add a list of project IDs here

            insert_stmt = """
                    INSERT INTO datasets_share_keys (dataset_id, share_token, created_at)
                    SELECT $dataset_id, $share_token, $created_at
                    FROM organizations
                    WHERE id = $organization_id AND NOT organizations.public_sharing_disabled
                    RETURNING *;
                """
            sql_query = database.kwargs_to_pgpos(
                insert_stmt,
                {
                    "dataset_id": dataset_id,
                    "organization_id": auth.organization_id,
                    "share_token": uuid4(),
                    "created_at": datetime.utcnow(),
                },
            )
            result = await db.fetchrow(sql_query.sql, *sql_query.args)
            if not result:
                raise HTTPException(
                    status_code=403,
                    detail="Sharing resources is disabled for this organization.",
                )
            return result
    except asyncpg.UniqueViolationError:
        raise HTTPException(status_code=409, detail="Dataset already shared")


@retry_asyncpg
async def unshare_dataset(
    auth: AuthInfo,
    dataset_id: UUID,
) -> None:
    """Unshare a run with a share key."""
    async with database.asyncpg_conn() as db, db.transaction():
        await models.datasets.fetch.fetch_dataset(db, auth, dataset_id)

        params = {"dataset_id": dataset_id}
        delete_stmt = "DELETE FROM datasets_share_keys WHERE dataset_id = $dataset_id"
        sql_query = database.kwargs_to_pgpos(delete_stmt, params)
        await db.execute(sql_query.sql, *sql_query.args)


@retry_asyncpg
async def get_dataset_share_key_by_dataset_id(
    auth: AuthInfo,
    dataset_id: UUID,
) -> Any:
    """Get a share key for the target dataset ID."""
    async with database.asyncpg_conn() as db:
        await models.datasets.fetch.fetch_dataset(db, auth, dataset_id)

        params = {"dataset_id": dataset_id}
        query = "SELECT * FROM datasets_share_keys WHERE dataset_id = $dataset_id"
        sql_query = database.kwargs_to_pgpos(
            query,
            params,
        )
        share_key_row = await db.fetchrow(sql_query.sql, *sql_query.args)

        if share_key_row is None:
            return None

        return share_key_row


async def create_feedback(
    auth: AuthInfo | FeedbackTokenInfo,
    feedback: schemas.FeedbackCreateSchemaInternal,
) -> int:
    """Create a feedback."""
    async with database.asyncpg_pool() as db:
        if feedback.comparative_experiment_id:
            ce_tid = await db.fetchval(
                "select tenant_id from comparative_experiment where id = $1",
                feedback.comparative_experiment_id,
            )
            if ce_tid is None or ce_tid != auth.tenant_id:
                raise HTTPException(
                    status_code=404, detail="Comparative Experiment not found"
                )

    # Feedback _may_ upgrade a trace, so we reject if the longlived usage limit is hit
    await check_longlived_usage_limits(auth)

    feedback_payload = feedback.model_dump()

    if (
        isinstance(auth, AuthInfo)
        and feedback_payload["feedback_source"] is not None
        and auth.user_id is not None
    ):
        feedback_payload["feedback_source"]["user_id"] = auth.user_id
    if (
        isinstance(auth, FeedbackTokenInfo)
        and auth.session_id
        and auth.trace_id
        and auth.start_time
    ):
        await models.feedback.ingest.upsert_feedback(
            [
                models.feedback.ingest.FeedbackInsert(
                    payload=orjson.loads(ORJSONSerializer.dumps(feedback_payload)),
                    trace_id=auth.trace_id,
                    session_id=auth.session_id,
                    start_time=auth.start_time,
                    redis=None,
                )
            ],
            auth,
        )
        return 200
    elif feedback.run_id is None:
        if feedback.session_id is None:
            raise HTTPException(
                status_code=400,
                detail="session_id is required for session-level feedback",
            )
        await models.feedback.ingest.upsert_feedback(
            [
                models.feedback.ingest.FeedbackInsert(
                    payload=orjson.loads(ORJSONSerializer.dumps(feedback_payload)),
                    trace_id=UUID("00000000-0000-0000-0000-000000000000"),
                    session_id=feedback.session_id,
                    start_time=datetime(1970, 1, 1, tzinfo=timezone.utc),
                    redis=None,
                )
            ],
            auth,
        )
        return 200
    elif run := await fetch_single_run_info_cached(
        auth,
        feedback.run_id,
        include_feedback=False,
    ):
        await models.feedback.ingest.upsert_feedback(
            [
                models.feedback.ingest.FeedbackInsert(
                    payload=orjson.loads(ORJSONSerializer.dumps(feedback_payload)),
                    trace_id=run["trace_id"],
                    session_id=run["session_id"],
                    start_time=run["start_time"],
                    redis=None,
                )
            ],
            auth,
        )
        return 200
    else:
        feedback_configs = (
            await models.feedback_configs.fetch.fetch_feedback_configs_cached(
                auth, [feedback.key]
            )
        )

        stored_config = (
            feedback_configs[0]["feedback_config"] if feedback_configs else None
        )
        payload_config = feedback.feedback_config
        default_config = models.feedback.ingest.get_default_feedback_config(
            [feedback_payload]
        )

        feedback_config = models.feedback.ingest.resolve_feedback_config(
            stored_config=stored_config,
            payload_config=payload_config,
            default_config=default_config,
        )

        models.feedback.ingest.verify_feedback_config(feedback_payload, feedback_config)

        payloads = [
            QueuePayload(
                run_id=feedback.run_id,
                parent_id=None,
                trace_id=None,
                value=orjson.dumps(feedback_payload),
                set_key="feedback",
                process_inline=False,
            )
        ]
        await ensure_sessions_before_queue_run_payload(auth, payloads)
        await queue_run_payload(auth, None, payloads)
        return 202


async def update_feedback_ch(
    auth: AuthInfo,
    feedback_id: UUID,
    feedback_update: schemas.FeedbackUpdateSchema,
) -> schemas.FeedbackSchema:
    existing = await models.feedback.fetch.fetch_feedback(feedback_id, auth)
    combined = {
        **existing.model_dump(),
        **feedback_update.model_dump(exclude_unset=True),
    }
    coroutines = [
        models.feedback.ingest.upsert_feedback(
            [
                models.feedback.ingest.FeedbackInsert(
                    payload=orjson.loads(ORJSONSerializer.dumps(combined)),
                    trace_id=existing.trace_id,  # type: ignore
                    session_id=existing.session_id,
                    start_time=existing.start_time,  # type: ignore
                    redis=None,
                )
            ],
            auth,
            throw_on_invalid=True,
        )
    ]

    # If the feedback has a correction and a source evaluator run, we need to add feedback to the evaluator run
    try:
        source_run_id = (
            combined.get("feedback_source", {})
            .get("metadata", {})
            .get("__run", {})
            .get("run_id")
        )
    except AttributeError:
        source_run_id = None
    if feedback_update.correction is not None and source_run_id is not None:
        if isinstance(feedback_update.correction, dict):
            if "score" in feedback_update.correction:
                value = feedback_update.correction["score"]
                try:
                    new_score = float(value)
                    del feedback_update.correction["score"]
                    feedback_correction = {
                        combined[
                            "key"
                        ]: f"{int(new_score) if new_score.is_integer() else new_score}",
                        **(feedback_update.correction),
                    }
                    coroutines.append(
                        create_feedback(
                            auth,
                            schemas.FeedbackCreateSchema(
                                key=combined["key"],
                                correction=feedback_correction,
                                run_id=source_run_id,
                            ),
                        )
                    )
                except Exception as e:
                    logger.error(
                        f"There was an error creating the example in the corrections dataset: {e}"
                    )
                    pass

    await asyncio.gather(*coroutines)
    return schemas.FeedbackSchema(**combined)


@redis_cache(ttl=120, cache_empty=False)
async def fetch_single_run_info_cached(
    auth: AuthInfo | ShareRunInfo | ShareDatasetInfo | FeedbackTokenInfo,
    run_id: UUID,
    select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
) -> dict[str, Any] | None:
    """
    Return info for single run, cached to be used by feedback creation.
    """
    async with redis.aredis_routed_pool(
        str(auth.tenant_id), redis.RedisOperation.READ
    ) as aredis:
        session_id, trace_id, start_time = await aredis.hmget(
            f"smith:runs:pending:{str(auth.tenant_id)}:{str(run_id)}",
            "session_id",
            "trace_id",
            "start_time",
        )
        # check if this is in Redis, which is the fastest path
        if session_id and trace_id and start_time:
            return {
                "id": str(run_id),
                "trace_id": str(orjson.loads(trace_id)),
                "session_id": orjson.loads(session_id),
                "start_time": datetime.fromisoformat(orjson.loads(start_time)),
            }

    # fallback to checking clickhouse
    run = await models.runs.fetch_ch.fetch_single_run(
        auth=auth, run_id=run_id, select=select, include_feedback=include_feedback
    )
    if run:
        return {
            "id": run["id"],
            "trace_id": run["trace_id"],
            "session_id": run["session_id"],
            "start_time": run["start_time"],
        }
    else:
        return run


def _build_annotation_queue_filters(
    auth: AuthInfo,
    query_params: schemas.QueryParamsForAnnotationQueueSchema,
) -> Tuple[str, list]:
    """Build filters for annotation queues."""
    parameters: List[Union[UUID, str, List[UUID], int]] = []
    sql_filter = "WHERE "

    if isinstance(auth, AuthInfo):
        sql_filter += "annotation_queues.tenant_id = $1 "
        parameters.append(auth.tenant_id)
    else:
        raise ValueError(f"Invalid auth type: {type(auth)}")

    curr_index = 2
    if query_params.name:
        sql_filter += f"AND annotation_queues.name = ${curr_index} "
        parameters.append(query_params.name)
        curr_index += 1
    if query_params.name_contains:
        sql_filter += f"AND annotation_queues.name ILIKE ${curr_index} "
        parameters.append(f"%{query_params.name_contains}%")
        curr_index += 1
    if query_params.ids:
        sql_filter += f"AND annotation_queues.id = ANY(${curr_index}) "
        parameters.append(query_params.ids)
        curr_index += 1
    if query_params.tag_value_id:
        sql_filter += f"""
        AND annotation_queues.id IN (
            SELECT t.resource_id
            FROM taggings t
            JOIN tag_values tv ON t.tag_value_id = tv.id
            JOIN tag_keys tk ON tv.tag_key_id = tk.id
            WHERE t.resource_type = 'queue'
            AND t.tag_value_id = ANY(${curr_index}::uuid[])
            AND tk.tenant_id = $1
            GROUP BY t.resource_id, t.resource_type
            HAVING COUNT(*) = (
                SELECT COUNT(*) FROM unnest(${curr_index}::uuid[])
            )
        )
        """
        parameters.append(query_params.tag_value_id)
        curr_index += 1
    if query_params.dataset_id:
        sql_filter += " ORDER BY is_associated_with_dataset DESC, updated_at DESC, created_at DESC "
    else:
        sql_filter += " ORDER BY updated_at DESC, created_at DESC "

    if query_params.limit > 0:
        sql_filter += f"LIMIT ${curr_index} OFFSET ${curr_index + 1}"
        parameters.append(query_params.limit + 1)
        parameters.append(query_params.offset)
    return sql_filter, parameters


async def get_annotation_queues(
    db: asyncpg.Connection,
    auth: AuthInfo,
    query_params: schemas.QueryParamsForAnnotationQueueSchema,
) -> Tuple[list[schemas.AnnotationQueueSchemaWithSize], int]:
    """Get all annotation queues based on query params."""
    filters, params = _build_annotation_queue_filters(auth, query_params=query_params)
    extra_select = ""
    if query_params.dataset_id:
        curr_index = len(params) + 1
        extra_select = f""", CASE WHEN EXISTS (
                SELECT 1
                FROM annotation_queue_sessions aqs
                JOIN tracer_session ts ON ts.id = aqs.session_id
                WHERE aqs.annotation_queue_id = annotation_queues.id
                AND ts.reference_dataset_id = ${curr_index}
            ) THEN 1 ELSE 0 END as is_associated_with_dataset
        """
        params.append(query_params.dataset_id)
        curr_index += 1
    rows = await db.fetch(
        f"""SELECT annotation_queues.*, (SELECT COUNT(*)
            FROM annotation_queue_runs aqr
            WHERE aqr.queue_id = annotation_queues.id) as total_runs{extra_select}
        FROM annotation_queues {filters}""",
        *params,
    )

    return [
        schemas.AnnotationQueueSchemaWithSize(**row)
        for row in (rows[: query_params.limit] if query_params.limit > 0 else rows)
    ], query_params.offset + len(rows)


async def get_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
) -> schemas.AnnotationQueueSchemaWithRubric:
    """Get an annotation queue based on query params."""
    queue_rows = await db.fetch(
        """
        SELECT
            aq.*,
            aqri.id as rubric_item_id,
            aqri.feedback_key,
            aqri.description as rubric_item_description,
            aqri.value_descriptions,
            aqri.score_descriptions
        FROM annotation_queues aq
        LEFT JOIN annotation_queue_rubric_items aqri ON aq.id = aqri.annotation_queue_id
        WHERE aq.id = $1 AND aq.tenant_id = $2
        """,
        queue_id,
        auth.tenant_id,
    )

    if not queue_rows:
        raise HTTPException(status_code=404, detail="Annotation queue not found")

    base_queue_data = {
        "id": queue_rows[0]["id"],
        "tenant_id": queue_rows[0]["tenant_id"],
        "name": queue_rows[0]["name"],
        "description": queue_rows[0]["description"],
        "created_at": queue_rows[0]["created_at"],
        "updated_at": queue_rows[0]["updated_at"],
        "default_dataset": queue_rows[0]["default_dataset"],
        "num_reviewers_per_item": queue_rows[0]["num_reviewers_per_item"],
        "enable_reservations": queue_rows[0]["enable_reservations"],
        "reservation_minutes": queue_rows[0]["reservation_minutes"],
        "rubric_instructions": queue_rows[0]["rubric_instructions"],
        "source_rule_id": queue_rows[0]["source_rule_id"],
    }

    rubric_items = []
    for row in queue_rows:
        if row["rubric_item_id"] is None:
            continue

        rubric_item = {
            "feedback_key": row["feedback_key"],
            "description": row["rubric_item_description"],
            "value_descriptions": row["value_descriptions"],
            "score_descriptions": row["score_descriptions"],
        }
        rubric_items.append(rubric_item)

    result = {
        **base_queue_data,
        "rubric_items": rubric_items,
    }

    return schemas.AnnotationQueueSchemaWithRubric(**result)


async def populate_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    request: schemas.PopulateAnnotationQueueSchema,
) -> None:
    """Populate an annotation queue with runs from sessions."""
    runs_to_insert, sessions = await get_runs_to_populate_annotation_queue(
        db, auth, request.session_ids
    )

    if not runs_to_insert["runs"]:
        return None

    async with db.transaction():
        rule_id = await add_runs_from_sessions_to_aq(
            db, auth, request.queue_id, sessions, runs_to_insert
        )
    if rule_id:
        from app.models.runs.rules import trigger_rule

        await trigger_rule(auth, rule_id)


async def get_runs_to_populate_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    session_ids: List[UUID],
) -> tuple[FetchRunsResult, list[dict]]:
    sessions = await db.fetch(
        """
        SELECT id, name, reference_dataset_id, start_time FROM tracer_session WHERE id = ANY($1) AND tenant_id = $2
        """,
        [str(id) for id in session_ids],
        auth.tenant_id,
    )
    if not sessions or len(sessions) != len(session_ids):
        raise HTTPException(status_code=404, detail="Session not found")
    elif any(not s["reference_dataset_id"] for s in sessions):
        raise HTTPException(
            status_code=400,
            detail="Session must have a reference dataset to create an annotation queue",
        )
    return await fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            session=session_ids,
            filter="eq(is_root, true)",
            limit=ANNOTATION_QUEUE_SYNC_RUN_FETCH_LIMIT,
        ),
    ), sessions


async def add_runs_from_sessions_to_aq(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    sessions: List[dict],
    runs_to_insert: FetchRunsResult,
) -> UUID | None:
    session_ids = [str(s["id"]) for s in sessions]

    await add_runs_to_annotation_queue(
        db, auth, queue_id, [run["id"] for run in runs_to_insert["runs"]]
    )

    await db.execute(
        """
        INSERT INTO annotation_queue_sessions (annotation_queue_id, session_id)
        SELECT $1, UNNEST($2::uuid[])
        ON CONFLICT DO NOTHING
        """,
        queue_id,
        session_ids,
    )

    if session_ids and runs_to_insert["cursors"]["next"]:
        from app.models.runs.rules import create_rule_with_existing_transaction

        # Create a disabled rule with backfill starting at the session start time to do the rest of the runs
        earliest_start_time = min([s["start_time"] for s in sessions])
        earliest_start_time = (
            (earliest_start_time - timedelta(minutes=1))
            .replace(tzinfo=timezone.utc)
            .isoformat()
        )
        rule = await create_rule_with_existing_transaction(
            db,
            auth,
            schemas.RunRulesCreateSchema(
                display_name="Annotation Queue backfill",
                is_enabled=False,
                backfill_from=earliest_start_time,
                dataset_id=str(sessions[0]["reference_dataset_id"]),
                add_to_annotation_queue_id=queue_id,
                sampling_rate=1.0,
                filter="and(eq(is_root, true), in(session_id, [{}]))".format(
                    ",".join(f"'{str(id)}'" for id in session_ids)
                ),
                transient=True,
            ),
        )
        logger.info(f"Created transient backfill rule {rule.id}")

        # Add rule id to annotation queue:
        await db.execute(
            """
            UPDATE annotation_queues
            SET
                source_rule_id = $1
            WHERE id = $2
            """,
            str(rule.id),
            queue_id,
        )

        return rule.id

    return None


async def create_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue: schemas.AnnotationQueueCreateSchema,
) -> schemas.AnnotationQueueSchema:
    """Create a new annotation queue."""
    runs_to_insert = None
    sessions = None
    rule_id = None

    if queue.session_ids:
        runs_to_insert, sessions = await get_runs_to_populate_annotation_queue(
            db, auth, queue.session_ids
        )
    async with db.transaction():
        row = await db.fetchrow(
            """
            INSERT INTO annotation_queues (
            id,
            name,
            description,
            created_at,
            tenant_id,
            default_dataset,
            num_reviewers_per_item,
            enable_reservations,
            reservation_minutes,
            rubric_instructions
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
            """,
            queue.id,
            queue.name,
            queue.description,
            queue.created_at,
            auth.tenant_id,
            queue.default_dataset,
            queue.num_reviewers_per_item,
            queue.enable_reservations,
            queue.reservation_minutes,
            queue.rubric_instructions,
        )

        if sessions and runs_to_insert and runs_to_insert["runs"]:
            rule_id = await add_runs_from_sessions_to_aq(
                db, auth, row["id"], sessions, runs_to_insert
            )

        if queue.rubric_items and row:
            await db.executemany(
                """
                INSERT INTO annotation_queue_rubric_items (annotation_queue_id, feedback_key, description, value_descriptions, score_descriptions)
                VALUES ($1, $2, $3, $4, $5)
                """,
                [
                    (
                        queue.id,
                        item.feedback_key,
                        item.description,
                        item.value_descriptions or {},
                        item.score_descriptions or {},
                    )
                    for item in queue.rubric_items
                ],
            )

    if rule_id:
        from app.models.runs.rules import trigger_rule

        await trigger_rule(auth, rule_id)

    return schemas.AnnotationQueueSchema(**row)


async def delete_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
) -> None:
    """Delete an annotation queue, ensuring that the owner is auth.tenant_id."""

    row = await db.fetchrow(
        """
        DELETE FROM annotation_queues
        WHERE id = $1 AND tenant_id = $2
        RETURNING *
        """,
        queue_id,
        auth.tenant_id,
    )

    # If the DELETE operation didn't affect any rows, raise an exception
    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Annotation queue with id {queue_id} does not exist or does not belong to the right tenant.",
        )


async def update_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    queue: schemas.AnnotationQueueUpdateSchema,
) -> None:
    """Update an annotation queue."""
    row = await db.fetchrow(
        """
        UPDATE annotation_queues
        SET
          name = COALESCE($1, name),
          description = COALESCE($2, description),
          default_dataset = COALESCE($3, default_dataset),
          num_reviewers_per_item = $4,
          enable_reservations = $5,
          reservation_minutes = $6,
          rubric_instructions = COALESCE($7, rubric_instructions)
        WHERE id = $8 AND tenant_id = $9
        RETURNING *
        """,
        queue.name,
        queue.description,
        queue.default_dataset,
        queue.num_reviewers_per_item,
        queue.enable_reservations,
        queue.reservation_minutes,
        queue.rubric_instructions,
        queue_id,
        auth.tenant_id,
    )
    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Annotation queue with id {queue_id} does not exist or does not belong to the right tenant.",
        )

    if queue.rubric_items is not None and len(queue.rubric_items) == 0:
        await db.execute(
            """
            DELETE FROM annotation_queue_rubric_items
            WHERE annotation_queue_id = $1
            """,
            queue_id,
        )
    elif queue.rubric_items:
        await db.execute(
            """
            DELETE FROM annotation_queue_rubric_items
                WHERE annotation_queue_id = $1
                AND feedback_key != ALL($2::text[])
            """,
            queue_id,
            [item.feedback_key for item in queue.rubric_items],
        )
        await db.executemany(
            """
            INSERT INTO annotation_queue_rubric_items (annotation_queue_id, feedback_key, description, value_descriptions, score_descriptions)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (annotation_queue_id, feedback_key) DO UPDATE
            SET description = EXCLUDED.description,
                value_descriptions = EXCLUDED.value_descriptions,
                score_descriptions = EXCLUDED.score_descriptions
            """,
            [
                (
                    queue_id,
                    item.feedback_key,
                    item.description,
                    item.value_descriptions or {},
                    item.score_descriptions or {},
                )
                for item in queue.rubric_items
            ],
        )


async def add_runs_to_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    run_ids: List[UUID],
) -> List[schemas.AnnotationQueueRunSchema]:
    """Add runs to a queue."""
    # First, check if all provided run ids exist and belong to the logged-in tenant, and that the queue belongs to the logged-in tenant
    queue_row = await db.fetchrow(
        """
        SELECT id
        FROM annotation_queues
        WHERE id = $1 AND tenant_id = $2
        """,
        queue_id,
        auth.tenant_id,
    )
    if not queue_row:
        raise HTTPException(
            status_code=404,
            detail=f"Annotation queue {queue_id} not found.",
        )

    # Annotation queues _may_ upgrade a trace, so we reject if the longlived usage limit is hit
    # Note: this will be very easy to change in the future, since we know the tier of each run
    #       up front before adding to the queue. We just have this behavior to be consistent
    #       with the rest of the system.
    await check_longlived_usage_limits(auth)

    runs = await models.runs.fetch_ch.fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            id=run_ids,
            select=[
                schemas.RunSelect.id,
                schemas.RunSelect.trace_tier,
                schemas.RunSelect.start_time,
                schemas.RunSelect.parent_run_id,
                schemas.RunSelect.session_id,
            ],
        ),
    )
    if not len(runs["runs"]) == len(run_ids):
        raise HTTPException(
            status_code=404,
            detail="Not found",
        )

    # If all run ids are valid, add them to the queue
    rows = await db.fetch(
        """
        INSERT INTO annotation_queue_runs (run_id, queue_id, added_at, start_time, is_root, session_id)
            SELECT
                run_id,
                $2,
                now(),
                start_time,
                is_root,
                session_id
            FROM UNNEST(
                $1::uuid[],
                $3::timestamp[],
                $4::bool[],
                $5::uuid[]
            ) as t(run_id, start_time, is_root, session_id)
            ON CONFLICT (run_id, queue_id) DO NOTHING
            RETURNING *
        """,
        [run["id"] for run in runs["runs"]],
        queue_id,
        [run["start_time"] for run in runs["runs"]],
        [run["parent_run_id"] is None for run in runs["runs"]],
        [run["session_id"] for run in runs["runs"]],
    )

    # If the run is already in the queue we should re-show it to people who have marked it as completed
    # But we don't want duplicate runs in a queue
    # So we add a identity_annotation_queue_run_status row for each identity that has completed the run
    # marking the status as null and the override_added_at as now() so it shows up at the end of their queue
    await db.fetch(
        """
        WITH already_in_queue_ids AS (
          SELECT id, run_id
          FROM annotation_queue_runs
          WHERE queue_id = $2
          AND run_id IN (SELECT UNNEST($1::uuid[]))
        ),
        identities_needing_update AS (
          SELECT i.id AS identity_id, aqr.id AS annotation_queue_run_id
          FROM identities i
          CROSS JOIN already_in_queue_ids aqr
          LEFT JOIN LATERAL (
              SELECT status
              FROM identity_annotation_queue_run_status
              WHERE identity_id = i.id
                AND annotation_queue_run_id = aqr.id
              ORDER BY created_at DESC
              LIMIT 1
          ) latest_run ON true
          WHERE i.tenant_id = $3
            AND latest_run.status = 'completed'
        )
        INSERT INTO identity_annotation_queue_run_status
            (identity_id, annotation_queue_run_id, status, override_added_at)
        SELECT identity_id, annotation_queue_run_id, NULL, now()
        FROM identities_needing_update
        RETURNING *
        """,
        run_ids,
        queue_id,
        auth.tenant_id,
    )

    if settings.FF_TRACE_TIERS_ENABLED:
        runs_to_upgrade = [
            run
            for run in runs["runs"]
            if run.get("trace_tier") == schemas.TraceTier.shortlived
        ]
        if runs_to_upgrade:
            await upgrade_trace_tier(
                auth=auth,
                session_id=runs_to_upgrade[0]["session_id"],
                trace_ids=list(set([run["trace_id"] for run in runs_to_upgrade])),
                reason=TraceTierUpgradeReason.annotation_queue,
            )

    return [schemas.AnnotationQueueRunSchema(**row) for row in rows]


async def get_run_from_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    index: int,
) -> schemas.RunSchemaWithAnnotationQueueInfo:
    """Get a single run from a queue based on index."""

    # We're looking for the first run in the queue that has not been completed by the current identity_id
    # And that is not already fully reserved by OTHER identity_ids or completed by anyone
    #
    # An identity holds a reservation for an annotation_queue_run if the most recent status entry
    # Between the identity and annotation_queue_run is "viewed" (and created_at is within the last specified reservation time)
    # or "completed" (regardless of created_at)
    #
    # Take 1 and offset by the index.
    row = await db.fetchrow(
        f"""
        {_reserved_or_completed_run_sql}
        SELECT aqr.*, COALESCE(current_user_status.override_added_at, aqr.added_at) AS effective_added_at
        FROM annotation_queue_runs aqr
        {_check_for_current_user_status_sql}
        ORDER BY effective_added_at ASC, aqr.id ASC
        LIMIT 1 OFFSET $4;
        """,
        auth.identity_id,
        queue_id,
        auth.tenant_id,
        index,
    )

    if not row:
        raise HTTPException(
            status_code=404,
            detail="Run not found",
        )

    runs = await models.runs.fetch_ch.fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            id=[UUID(row["run_id"].hex)],
            start_time=row["start_time"],
            is_root=row["is_root"],
            session=[row["session_id"]] if row["session_id"] else None,
            select=[
                schemas.RunSelect.inputs,
                schemas.RunSelect.outputs,
                schemas.RunSelect.reference_dataset_id,
                schemas.RunSelect.reference_example_id,
            ],
        ),
    )
    runs_by_id = {run["id"]: run for run in runs["runs"]}

    if run := runs_by_id.get(UUID(row["run_id"].hex)):
        await create_identity_annotation_queue_run_status(
            auth,
            row["id"],
            schemas.IdentityAnnotationQueueRunStatusCreateSchema(status="viewed"),
        )

        return schemas.RunSchemaWithAnnotationQueueInfo(
            **run,
            queue_run_id=row["id"],
            added_at=row["added_at"],
            effective_added_at=row["effective_added_at"],
            last_reviewed_time=row["last_reviewed_time"],
        )

    # If we got here, the run no longer exists in clickhouse. Delete from postgres and fetch another.
    await db.execute(
        """
        DELETE FROM annotation_queue_runs
        USING annotation_queues
        WHERE annotation_queue_runs.queue_id = annotation_queues.id
        AND annotation_queue_runs.run_id = $1
        AND annotation_queue_runs.queue_id = $2
        AND annotation_queues.tenant_id = $3
        """,
        row["run_id"],
        queue_id,
        auth.tenant_id,
    )
    return await get_run_from_annotation_queue(db, auth, queue_id, index)


async def export_annotation_queue_archived_runs(
    auth: AuthInfo,
    queue_id: UUID,
    request: schemas.ExportAnnotationQueueRunsRequest,
    write_stream: MemoryObjectSendStream[bytes],
    task_status: TaskStatus[None] = TASK_STATUS_IGNORED,
) -> None:
    """Export archived annotation queue runs as CSV."""
    offset = 0
    batch_size = 1000
    headers_written = False

    start_utc = (
        request.start_time.astimezone(timezone.utc).replace(tzinfo=None)
        if request.start_time and request.start_time.tzinfo
        else request.start_time
    )
    end_utc = (
        request.end_time.astimezone(timezone.utc).replace(tzinfo=None)
        if request.end_time and request.end_time.tzinfo
        else request.end_time
    )

    async with database.asyncpg_conn() as db:
        rubric_items = await db.fetch(
            """
            SELECT feedback_key
                FROM annotation_queues aq
                LEFT JOIN annotation_queue_rubric_items aqri ON aq.id = aqri.annotation_queue_id
                WHERE aq.id = $1 AND aq.tenant_id = $2
            """,
            queue_id,
            auth.tenant_id,
        )
        if not rubric_items:
            raise HTTPException(
                status_code=404,
                detail="Annotation queue not found",
            )
        try:
            while True:
                rows = await db.fetch(
                    """
                    SELECT aqr.*, COALESCE(aqr.deleted_at, aqr.added_at) as reviewed_at
                    FROM annotation_queue_runs_archive aqr
                    JOIN annotation_queues aq ON aqr.queue_id = aq.id
                    WHERE aqr.queue_id = $3
                    AND aq.tenant_id = $4
                    AND COALESCE(aqr.deleted_at, aqr.added_at) > $5
                    AND COALESCE(aqr.deleted_at, aqr.added_at) < $6
                    ORDER BY COALESCE(aqr.deleted_at, aqr.added_at) DESC, aqr.id ASC
                    LIMIT $1 OFFSET $2;
                    """,
                    batch_size,
                    offset,
                    queue_id,
                    auth.tenant_id,
                    start_utc or datetime.min,
                    end_utc or datetime.max,
                )

                if offset == 0:
                    if not rows:
                        raise HTTPException(
                            status_code=404,
                            detail="No archived runs found",
                        )
                    task_status.started()

                if not rows:
                    break

                feedback_keys = get_rubric_item_feedback_keys(rubric_items)

                runs, (feedbacks, _) = await asyncio.gather(
                    fetch_runs(
                        auth,
                        schemas.BodyParamsForRunSchema.model_construct(
                            id=[UUID(row["run_id"].hex) for row in rows],
                            limit=batch_size,
                        ),
                        include_feedback=True,
                    ),
                    fetch_feedbacks(
                        auth,
                        schemas.QueryParamsForFeedbackSchema(
                            run=[UUID(row["run_id"].hex) for row in rows],
                            key=feedback_keys,
                            has_comment=True,
                        ),
                    ),
                )
                runs_arr = []
                for row in rows:
                    run = next(
                        (r for r in runs["runs"] if r["id"] == UUID(row["run_id"].hex)),
                        None,
                    )
                    if run:
                        runs_arr.append({**run, "reviewed_at": row["reviewed_at"]})

                if not runs_arr:
                    break

                # Write headers if this is the first batch
                if not headers_written and runs_arr:
                    # Extract field names from the first run
                    fieldnames = get_csv_fieldnames(feedback_keys)
                    headers = ",".join(fieldnames) + "\n"
                    await write_stream.send(headers.encode("utf-8"))
                    headers_written = True

                # Write each run as a CSV row
                to_write = ""
                for run in runs_arr:
                    try:
                        csv_row = format_run_as_csv(run, fieldnames, feedbacks)
                        to_write += csv_row
                    except Exception as e:
                        import traceback

                        await logger.aerror(
                            "Error writing run to stream",
                            error=e,
                            traceback=traceback.format_exc(),
                        )
                        continue
                try:
                    await write_stream.send(to_write.encode("utf-8"))
                except Exception as e:
                    await logger.aerror("Error writing run to stream", error=e)
                    continue

                offset += batch_size
        finally:
            await write_stream.aclose()


def get_rubric_item_feedback_keys(rubric_items: list[dict]) -> list[str]:
    """Get feedback keys from rubric items."""
    return (
        [item["feedback_key"] for item in rubric_items if item["feedback_key"]]
        if rubric_items
        else []
    )


def get_csv_fieldnames(feedback_keys: list) -> list[str]:
    """Extract field names from a run object."""
    # Add or remove fields based on your needs
    base_fields = [
        "id",
        "status",
        "inputs",
        "outputs",
        "start_time",
        "end_time",
        "reviewed_at",
        "extra",
    ]

    # Add rubric items as fields
    for key in feedback_keys:
        base_fields.append(f"feedback.{key}")

    return base_fields


def format_run_as_csv(
    run: dict, fieldnames: list[str], feedbacks: list[schemas.FeedbackSchema]
) -> str:
    """Format a run object as a CSV row."""
    row_data = []
    for field in fieldnames:
        if field.startswith("feedback."):
            key = field[len("feedback.") :]
            feedback_stat = run.get("feedback_stats", {}).get(key, {})
            feedback_score = feedback_stat.get("avg", None)
            feedback_values = feedback_stat.get("values", None)
            feedback_value = (
                next(iter(feedback_values.keys())) if feedback_values else None
            )
            feedback_comment = None
            if feedback_score is None and feedback_value is None:
                # Go through fetched feedbacks for the given key and run_id to see if there is a comment to include here
                feedback = next(
                    (
                        f
                        for f in feedbacks
                        if f.run_id == run["id"]
                        and f.key == key
                        and f.comment is not None
                        and f.comment != ""
                    ),
                    None,
                )
                if feedback:
                    feedback_comment = feedback.comment
            value = (
                feedback_value
                if feedback_value is not None
                else f"{feedback_score}"
                if feedback_score is not None
                else feedback_comment
                if feedback_comment is not None
                else ""
            )
        else:
            value = run.get(field, "")

        # Escape and quote the value if needed
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        elif isinstance(value, datetime):
            value = value.isoformat()
        if isinstance(value, str) and ("," in value or '"' in value or "\n" in value):
            value = '"' + value.replace('"', '""') + '"'
        row_data.append(str(value))

    return ",".join(row_data) + "\n"


async def get_runs_from_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    query_params: schemas.QueryParamsForAnnotationQueueRunSchema,
    already_fetched: list[schemas.RunSchemaWithAnnotationQueueInfo] | None = None,
) -> tuple[list[schemas.RunSchemaWithAnnotationQueueInfo], int]:
    """Get runs from a queue based on query params."""

    if not query_params.archived:
        rows = await db.fetch(
            f"""
            {_reserved_or_completed_run_sql}
            SELECT aqr.*, COALESCE(current_user_status.override_added_at, aqr.added_at) AS effective_added_at
            FROM annotation_queue_runs aqr
            {_check_for_current_user_status_sql}
            ORDER BY effective_added_at ASC, aqr.id ASC
            LIMIT $4 OFFSET $5;
            """,
            auth.identity_id,
            queue_id,
            auth.tenant_id,
            query_params.limit + 1 - (len(already_fetched) if already_fetched else 0),
            query_params.offset + (len(already_fetched) if already_fetched else 0),
        )
    else:
        rows = await db.fetch(
            """
            SELECT aqr.*, aqr.added_at AS effective_added_at
            FROM annotation_queue_runs_archive aqr
            JOIN annotation_queues aq ON aqr.queue_id = aq.id
            WHERE aqr.queue_id = $3
            AND aq.tenant_id = $4
            ORDER BY COALESCE(aqr.deleted_at, aqr.added_at) DESC, aqr.id ASC
            LIMIT $1 OFFSET $2;
            """,
            query_params.limit + 1 - (len(already_fetched) if already_fetched else 0),
            query_params.offset + (len(already_fetched) if already_fetched else 0),
            queue_id,
            auth.tenant_id,
        )
    all_start_times = [row["start_time"] for row in rows]
    earliest_start_time = (
        min(all_start_times) if all_start_times and all(all_start_times) else None
    )
    is_all_root = all([row["is_root"] for row in rows])
    is_none_root = all([row["is_root"] is False for row in rows])
    all_have_sessions = all([row["session_id"] for row in rows])
    sessions_agg = (
        list(set([row["session_id"] for row in rows if row["session_id"]]))
        if all_have_sessions
        else None
    )

    if not rows:
        return [], query_params.offset
    runs = await models.runs.fetch_ch.fetch_runs(
        auth,
        schemas.BodyParamsForRunSchema(
            id=[UUID(row["run_id"].hex) for row in rows],
            start_time=earliest_start_time,
            is_root=True if is_all_root else False if is_none_root else None,
            session=sessions_agg,
            select=[
                schemas.RunSelect.inputs,
                schemas.RunSelect.outputs,
                schemas.RunSelect.s3_urls,
                schemas.RunSelect.reference_dataset_id,
                schemas.RunSelect.reference_example_id,
                *(
                    [schemas.RunSelect.feedback_stats]
                    if query_params.include_stats
                    else []
                ),
            ],
        ),
        include_feedback=query_params.include_stats,
    )
    runs_by_id = {run["id"]: run for run in runs["runs"]}

    fetched_runs = [] if already_fetched is None else already_fetched
    runs_to_delete = []
    for row in rows:
        if run := runs_by_id.get(UUID(row["run_id"].hex)):
            fetched_runs.append(
                schemas.RunSchemaWithAnnotationQueueInfo(
                    **run,
                    queue_run_id=row["id"],
                    added_at=row["added_at"],
                    effective_added_at=row["effective_added_at"],
                    last_reviewed_time=row["last_reviewed_time"],
                )
            )
        else:
            runs_to_delete.append(row["run_id"])

    if runs_to_delete:
        await db.execute(
            """
            DELETE FROM annotation_queue_runs
            USING annotation_queues
            WHERE annotation_queue_runs.queue_id = annotation_queues.id
            AND annotation_queue_runs.run_id = ANY($1::uuid[])
            AND annotation_queue_runs.queue_id = $2
            AND annotation_queues.tenant_id = $3
            """,
            runs_to_delete,
            queue_id,
            auth.tenant_id,
        )
        return await get_runs_from_annotation_queue(
            db, auth, queue_id, query_params, fetched_runs
        )
    else:
        return fetched_runs[: query_params.limit], query_params.offset + len(
            fetched_runs
        )


async def get_annotation_queues_for_run(
    db: asyncpg.Connection,
    auth: AuthInfo,
    run_id: UUID,
) -> List[schemas.AnnotationQueueSchema]:
    """Get annotation queues for a given run."""
    rows = await db.fetch(
        """
        SELECT
            annotation_queues.*
        FROM annotation_queues
        JOIN annotation_queue_runs ON annotation_queues.id = annotation_queue_runs.queue_id
        WHERE annotation_queue_runs.run_id = $1
        AND annotation_queues.tenant_id = $2
    """,
        run_id,
        auth.tenant_id,
    )

    return [schemas.AnnotationQueueSchema(**row) for row in rows]


async def update_run_in_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    queue_run_id: UUID,
    updated_annotation_queue_run: schemas.AnnotationQueueRunUpdateSchema,
    session_id: Optional[UUID] = None,
    start_time: Optional[datetime] = None,
) -> None:
    """Update a run in a queue."""
    # First, check if the run belongs to the correct tenant
    await models.runs.fetch_ch.fetch_and_assert_run(
        auth, queue_run_id, session_id=session_id, start_time=start_time
    )

    # If the run belongs to the correct tenant, update it
    await db.fetchrow(
        """
        UPDATE annotation_queue_runs
        SET added_at = COALESCE($1, added_at), last_reviewed_time = COALESCE($2, last_reviewed_time)
        WHERE run_id = $3 AND queue_id = $4
        """,
        updated_annotation_queue_run.added_at,
        updated_annotation_queue_run.last_reviewed_time,
        queue_run_id,
        queue_id,
    )


async def delete_run_from_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    queue_run_id: UUID,
) -> None:
    """Update a run in a queue."""
    # If the run belongs to the correct tenant, archive it
    async with db.transaction():
        await db.fetchrow(
            """
            INSERT INTO annotation_queue_runs_archive
            SELECT aqr.* FROM annotation_queue_runs aqr
            JOIN annotation_queues aq ON aqr.queue_id = aq.id
            WHERE aqr.run_id = $1 AND aqr.queue_id = $2 AND aq.tenant_id = $3
            ON CONFLICT (run_id, queue_id) DO UPDATE
            SET
                added_at = EXCLUDED.added_at,
                last_reviewed_time = EXCLUDED.last_reviewed_time
            """,
            queue_run_id,
            queue_id,
            auth.tenant_id,
        )

        deleted_row = await db.fetch(
            """
            DELETE FROM annotation_queue_runs aqr
            USING annotation_queues aq
            WHERE aqr.queue_id = aq.id
                AND aqr.run_id = $1
                AND aqr.queue_id = $2
                AND aq.tenant_id = $3
            RETURNING aqr.*
            """,
            queue_run_id,
            queue_id,
            auth.tenant_id,
        )

        if not deleted_row:
            raise HTTPException(
                status_code=404,
                detail="Run not found",
            )


async def delete_runs_from_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
    request: schemas.AnnotationQueueBulkDeleteRunsRequest,
) -> None:
    """Update a run in a queue."""
    async with db.transaction():
        # First, insert into archive
        if request.run_ids:
            await db.fetch(
                """
                INSERT INTO annotation_queue_runs_archive
                SELECT aqr.*
                FROM annotation_queue_runs as aqr
                JOIN annotation_queues aq ON aqr.queue_id = aq.id
                WHERE aqr.run_id = ANY($1::uuid[])
                    AND aqr.queue_id = $2
                    AND aq.tenant_id = $3
                ON CONFLICT (run_id, queue_id) DO UPDATE
                SET
                    added_at = EXCLUDED.added_at,
                    last_reviewed_time = EXCLUDED.last_reviewed_time
                """,
                request.run_ids,
                queue_id,
                auth.tenant_id,
            )

            deleted = await db.fetch(
                """
                DELETE from annotation_queue_runs aqr
                USING annotation_queues aq
                WHERE aqr.queue_id = aq.id
                    AND aqr.run_id = ANY($1::uuid[])
                    AND aqr.queue_id = $2
                    AND aq.tenant_id = $3
                RETURNING aqr.*
                """,
                request.run_ids,
                queue_id,
                auth.tenant_id,
            )
            if len(deleted) != len(request.run_ids):
                raise HTTPException(
                    status_code=404,
                    detail=f"Some runs not found: {len(deleted)} deleted but {len(request.run_ids)} requested.",
                )
        else:
            annotation_queue = await db.fetchrow(
                """
                SELECT id FROM annotation_queues WHERE id = $1 AND tenant_id = $2
                """,
                queue_id,
                auth.tenant_id,
            )
            if not annotation_queue:
                raise HTTPException(
                    status_code=404,
                    detail="Annotation queue not found",
                )
            await db.fetch(
                """
                INSERT INTO annotation_queue_runs_archive
                SELECT aqr.*
                FROM annotation_queue_runs as aqr
                WHERE aqr.queue_id = $1
                    AND aqr.run_id NOT IN (SELECT UNNEST($2::uuid[]))
                ON CONFLICT (run_id, queue_id) DO UPDATE
                SET
                    added_at = EXCLUDED.added_at,
                    last_reviewed_time = EXCLUDED.last_reviewed_time
                """,
                queue_id,
                request.exclude_run_ids or [],
            )
            await db.execute(
                """
                DELETE from annotation_queue_runs aqr
                WHERE aqr.queue_id = $1
                    AND aqr.run_id NOT IN (SELECT UNNEST($2::uuid[]))
                """,
                queue_id,
                request.exclude_run_ids or [],
            )


async def get_total_size_from_annotation_queue(
    auth: AuthInfo,
    queue_id: UUID,
) -> int:
    """Get total number of uncompleted runs for an annotation queue."""

    async with database.asyncpg_conn() as db:
        # make sure the authed user is in this workspace
        row = await db.fetchval(
            """
            SELECT tenant_id
            FROM annotation_queues
            WHERE id = $1
            AND tenant_id = $2
            """,
            queue_id,
            auth.tenant_id,
        )

        if not row:
            raise HTTPException(
                status_code=404,
                detail="Annotation queue not found.",
            )

        count = await db.fetchval(
            """
            SELECT COUNT(*) as total_count
            FROM annotation_queue_runs aqr
            INNER JOIN annotation_queues aq ON aqr.queue_id = aq.id
            WHERE aqr.queue_id = $1
            """,
            queue_id,
        )

        return count


async def get_total_archived_size_from_annotation_queue(
    auth: AuthInfo,
    queue_id: UUID,
    start_time: datetime | None = None,
    end_time: datetime | None = None,
) -> int:
    """Get total number of completed runs for an annotation queue."""
    start_utc = (
        start_time.astimezone(timezone.utc).replace(tzinfo=None)
        if start_time and start_time.tzinfo
        else start_time
    )
    end_utc = (
        end_time.astimezone(timezone.utc).replace(tzinfo=None)
        if end_time and end_time.tzinfo
        else end_time
    )
    async with database.asyncpg_conn() as db:
        # make sure the authed user is in this workspace
        row = await db.fetchval(
            """
            SELECT tenant_id
            FROM annotation_queues
            WHERE id = $1
            AND tenant_id = $2
            """,
            queue_id,
            auth.tenant_id,
        )

        if not row:
            raise HTTPException(
                status_code=404,
                detail="Annotation queue not found.",
            )

        count = await db.fetchval(
            """
            SELECT COUNT(*) as total_count
            FROM annotation_queue_runs_archive aqr
            JOIN annotation_queues aq ON aqr.queue_id = aq.id
            WHERE aqr.queue_id = $1
            AND aq.tenant_id = $2
            AND COALESCE(aqr.deleted_at, aqr.added_at) > $3 AND COALESCE(aqr.deleted_at, aqr.added_at) < $4
            """,
            queue_id,
            auth.tenant_id,
            start_utc if start_utc else datetime.min,
            end_utc if end_utc else datetime.max,
        )

        return count


_reserved_or_completed_run_sql = """
WITH latest AS (
    SELECT iaqs.identity_id, iaqs.annotation_queue_run_id, max(iaqs.created_at) as created_at
    FROM identity_annotation_queue_run_status iaqs
    JOIN annotation_queue_runs aqr ON iaqs.annotation_queue_run_id = aqr.id
    WHERE aqr.queue_id = $2
    GROUP BY 2, 1
),
latest_statuses AS (
    SELECT iaqs.annotation_queue_run_id, iaqs.identity_id, iaqs.status, iaqs.created_at, iaqs.override_added_at
    FROM latest
    JOIN identity_annotation_queue_run_status iaqs
    ON latest.annotation_queue_run_id = iaqs.annotation_queue_run_id AND latest.created_at = iaqs.created_at AND latest.identity_id = iaqs.identity_id
),
reserved_or_completed_runs AS (
    SELECT ls.annotation_queue_run_id, COUNT(DISTINCT ls.identity_id) as reservation_count
    FROM latest_statuses ls
    JOIN annotation_queue_runs aqr ON ls.annotation_queue_run_id = aqr.id
    JOIN annotation_queues aq ON aqr.queue_id = aq.id
    WHERE (
        (ls.status = 'viewed' AND ls.created_at > NOW() - (aq.reservation_minutes || ' minutes')::INTERVAL AND ls.identity_id != $1)
        OR (ls.status = 'completed')
    )
    GROUP BY ls.annotation_queue_run_id
)
"""

_check_for_current_user_status_sql = """
INNER JOIN annotation_queues aq ON aq.id = aqr.queue_id
LEFT JOIN latest_statuses current_user_status
    ON aqr.id = current_user_status.annotation_queue_run_id
    AND current_user_status.identity_id = $1
LEFT JOIN reserved_or_completed_runs
    ON aqr.id = reserved_or_completed_runs.annotation_queue_run_id
WHERE aq.id = $2
  AND aq.tenant_id = $3
  AND (current_user_status.status IS NULL OR current_user_status.status <> 'completed')
  AND (
    aq.enable_reservations = false
    OR aq.num_reviewers_per_item IS NULL
    OR reserved_or_completed_runs.annotation_queue_run_id IS NULL
    OR COALESCE(reserved_or_completed_runs.reservation_count, 0) < aq.num_reviewers_per_item
  )
"""


async def get_size_from_annotation_queue(
    db: asyncpg.Connection,
    auth: AuthInfo,
    queue_id: UUID,
) -> int:
    """Get total size of an annotation queue for the current authed user, not including reserved runs."""
    count = await db.fetchval(
        f"""
        {_reserved_or_completed_run_sql}
        SELECT COUNT(*) as total_count
        FROM annotation_queue_runs aqr
        {_check_for_current_user_status_sql}
        """,
        auth.identity_id,
        queue_id,
        auth.tenant_id,
    )

    return count


async def create_identity_annotation_queue_run_status(
    auth: AuthInfo,
    annotation_queue_run_id: UUID,
    log_info: schemas.IdentityAnnotationQueueRunStatusCreateSchema,
) -> None:
    """Log the status of a run in an annotation queue."""
    async with database.asyncpg_pool() as db:
        # Check if the run belongs to an annotation queue owned by the current tenant
        async def get_row():
            return await db.fetchrow(
                """
                SELECT aqr.run_id as aqr_run_id, aq.id as aq_id
                FROM annotation_queue_runs aqr
                JOIN annotation_queues aq ON aq.id = aqr.queue_id
                WHERE aqr.id = $1 AND aq.tenant_id = $2
                """,
                annotation_queue_run_id,
                auth.tenant_id,
            )

        # Once again check if the user is allowed to reserve this run
        # This safeguards against too many reservations on the same run
        async def check_can_reserve():
            if log_info.status == "viewed":
                return await db.fetchval(
                    """
                    WITH user_status AS (
                        SELECT status
                        FROM identity_annotation_queue_run_status
                        WHERE annotation_queue_run_id = $2
                          AND identity_id = $1
                        ORDER BY created_at DESC
                        LIMIT 1
                    ),
                    reserved_or_completed_count AS (
                        SELECT COUNT(DISTINCT iaqs.identity_id) as count
                        FROM identity_annotation_queue_run_status iaqs
                        JOIN annotation_queue_runs aqr ON iaqs.annotation_queue_run_id = aqr.id
                        JOIN annotation_queues aq ON aqr.queue_id = aq.id
                        WHERE iaqs.annotation_queue_run_id = $2
                          AND ((iaqs.status = 'viewed' AND iaqs.created_at > NOW() - (aq.reservation_minutes || ' minutes')::INTERVAL AND iaqs.identity_id != $1)
                              OR iaqs.status = 'completed')
                    )

                    SELECT
                        CASE
                            WHEN (
                                (us.status IS NULL OR us.status <> 'completed')
                                AND (
                                    aq.enable_reservations = false
                                    OR aq.num_reviewers_per_item IS NULL
                                    OR rc.count < aq.num_reviewers_per_item
                                )
                            ) THEN true
                            ELSE false
                        END as is_available
                    FROM annotation_queue_runs aqr
                    JOIN annotation_queues aq ON aq.id = aqr.queue_id
                    LEFT JOIN user_status us ON true
                    CROSS JOIN reserved_or_completed_count rc
                    WHERE aqr.id = $2 AND aq.tenant_id = $3
                    """,
                    auth.identity_id,
                    annotation_queue_run_id,
                    auth.tenant_id,
                )

            return None

        async def get_most_recent_status():
            if log_info.override_added_at is None and log_info.status != "completed":
                return await db.fetchrow(
                    """
                    SELECT override_added_at
                    FROM identity_annotation_queue_run_status
                    WHERE identity_id = $1
                    AND annotation_queue_run_id = $2
                    ORDER BY created_at DESC
                    LIMIT 1
                    """,
                    auth.identity_id,
                    annotation_queue_run_id,
                )
            return None

        row, can_reserve, most_recent_status = await asyncio.gather(
            get_row(),
            check_can_reserve(),
            get_most_recent_status(),
        )

        if not row:
            raise HTTPException(
                status_code=404,
                detail="Run already reviewed",
            )

        if log_info.status == "viewed" and not can_reserve:
            raise HTTPException(
                status_code=409,
                detail="Run cannot be reserved by this user",
            )

        # If we're not overriding the added_at, check if the run has been overridden before
        # and propagate the override_added_at value
        override_added_at = log_info.override_added_at
        if override_added_at is None and most_recent_status:
            override_added_at = most_recent_status["override_added_at"]

        coros = []
        coros.append(
            db.fetchrow(
                """
            INSERT INTO identity_annotation_queue_run_status (identity_id, annotation_queue_run_id, status, override_added_at)
            VALUES ($1, $2, $3, $4)
            """,
                auth.identity_id,
                annotation_queue_run_id,
                log_info.status,
                override_added_at,
            )
        )

        # see if it's time to delete the annotation_queue_run from the queue
        if log_info.status == "completed":
            coros.append(
                _check_and_delete_annotation_queue_run(
                    auth,
                    row["aq_id"],
                    annotation_queue_run_id,
                    row["aqr_run_id"],
                )
            )

        try:
            await asyncio.gather(*coros)
        except asyncpg.ForeignKeyViolationError:
            await logger.aerror(
                f"Error logging annotation queue run status for identity id: {auth.identity_id}.",
                annotation_queue_run_id=annotation_queue_run_id,
                status=log_info.status,
                override_added_at=override_added_at,
            )
            raise HTTPException(
                status_code=404,
                detail="Run not found",
            )


async def _check_and_delete_annotation_queue_run(
    auth: AuthInfo,
    annotation_queue_id: UUID,
    annotation_queue_run_id: UUID,
    run_id: UUID,
) -> None:
    async with database.asyncpg_conn() as db:
        all_tenant_members, annotation_queue = await asyncio.gather(
            get_tenant_members(auth),
            db.fetchrow(
                """
            SELECT *
            FROM annotation_queues
            WHERE id = $1
            """,
                annotation_queue_id,
            ),
        )

        if not annotation_queue:
            raise HTTPException(
                status_code=404,
                detail="Annotation queue not found",
            )

        aq = schemas.AnnotationQueueSchema(**annotation_queue)

        # Check if we've hit the threshold
        # If threshold is null, check if we've hit the number of users in the workspace
        threshold = (
            aq.num_reviewers_per_item
            if aq.num_reviewers_per_item
            else len(all_tenant_members.members)
        )

        if threshold == 1:
            await delete_run_from_queue(db, auth, annotation_queue_id, run_id)
        else:
            # get the number of reviewers whose most recent status for this annotation queue run is 'completed'
            num_completed_reviewers = await db.fetchval(
                """
                SELECT COUNT(*)
                FROM (
                    SELECT DISTINCT ON (identity_id)
                        identity_id,
                        status
                    FROM identity_annotation_queue_run_status
                    WHERE annotation_queue_run_id = $1
                    ORDER BY identity_id, created_at DESC
                ) AS latest_statuses
                WHERE status = 'completed';
                """,
                annotation_queue_run_id,
            )

            if num_completed_reviewers >= threshold:
                # we've hit the threshold, delete the annotation_queue_run
                await delete_run_from_queue(
                    db,
                    auth,
                    annotation_queue_id,
                    run_id,
                )


async def _cleanup_queued_runs(auth_id: str, run_ids: list[str]):
    try:
        async with (
            redis.aredis_routed_pool(auth_id, redis.RedisOperation.READ) as aredis,
            aredis.pipeline() as pipe,
        ):
            # dual-write: write but we're not worrying about this, will shard after dual-write period is over
            pipe.srem(queued_runs_key(auth_id), *run_ids)
            await pipe.execute()
    except Exception as e:
        await logger.aerror(
            f"Error cleaning up queued runs {auth_id} {run_ids} {repr(e)}"
        )


async def create_playground_settings(
    db: asyncpg.Connection,
    auth: AuthInfo,
    settings: schemas.PlaygroundSettingsCreateRequest,
) -> schemas.PlaygroundSettingsResponse:
    """Create a new playground settings."""
    row = await db.fetchrow(
        """
        INSERT INTO playground_settings (id, name, description, tenant_id, settings, options)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
        """,
        uuid4(),
        settings.name,
        settings.description,
        auth.tenant_id,
        settings.settings,
        settings.options.model_dump() if settings.options else None,
    )

    ret_val = schemas.PlaygroundSettingsResponse(**row)

    return ret_val


async def list_playground_settings(
    db: asyncpg.Connection, auth: AuthInfo
) -> List[schemas.PlaygroundSettingsResponse]:
    """Get all playground settings for the current tenant id."""
    rows = await db.fetch(
        """
        SELECT *
        FROM playground_settings
        WHERE tenant_id = $1
        ORDER BY created_at ASC
        """,
        auth.tenant_id,
    )

    return [schemas.PlaygroundSettingsResponse(**row) for row in rows]


async def update_playground_settings(
    db: asyncpg.Connection,
    auth: AuthInfo,
    id: str,
    settings: schemas.PlaygroundSettingsUpdateRequest,
) -> schemas.PlaygroundSettingsResponse:
    """Update a playground settings."""

    row = await db.fetchrow(
        """
        UPDATE playground_settings
        SET name = COALESCE($1, name),
            description = COALESCE($2, description),
            settings = COALESCE($3, settings),
            options = COALESCE($4, options)
        WHERE id = $5 AND tenant_id = $6
        RETURNING *
        """,
        settings.name,
        settings.description,
        settings.settings,
        settings.options.model_dump() if settings.options else None,
        id,
        auth.tenant_id,
    )

    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Playground settings with id {id} does not exist or does not belong to the right tenant.",
        )

    return schemas.PlaygroundSettingsResponse(**row)


async def delete_playground_settings(
    db: asyncpg.Connection, auth: AuthInfo, id: str
) -> None:
    """Delete a saved playground setting for the current tenant id."""
    await db.execute(
        """
        DELETE FROM playground_settings
        WHERE tenant_id = $1 AND id = $2
        RETURNING *
        """,
        auth.tenant_id,
        id,
    )

    return None


async def get_filter_views(
    auth: AuthInfo, session_id: UUID, type: schemas.FilterViewType | None = None
) -> List[schemas.FilterView]:
    """Get all filter views for a session."""
    async with database.asyncpg_conn() as db:
        query = """
            SELECT fv.*
            FROM filter_views fv
            JOIN tracer_session ts ON fv.session_id = ts.id
            WHERE ts.tenant_id = $1 AND fv.session_id = $2
        """
        params: list[UUID | schemas.FilterViewType] = [auth.tenant_id, session_id]

        if type is not None:
            query += " AND fv.type = $3"
            params.append(type)

        rows = await db.fetch(query, *params)

        return [schemas.FilterView(**row) for row in rows]


async def get_filter_view(
    auth: AuthInfo, session_id: UUID, filter_view_id: UUID
) -> schemas.FilterView:
    """Get a filter view by id."""
    async with database.asyncpg_conn() as db:
        row = await db.fetchrow(
            """
            SELECT fv.*
            FROM filter_views fv
            JOIN tracer_session ts ON fv.session_id = ts.id
            WHERE ts.tenant_id = $1 AND fv.session_id = $2 AND fv.id = $3
            """,
            auth.tenant_id,
            session_id,
            filter_view_id,
        )

        auth.exists(row)

        return schemas.FilterView(**row)


async def create_filter_view(
    auth: AuthInfo, session_id: UUID, filter_view: schemas.FilterViewCreate
) -> schemas.FilterView:
    """Create a filter view."""
    if filter_view.filter_string:
        parse_as_filter_directive(filter_view.filter_string)

    if filter_view.trace_filter_string:
        parse_as_filter_directive(filter_view.trace_filter_string)

    if filter_view.tree_filter_string:
        parse_as_filter_directive(filter_view.tree_filter_string)

    async with database.asyncpg_conn() as db:
        row = await db.fetchrow(
            """
            INSERT INTO filter_views (
                session_id,
                filter_string,
                display_name,
                description,
                trace_filter_string,
                tree_filter_string,
                type
            )
            SELECT id, $2, $3, $4, $5, $6, $7
            FROM tracer_session
            WHERE tenant_id = $1 AND id = $8
            RETURNING *
            """,
            auth.tenant_id,
            filter_view.filter_string,
            filter_view.display_name,
            filter_view.description,
            filter_view.trace_filter_string,
            filter_view.tree_filter_string,
            filter_view.type,
            session_id,
        )

        if not row:
            raise HTTPException(
                status_code=404,
                detail="Session not found",
            )

        return schemas.FilterView(**row)


async def update_filter_view(
    auth: AuthInfo,
    session_id: UUID,
    filter_view_id: UUID,
    filter_view: schemas.FilterViewUpdate,
) -> schemas.FilterView:
    """Update a filter view."""
    if filter_view.filter_string:
        parse_as_filter_directive(filter_view.filter_string)

    if filter_view.trace_filter_string:
        parse_as_filter_directive(filter_view.trace_filter_string)

    if filter_view.tree_filter_string:
        parse_as_filter_directive(filter_view.tree_filter_string)

    async with database.asyncpg_conn() as db:
        row = await db.fetchrow(
            """
            UPDATE filter_views
            SET filter_string = COALESCE($1, filter_string),
                trace_filter_string = COALESCE($2, trace_filter_string),
                tree_filter_string = COALESCE($3, tree_filter_string),
                display_name = COALESCE($4, display_name),
                description = COALESCE($5, filter_views.description),
                type = COALESCE($6, type),
                updated_at = now()
            FROM tracer_session ts
            WHERE ts.id = filter_views.session_id
            AND ts.id = $7
            AND filter_views.id = $8
            AND ts.tenant_id = $9
            RETURNING filter_views.*
            """,
            filter_view.filter_string,
            filter_view.trace_filter_string,
            filter_view.tree_filter_string,
            filter_view.display_name,
            filter_view.description,
            filter_view.type,
            session_id,
            filter_view_id,
            auth.tenant_id,
        )

        auth.exists(row)

        return schemas.FilterView(**row)


async def delete_filter_view(
    auth: AuthInfo, session_id: UUID, filter_view_id: UUID
) -> None:
    """Delete a filter view."""
    async with database.asyncpg_conn() as db:
        row = await db.fetchrow(
            """
            DELETE FROM filter_views
            USING tracer_session ts
            WHERE ts.id = filter_views.session_id
            AND ts.id = $1
            AND filter_views.id = $2
            AND ts.tenant_id = $3
            RETURNING filter_views.id
            """,
            session_id,
            filter_view_id,
            auth.tenant_id,
        )

        auth.exists(row)

        return None


@redis_cache(60)
async def get_auth_for_tenant_cached(tenant_id: UUID) -> AuthInfo:
    from app.api.auth.verify import internal_auth_request

    return await internal_auth_request(tenant_id)


async def delete_runs(
    auth: AuthInfo,
    session_id: UUID,
    trace_ids: List[UUID],
) -> None:
    if not trace_ids:
        raise HTTPException(
            status_code=400, detail="Must provide at least one trace id."
        )
    if len(trace_ids) > settings.RUN_DELETE_MAX_RUNS:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete more than {settings.RUN_DELETE_MAX_RUNS} runs at once.",
        )

    async with database.asyncpg_conn() as db:
        session = await db.fetchrow(
            """
            SELECT id FROM tracer_session
            WHERE id = $1 AND tenant_id = $2
            """,
            session_id,
            auth.tenant_id,
        )
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Session not found.",
            )

    delete_runs_key = uuid5(
        auth.tenant_id,
        orjson.dumps(sorted(map(str, trace_ids))).decode("utf-8"),
    )
    inflight_key = f"delete_runs_job:{delete_runs_key}"

    async with redis.aredis_pool() as aredis:
        lock = aredis.lock(inflight_key, timeout=300, blocking=False)
        if not await lock.acquire():
            await logger.awarning(
                "Delete job already in flight, skipping enqueue",
            )
            return

        try:
            sorted_set_key = settings.RUN_DELETE_SORTED_SET_KEY
            now_ts = datetime.now(timezone.utc).timestamp()
            payload = {
                "tenant_id": str(auth.tenant_id),
                "session_id": str(session_id),
                "trace_ids": list(map(str, trace_ids)),
            }
            await aredis.zadd(
                sorted_set_key,
                {orjson.dumps(payload).decode(): now_ts},
            )
            await logger.ainfo(
                "Enqueued delete_runs batch in sorted set",
                sorted_set_key=sorted_set_key,
                session_id=session_id,
                trace_ids=payload["trace_ids"],
            )
        finally:
            try:
                await lock.release()
            except Exception as e:
                await logger.info(f"Error releasing delete lock {inflight_key}: {e}")


async def delete_runs_ch(
    tenant_ids: list[UUID], session_ids: list[UUID], trace_ids: list[UUID]
) -> None:
    """
    Delete everything that belongs to the given trace_ids.
    """

    params = {
        "tenant_ids": tenant_ids,
        "session_ids": session_ids,
        "trace_ids": trace_ids,
    }

    await clickhouse.multi_execute_single(
        # 1) delete the base runs + feedbacks
        clickhouse.ExecuteRequest(
            "delete_runs",
            """
            DELETE FROM runs
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND(id, is_root, start_time) IN (
                SELECT id, is_root, start_time
                FROM runs_trace_id
                WHERE tenant_id IN {tenant_ids}
                  AND session_id IN {session_ids}
                  AND trace_id IN {trace_ids}
              )
            """,
            params=params,
        ),
        clickhouse.ExecuteRequest(
            "delete_feedbacks",
            """
            DELETE FROM feedbacks
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND(run_id, is_root, start_time) IN (
                SELECT id, is_root, start_time
                FROM runs_trace_id
                WHERE tenant_id IN {tenant_ids}
                  AND session_id IN {session_ids}
                  AND trace_id IN {trace_ids}
              )
            """,
            params=params,
        ),
        # 4) delete any feedbacks_* materialized views
        *clickhouse.delete_materialized_view_queries(
            source_table="feedbacks",
            query="""
            DELETE FROM {table}
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND(run_id, is_root, start_time) IN (
                SELECT id, is_root, start_time
                FROM runs_trace_id
                WHERE tenant_id IN {tenant_ids}
                  AND session_id IN {session_ids}
                  AND trace_id IN {trace_ids}
              )
            """,
            params=params,
        ),
        # 5) delete runs_* MVs that key off the run's primary-key (id)
        *clickhouse.delete_materialized_view_queries(
            source_table="runs",
            query="""
            DELETE FROM {table}
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND(id, is_root, start_time) IN (
                SELECT id, is_root, start_time
                FROM runs_trace_id
                WHERE tenant_id IN {tenant_ids}
                  AND session_id IN {session_ids}
                  AND trace_id IN {trace_ids}
              )
            """,
            params=params,
            materialized_views=clickhouse.RUNS_MATERIALIZED_VIEWS_ID,
        ),
        # 6) delete runs_* MVs that key off the run_id column
        *clickhouse.delete_materialized_view_queries(
            source_table="runs",
            query="""
            DELETE FROM {table}
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND(run_id, is_root, start_time) IN (
                SELECT id, is_root, start_time
                FROM runs_trace_id
                WHERE tenant_id IN {tenant_ids}
                  AND session_id IN {session_ids}
                  AND trace_id IN {trace_ids}
              )
            """,
            params=params,
            materialized_views=clickhouse.RUNS_MATERIALIZED_VIEWS_RUN_ID,
        ),
        use_slow_client=True,
        max_attempts=2,
    )


async def delete_runs_trace_id_ch(
    tenant_ids: list[UUID],
    session_ids: list[UUID],
    trace_ids: list[UUID],
) -> None:
    params = {
        "tenant_ids": tenant_ids,
        "session_ids": session_ids,
        "trace_ids": trace_ids,
    }
    await clickhouse.multi_execute_single(
        clickhouse.ExecuteRequest(
            "delete_runs_trace_id",
            """
            DELETE FROM runs_trace_id
            WHERE tenant_id IN {tenant_ids}
              AND session_id IN {session_ids}
              AND trace_id IN {trace_ids}
            """,
            params=params,
        ),
        use_slow_client=True,
        max_attempts=2,
    )
