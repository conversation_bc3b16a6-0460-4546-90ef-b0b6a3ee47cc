[tool.poetry]
name = "langchainplus"
version = "0.10.70"
description = "Backend python for the LangSmith service."
authors = []
packages = [{ include = "app" }]

[tool.poetry.dependencies]
python = "^3.11"
sqlalchemy = { extras = ["asyncio"], version = "^2.0.15" }
psycopg = { extras = ["binary"], version = "^3.1.9" }
google-auth = "^2.19.1"
pyjwt = "^2.7.0"
uvicorn = { extras = ["standard"], version = "^0.27.0.post1" }
alembic = "^1.11.1"
httpx = ">=0.28.0,<0.29.0"
pandas = "^2.0.2"
uvloop = "^0.19.0"
google-cloud-sqlcommenter = "^2.0.0"
lark = "^1.1.5"
greenlet = "^2.0.2"
python-multipart = "^0.0.19"
types-google-cloud-ndb = "^*******"
tiktoken = "^0.8.0"
langchain = "^0.3.24"
click = "^8.1.3"
google-cloud-logging = "^3.6.0"
tenacity = "^8.2.2"
orjson = "^3.9.2"
starlette-context = "^0.3.6"
lc_config = { path = "../lc_config", develop = true }
lc_database = { path = "../lc_database", develop = true }
lc_logging = { path = "../lc_logging", develop = true }
lc_metrics = { path = "../lc_metrics", develop = true }
host-backend = { path = "../host-backend", develop = true }
supervisor = "^4.2.5"
anyio = "^4.6.0"
minio = "^7.1.17"
brotli = "^1.1.0"
jsonpatch = "^1.33"
sse-starlette = "^2.0.0"
starlette-cramjam = "^0.3.2"
python-dateutil = "^2.8.2"
types-python-dateutil = "^*********"
jsonschema-rs = "^0.18.0"
langchain-openai = "^0.3.14"
langsmith = "^0.1.142"
langchain-core = "^0.3.59"
email-validator = "^2.1.1"
pydantic = { extras = ["email"], version = "^2.7.0" }
httpcore = { git = "https://github.com/langchain-ai/httpcore" }
jsonschema = "^4.23.0"
google-cloud-secret-manager = "2.17.0"
elasticsearch = "^8.14.0"
google-cloud-artifact-registry = "^1.11.5"
aiocache = "^0.12.2"
msgspec = "^0.18.6"
fastparquet = "^2024.5.0"
s3fs = "^2024.9.0"
langgraph = "^0.2.39"
google-crc32c = "1.5.0"
zstandard = "^0.23.0"
uhashring = "2.3"
xxhash = "^3.5.0"
truststore = "^0.10.1"
ddtrace = "2.21.8"

[tool.poetry.group.test.dependencies]
pytest = "^7.3.1"
pytest-watcher = "^0.3.4"
httpx-sse = "^0.4.0"
pytest-xdist = "^3.6.1"
smith-playground = { path = "../smith-playground", develop = true }
pytest-mock = "^3.14.0"
deepdiff = "^8.0.1"
psutil = "^6.1.1"
syrupy = "^4.9.1"


[tool.poetry.group.lint.dependencies]
ruff = "^0.11.2"
types-toml = "^0.10.8.7"


[tool.poetry.group.typing.dependencies]
mypy = "^1.3.0"
pandas-stubs = "^2.0.2.230605"
types-requests = "<2.31.0.7"
types-redis = "^********"
types-python-dateutil = "^2.8.19.20240106"


[tool.poetry.group.dev.dependencies]
poethepoet = "^0.21.1"
poetry-bumpversion = "^0.3.2"
freezegun = "^1.4.0"
rich = "^13.7.1"
flaky = "^3.8.1"
pytest-repeat = "^0.9.3"
time-machine = "^2.16.0"
types-pycurl = "^7.45.3.20240421"
j2cli = "^0.3.10"

[tool.ruff]
lint.select = [
    "E", # pycodestyle
    "F", # pyflakes
    "I", # isort
]
lint.ignore = ["E501"]
target-version = "py311"

[tool.mypy]
ignore_missing_imports = "True"
follow_imports = "skip"
exclude = ["notebooks"]

# Auto-bump the version in __init__.py
[tool.poetry_bumpversion.file."app/__init__.py"]

[tool.poe.tasks]
_format-ruff-check = "ruff check --fix ."
_format-ruff-format = "ruff format ."
format = ["_format-ruff-check", "_format-ruff-format"]

_lint-mypy = "mypy ."
_lint-ruff-version="ruff version"
_lint-ruff-check = "ruff check ."
_lint-ruff-format = "ruff format --check ."
lint = ["_lint-ruff-version", "_lint-mypy", "_lint-ruff-check", "_lint-ruff-format"]

bump = "poetry version patch"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
