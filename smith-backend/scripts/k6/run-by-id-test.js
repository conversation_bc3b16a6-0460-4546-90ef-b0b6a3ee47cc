// langchainplus/smith-backend/scripts/k6/run-by-id-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';

// Custom metrics
const readLatency = new Trend('read_latency');
const successfulReads = new Counter('successful_reads');
const failedReads = new Counter('failed_reads');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const RUN_ID = __ENV.TEST_RUN_ID;
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// Scale testing configurations based on Brian's requirements
const SCALE_CONFIGS = {
  baseline: {
    vus: 10,     // Current baseline virtual users
    rps: 600     // Current baseline requests per second (600-750 RPS)
  },
  '4x': {
    vus: 40,     // 4x the baseline VUs for 20B traces target
    rps: 2800    // Target 2.8k RPS
  },
  '20x': {
    vus: 200,    // 20x the baseline VUs for 100B traces target
    rps: 15000   // Target 15k RPS
  },
};

// Get scale level from environment variables
const SCALE_LEVEL = __ENV.SCALE_LEVEL || 'baseline';
const CONFIG = SCALE_CONFIGS[SCALE_LEVEL];

if (!CONFIG) {
  throw new Error(`Invalid scale level: ${SCALE_LEVEL}. Must be one of: ${Object.keys(SCALE_CONFIGS).join(', ')}`);
}

export const options = {
  scenarios: {
    read_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.vus },    // Ramp up to configured VUs
        { duration: STEADY_DURATION, target: CONFIG.vus },     // Stay at configured VUs
        { duration: RAMP_DOWN_DURATION, target: 0 }           // Ramp down
      ],
    },
  },
  thresholds: {
    'read_latency': [
      { threshold: 'p(50)<1000', abortOnFail: false },  // 50th percentile < 1000ms
      { threshold: 'p(95)<2000', abortOnFail: false },  // 95th percentile < 2000ms
      { threshold: 'p(99)<5000', abortOnFail: false }   // 99th percentile < 5000ms
    ],
    'error_rate': [
      { threshold: 'rate<0.005', abortOnFail: false }  // 0.5% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<100', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ]
  },
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for request: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for request: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for request: ${response.request.url}`);
  }
}

export default function () {
  const startTime = new Date().getTime();
  const response = http.get(`${BASE_URL}/runs/${RUN_ID}`, {
    headers: {
      'x-api-key': API_KEY, 
      'Content-Type': 'application/json',
    },
    timeout: '10s', // Timeout after 10 seconds
  });

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  readLatency.add(duration);
  
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 5s': (r) => r.timings.duration < 5000, // Align with 5s target
  });

  if (success) {
    successfulReads.add(1);
  } else {
    failedReads.add(1);
    errorRate.add(1);
    categorizeError(response);
  }

  // Dynamic sleep based on configured RPS target
  const sleepTime = 1000 / CONFIG.rps;
  sleep(sleepTime / 1000); // Convert to seconds for k6
}

// Log test configuration on startup
console.log('Test Configuration:', {
  scaleLevel: SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: BASE_URL
});