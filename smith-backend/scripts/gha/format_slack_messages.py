import base64
import sys

GITHUB_TO_SLACK_MAP = {
    "nfcampos": "U04UTFBCB5M",
    "jakerachleff": "U0630EXND2T",
    "dqbd": "U05K3LY8AE7",
    "agola11": "U04N3CBJ4BV",
    "akira": "U067DCG2UBX",
    "langchain-infra": "U05SD6TR4TA",
    "efriis": "U05F5PY9JF8",
    "hinthornw": "U052SVAQ3QR",
    "samnoyes": "U05QQ0ZUPPF",
    "madams0013": "U06STFBUCAU",
    "eric-langchain": "U07109W7Z2T",
    "barberscott": "U069H5P9UEN",
    "bvs-langchain": "U06TB063NLB",
    "henryweng03": "U0789JSR4BF",
    "andrewnguonly": "U06T4EFBY86",
    "baskaryan": "U052SVAJV5X",
    "lc-arjun": "U07RJF7SMR9",
    "isahers1": "U0761KS489Z",
    "angus-langchain": "U07ULEA36S1",
    "jacoblee93": "U058T4T7W59",
    "davidx33": "U07PKGF198T",
    "suraj-langchain": "U086J2BDHNK",
    "tanushree-sharma": "U0846U3HABV",
    "xornivore": "U08GV8NNLKW",
    "hari-dhanushkodi": "U08HRUNVB3P",
    "phvash": "U08MZUQCG03",
    "sasubillis": "U08N2EM4Y15",
    "quentinbrosse": "U08QDCARVP1",
    "EugeneJinXin": "U08TGP9S8DN",
    "joaquin-borggio-lc": "U08TWV4S3S8",
    "romain-priour-lc": "U08TWV7R8JG",
}

release_notes_base64 = sys.argv[1]
environment = sys.argv[2]
if environment not in ["staging", "production"]:
    raise ValueError(f"Invalid environment: {environment}")
if environment == "staging":
    url = "https://beta.smith.langchain.com"
else:
    url = "https://smith.langchain.com"
release_notes = base64.b64decode(release_notes_base64).decode("utf-8")

# Jank but filter out semantic versioning changes:
lines = release_notes.split("\n")
filtered_lines = []
skip_next_lines = False

for line in lines:
    if "ci: Update Semantic Version to" in line:
        continue
    filtered_lines.append(line)
release_notes = "\n".join(filtered_lines)

test_description = (
    "\nIf you are tagged, please test your changes." if environment == "staging" else ""
)
release_notes = (
    f"New {environment} release out! Here are the changes:\n"
    + release_notes
    + "\n"
    + url
    + test_description
    + "\n Please note that it might take a few minutes for the changes to reflect on the website!!!"
)


# Add number of users who made changes
number_of_users_who_made_changes = 0
for github_user in GITHUB_TO_SLACK_MAP.keys():
    if f"@{github_user}" in release_notes:
        number_of_users_who_made_changes += 1
release_notes = (
    release_notes
    + "\n\n"
    + f"Number of users who made changes: {number_of_users_who_made_changes}"
)

for github_user, slack_id in GITHUB_TO_SLACK_MAP.items():
    release_notes = release_notes.replace(f"@{github_user}", f"<@{slack_id}>")

print(release_notes)
